import { CheckIcon, XMarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import React, { useState, useEffect } from 'react';

import { cn } from '../../utils/helpers';

// Tipuri pentru componentele Progress
export type ProgressSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type ProgressVariant = 'default' | 'success' | 'warning' | 'error' | 'info' | 'gradient';
export type StepOrientation = 'horizontal' | 'vertical';
export type StepStatus = 'completed' | 'current' | 'pending';
export type StatusType = 'loading' | 'success' | 'error' | 'warning';

export interface ProgressProps {
  value?: number;
  max?: number;
  size?: ProgressSize;
  variant?: ProgressVariant;
  showLabel?: boolean;
  showPercentage?: boolean;
  label?: string;
  className?: string;
  trackClassName?: string;
  fillClassName?: string;
  animated?: boolean;
  striped?: boolean;
  indeterminate?: boolean;
}

export interface CircularProgressProps {
  value?: number;
  max?: number;
  size?: ProgressSize;
  variant?: Exclude<ProgressVariant, 'gradient'>;
  showLabel?: boolean;
  showPercentage?: boolean;
  label?: string;
  strokeWidth?: number;
  className?: string;
  indeterminate?: boolean;
}

export interface Step {
  id?: string | number;
  label?: string;
  description?: string;
}

export interface StepProgressProps {
  steps?: Step[];
  currentStep?: number;
  variant?: 'default' | 'success';
  size?: Exclude<ProgressSize, 'xs' | 'xl'>;
  orientation?: StepOrientation;
  showLabels?: boolean;
  showNumbers?: boolean;
  className?: string;
}

export interface MultiProgressItem {
  id?: string | number;
  label: string;
  value: number;
  color?: string;
}

export interface MultiProgressProps {
  items?: MultiProgressItem[];
  max?: number;
  size?: ProgressSize;
  showLabels?: boolean;
  showPercentages?: boolean;
  className?: string;
}

export interface LoadingProgressProps {
  variant?: ProgressVariant;
  size?: ProgressSize;
  message?: string;
  showMessage?: boolean;
  className?: string;
}

export interface StatusProgressProps {
  status?: StatusType;
  value?: number;
  max?: number;
  size?: ProgressSize;
  message?: string;
  showIcon?: boolean;
  className?: string;
}

interface SizeConfig {
  size: number;
  fontSize: string;
}

interface VariantConfig {
  track: string;
  fill: string;
}

interface StepVariantConfig {
  completed: string;
  current: string;
  pending: string;
  line: string;
  pendingLine: string;
}

interface StatusConfig {
  variant: ProgressVariant;
  icon: React.ReactNode;
  message: string;
}

/**
 * Componenta Progress principală
 */
const Progress: React.FC<ProgressProps> = ({
  value = 0,
  max = 100,
  size = 'md',
  variant = 'default',
  showLabel = false,
  showPercentage = false,
  label,
  className = '',
  trackClassName = '',
  fillClassName = '',
  animated = false,
  striped = false,
  indeterminate = false,
  ...rest
}) => {
  // Normalizează valoarea între 0 și max
  const normalizedValue = Math.max(0, Math.min(value, max));
  const percentage = max > 0 ? (normalizedValue / max) * 100 : 0;

  const sizeClasses: Record<ProgressSize, string> = {
    xs: 'h-1',
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
    xl: 'h-6',
  };

  const variantClasses: Record<ProgressVariant, VariantConfig> = {
    default: {
      track: 'bg-gray-200',
      fill: 'bg-primary-600',
    },
    success: {
      track: 'bg-gray-200',
      fill: 'bg-green-600',
    },
    warning: {
      track: 'bg-gray-200',
      fill: 'bg-yellow-600',
    },
    error: {
      track: 'bg-gray-200',
      fill: 'bg-red-600',
    },
    info: {
      track: 'bg-gray-200',
      fill: 'bg-blue-600',
    },
    gradient: {
      track: 'bg-gray-200',
      fill: 'bg-gradient-to-r from-primary-500 to-primary-700',
    },
  };

  const animationClasses: Record<string, string> = {
    animated: 'transition-all duration-300 ease-out',
    striped: 'bg-stripes',
    indeterminate: 'animate-pulse',
  };

  return (
    <div className={cn('w-full', className)} {...rest}>
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          {showPercentage && (
            <span className="text-sm text-gray-500">{Math.round(percentage)}%</span>
          )}
        </div>
      )}

      <div
        className={cn(
          'w-full rounded-full overflow-hidden',
          sizeClasses[size],
          variantClasses[variant].track,
          trackClassName,
        )}
        role="progressbar"
        aria-valuenow={normalizedValue}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label}
      >
        <div
          className={cn(
            'h-full rounded-full transition-all duration-300 ease-out',
            variantClasses[variant].fill,
            animated && animationClasses['animated'],
            striped && animationClasses['striped'],
            indeterminate && animationClasses['indeterminate'],
            fillClassName,
          )}
          style={{
            width: indeterminate ? '100%' : `${percentage}%`,
            animation: indeterminate ? 'indeterminate 2s infinite linear' : undefined,
          }}
        />
      </div>
    </div>
  );
};

/**
 * Progress circular
 */
export const CircularProgress: React.FC<CircularProgressProps> = ({
  value = 0,
  max = 100,
  size = 'md',
  variant = 'default',
  showLabel = false,
  showPercentage = false,
  label,
  strokeWidth = 8,
  className = '',
  indeterminate = false,
  ...rest
}) => {
  const normalizedValue = Math.max(0, Math.min(value, max));
  const percentage = max > 0 ? (normalizedValue / max) * 100 : 0;

  const sizeClasses: Record<ProgressSize, SizeConfig> = {
    xs: { size: 32, fontSize: 'text-xs' },
    sm: { size: 40, fontSize: 'text-sm' },
    md: { size: 56, fontSize: 'text-base' },
    lg: { size: 72, fontSize: 'text-lg' },
    xl: { size: 96, fontSize: 'text-xl' },
  };

  const { size: circleSize, fontSize } = sizeClasses[size];
  const radius = (circleSize - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = indeterminate ? 0 : circumference - (percentage / 100) * circumference;

  const variantClasses: Record<Exclude<ProgressVariant, 'gradient'>, string> = {
    default: 'stroke-primary-600',
    success: 'stroke-green-600',
    warning: 'stroke-yellow-600',
    error: 'stroke-red-600',
    info: 'stroke-blue-600',
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)} {...rest}>
      <svg width={circleSize} height={circleSize} className="transform -rotate-90">
        {/* Track */}
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        {/* Progress */}
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            'transition-all duration-300 ease-out',
            variantClasses[variant],
            indeterminate && 'animate-spin',
          )}
          style={{
            animation: indeterminate ? 'spin 2s linear infinite' : undefined,
          }}
        />
      </svg>

      {(showLabel || showPercentage || label) && (
        <div className={cn('absolute inset-0 flex flex-col items-center justify-center', fontSize)}>
          {label && <span className="font-medium text-gray-700 text-center">{label}</span>}
          {showPercentage && <span className="text-gray-500">{Math.round(percentage)}%</span>}
        </div>
      )}
    </div>
  );
};

/**
 * Progress cu pași
 */
export const StepProgress: React.FC<StepProgressProps> = ({
  steps = [],
  currentStep = 0,
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  showLabels = true,
  showNumbers = true,
  className = '',
  ...rest
}) => {
  const sizeClasses: Record<
    Exclude<ProgressSize, 'xs' | 'xl'>,
    { circle: string; line: string; text: string }
  > = {
    sm: {
      circle: 'w-6 h-6 text-xs',
      line: orientation === 'horizontal' ? 'h-0.5' : 'w-0.5',
      text: 'text-xs',
    },
    md: {
      circle: 'w-8 h-8 text-sm',
      line: orientation === 'horizontal' ? 'h-1' : 'w-1',
      text: 'text-sm',
    },
    lg: {
      circle: 'w-10 h-10 text-base',
      line: orientation === 'horizontal' ? 'h-1.5' : 'w-1.5',
      text: 'text-base',
    },
  };

  const variantClasses: Record<'default' | 'success', StepVariantConfig> = {
    default: {
      completed: 'bg-primary-600 text-white border-primary-600',
      current: 'bg-primary-100 text-primary-600 border-primary-600',
      pending: 'bg-gray-100 text-gray-400 border-gray-300',
      line: 'bg-primary-600',
      pendingLine: 'bg-gray-300',
    },
    success: {
      completed: 'bg-green-600 text-white border-green-600',
      current: 'bg-green-100 text-green-600 border-green-600',
      pending: 'bg-gray-100 text-gray-400 border-gray-300',
      line: 'bg-green-600',
      pendingLine: 'bg-gray-300',
    },
  };

  const getStepStatus = (index: number): StepStatus => {
    if (index < currentStep) return 'completed';
    if (index === currentStep) return 'current';
    return 'pending';
  };

  const StepCircle: React.FC<{ step: Step; index: number; status: StepStatus }> = ({
    step,
    index,
    status,
  }) => (
    <div className="relative flex items-center justify-center">
      <div
        className={cn(
          'rounded-full border-2 flex items-center justify-center font-medium transition-all duration-200',
          sizeClasses[size].circle,
          variantClasses[variant][status],
        )}
      >
        {status === 'completed' ? (
          <CheckIcon className="w-4 h-4" />
        ) : showNumbers ? (
          index + 1
        ) : null}
      </div>

      {showLabels && step.label && (
        <div
          className={cn(
            'absolute top-full mt-2 text-center font-medium',
            orientation === 'horizontal' ? 'whitespace-nowrap' : 'w-20',
            sizeClasses[size].text,
            status === 'current' ? 'text-primary-600' : 'text-gray-600',
          )}
        >
          {step.label}
        </div>
      )}

      {showLabels && step.description && (
        <div
          className={cn(
            'absolute top-full mt-8 text-center text-gray-500',
            orientation === 'horizontal' ? 'whitespace-nowrap' : 'w-20',
            'text-xs',
          )}
        >
          {step.description}
        </div>
      )}
    </div>
  );

  const StepLine: React.FC<{ isCompleted: boolean }> = ({ isCompleted }) => (
    <div
      className={cn(
        'flex-1 transition-all duration-300',
        orientation === 'horizontal' ? 'mx-4' : 'my-4 mx-auto',
        sizeClasses[size].line,
        isCompleted ? variantClasses[variant].line : variantClasses[variant].pendingLine,
      )}
    />
  );

  return (
    <div
      className={cn(
        'flex items-center',
        orientation === 'vertical' ? 'flex-col' : 'flex-row',
        className,
      )}
      {...rest}
    >
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        const isLast = index === steps.length - 1;

        return (
          <React.Fragment key={step.id || index}>
            <StepCircle step={step} index={index} status={status} />
            {!isLast && <StepLine isCompleted={index < currentStep} />}
          </React.Fragment>
        );
      })}
    </div>
  );
};

/**
 * Progress cu multiple bare
 */
export const MultiProgress: React.FC<MultiProgressProps> = ({
  items = [],
  max = 100,
  size = 'md',
  showLabels = true,
  showPercentages = true,
  className = '',
  ...rest
}) => {
  const total = items.reduce((sum, item) => sum + (item.value || 0), 0);
  const normalizedMax = Math.max(max, total);

  const sizeClasses: Record<ProgressSize, string> = {
    xs: 'h-1',
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
    xl: 'h-6',
  };

  const defaultColors: string[] = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-gray-500',
  ];

  return (
    <div className={cn('w-full', className)} {...rest}>
      {showLabels && (
        <div className="flex flex-wrap gap-4 mb-3">
          {items.map((item, index) => {
            const percentage = normalizedMax > 0 ? (item.value / normalizedMax) * 100 : 0;
            const colorClass = item.color || defaultColors[index % defaultColors.length];

            return (
              <div key={item.id || index} className="flex items-center gap-2">
                <div className={cn('w-3 h-3 rounded-full', colorClass)} />
                <span className="text-sm text-gray-700">
                  {item.label}
                  {showPercentages && (
                    <span className="text-gray-500 ml-1">({Math.round(percentage)}%)</span>
                  )}
                </span>
              </div>
            );
          })}
        </div>
      )}

      <div
        className={cn('w-full bg-gray-200 rounded-full overflow-hidden flex', sizeClasses[size])}
      >
        {items.map((item, index) => {
          const percentage = normalizedMax > 0 ? (item.value / normalizedMax) * 100 : 0;
          const colorClass = item.color || defaultColors[index % defaultColors.length];

          return (
            <div
              key={item.id || index}
              className={cn('h-full transition-all duration-300 ease-out', colorClass)}
              style={{ width: `${percentage}%` }}
              title={`${item.label}: ${item.value} (${Math.round(percentage)}%)`}
            />
          );
        })}
      </div>
    </div>
  );
};

/**
 * Progress cu animație de încărcare
 */
export const LoadingProgress: React.FC<LoadingProgressProps> = ({
  variant = 'default',
  size = 'md',
  message = 'Se încarcă...',
  showMessage = true,
  className = '',
  ...rest
}) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev: number) => {
        if (prev >= 100) {
          return 0;
        }
        return prev + Math.random() * 10;
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={cn('w-full', className)} {...rest}>
      {showMessage && (
        <div className="text-center mb-4">
          <span className="text-sm text-gray-600">{message}</span>
        </div>
      )}

      <Progress value={progress} variant={variant} size={size} animated striped />
    </div>
  );
};

/**
 * Progress cu status
 */
export const StatusProgress: React.FC<StatusProgressProps> = ({
  status = 'loading',
  value = 0,
  max = 100,
  size = 'md',
  message,
  showIcon = true,
  className = '',
  ...rest
}) => {
  const statusConfig: Record<StatusType, StatusConfig> = {
    loading: {
      variant: 'default' as ProgressVariant,
      icon: null,
      message: message || 'Se încarcă...',
    },
    success: {
      variant: 'success' as ProgressVariant,
      icon: <CheckIcon className="w-5 h-5" />,
      message: message || 'Finalizat cu succes',
    },
    error: {
      variant: 'error' as ProgressVariant,
      icon: <XMarkIcon className="w-5 h-5" />,
      message: message || 'A apărut o eroare',
    },
    warning: {
      variant: 'warning' as ProgressVariant,
      icon: <ExclamationTriangleIcon className="w-5 h-5" />,
      message: message || 'Atenție',
    },
  };

  const config = statusConfig[status];

  return (
    <div className={cn('w-full', className)} {...rest}>
      <div className="flex items-center gap-3 mb-3">
        {showIcon && config.icon && (
          <span
            className={cn(
              status === 'success' && 'text-green-600',
              status === 'error' && 'text-red-600',
              status === 'warning' && 'text-yellow-600',
              status === 'loading' && 'text-primary-600',
            )}
          >
            {config.icon}
          </span>
        )}
        <span className="text-sm font-medium text-gray-700">{config.message}</span>
      </div>

      <Progress
        value={status === 'loading' ? value : status === 'success' ? 100 : value}
        max={max}
        variant={config.variant}
        size={size}
        animated={status === 'loading'}
        indeterminate={status === 'loading' && value === 0}
      />
    </div>
  );
};

export default Progress;
