import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Link, useNavigate } from 'react-router-dom';
import { z } from 'zod';

import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Logo from '../../components/ui/Logo';
import { useAuthStore } from '../../store/authStore';

// Schema de validare pentru formularul de login
const loginSchema = z.object({
  email: z.string().min(1, 'Email-ul este obligatoriu').email('Email-ul nu este valid'),
  password: z
    .string()
    .min(1, 'Parola este obligatorie')
    .min(6, 'Parola trebuie să aibă cel puțin 6 caractere'),
});

// Tipuri pentru formularul de login
type LoginFormData = z.infer<typeof loginSchema>;

const Login: React.FC = () => {
  const { login, isLoading } = useAuthStore();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    // Precompletează câmpurile cu credențialele de admin pentru mediul de dezvoltare
    defaultValues: {
      email: '<EMAIL>',
      password: 'admin123',
    },
  });

  const onSubmit = async (data: LoginFormData): Promise<void> => {
    try {
      const result = await login(data);
      if (result.success) {
        // Toast-ul de succes este deja afișat în store
        // Obține utilizatorul din store pentru a verifica rolul
        const { user } = useAuthStore.getState();

        // Redirecționează în funcție de rolul utilizatorului
        if (user?.role === 'admin') {
          navigate('/app/admin/dashboard');
        } else {
          navigate('/app/dashboard');
        }
      } else {
        toast.error(result.message || 'Eroare la autentificare');
      }
    } catch (error: unknown) {
      toast.error((error as any).message || 'Eroare la autentificare');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col">
      {/* Header cu logo și buton de întoarcere */}
      <div className="flex items-center justify-between p-6">
        <Link
          to="/"
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors group"
        >
          <ArrowLeftIcon className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
          <span className="font-medium">Înapoi la pagina principală</span>
        </Link>
        <Logo size="md" to="/" animated />
      </div>

      {/* Conținutul principal */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {/* Card principal */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="mb-6">
                <Logo size="lg" showText={false} className="justify-center" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Bun venit înapoi!</h2>
              <p className="text-gray-600">Conectează-te pentru a-ți accesa contul</p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-5">
                <div>
                  <Input
                    {...register('email')}
                    type="email"
                    label="Adresa de email"
                    placeholder="<EMAIL>"
                    error={errors.email?.message}
                    autoComplete="email"
                    className="rounded-xl"
                  />
                </div>

                <div>
                  <Input
                    {...register('password')}
                    type="password"
                    label="Parola"
                    placeholder="Introdu parola"
                    error={errors.password?.message}
                    autoComplete="current-password"
                    className="rounded-xl"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                    Ține-mă minte
                  </label>
                </div>

                <div className="text-sm">
                  <Link
                    to="/forgot-password"
                    className="font-medium text-primary-600 hover:text-primary-700 transition-colors"
                  >
                    Ai uitat parola?
                  </Link>
                </div>
              </div>

              <div className="space-y-4">
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? 'Se conectează...' : 'Conectează-te'}
                </Button>

                {/* Separator */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500">sau</span>
                  </div>
                </div>

                {/* Link către înregistrare */}
                <div className="text-center">
                  <p className="text-gray-600">
                    Nu ai cont încă?{' '}
                    <Link
                      to="/register"
                      className="font-semibold text-primary-600 hover:text-primary-700 transition-colors"
                    >
                      Creează un cont nou
                    </Link>
                  </p>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
