{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "back": "Back"}, "navigation": {"dashboard": "Dashboard", "expenses": "Expenses", "categories": "Categories", "reports": "Reports", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Successfully logged in!", "logoutSuccess": "You have been logged out successfully!", "loginError": "Login error", "registerSuccess": "Account created successfully!"}, "settings": {"title": "Settings", "subtitle": "Customize your app experience", "general": {"title": "General Settings", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "theme": "Theme", "dateFormat": "Date Format", "themes": {"light": "Light", "dark": "Dark", "auto": "Auto"}}, "notifications": {"title": "Notifications", "email": {"title": "Email Notifications", "description": "Receive notifications via email"}, "push": {"title": "Push Notifications", "description": "Receive notifications in browser"}, "weeklyReport": {"title": "Weekly Reports", "description": "Receive a weekly summary of your activity"}, "budgetAlerts": {"title": "Budget Alerts", "description": "Receive alerts when you exceed your budget"}}, "privacy": {"title": "Privacy", "dataSharing": {"title": "Data Sharing", "description": "Allow data sharing for improvements"}, "analytics": {"title": "Analytics", "description": "Allow usage data collection"}, "marketing": {"title": "Marketing", "description": "Receive marketing communications"}}, "export": {"title": "Export Data", "description": "Download a copy of all your data in JSON format", "button": "Export Data", "allData": "Export All Data", "success": "Data exported successfully!", "error": "Error exporting data"}, "dangerZone": {"title": "Danger Zone", "logout": {"title": "Logout", "description": "Sign out of your account", "button": "Logout", "confirmTitle": "Confirm <PERSON>ut", "confirmMessage": "Are you sure you want to logout?", "confirmButton": "Logout"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently delete your account and all data", "button": "Delete Account", "confirmTitle": "Confirm Account Deletion", "warningTitle": "Warning! This action is irreversible.", "warningMessage": "All your data will be permanently deleted and cannot be recovered.", "confirmInstructions": "To confirm, type DELETE in the field below:", "confirmPlaceholder": "Type DELETE to confirm", "confirmButton": "Delete Account", "success": "Account deleted successfully!", "error": "Error deleting account"}}, "saveButton": "Save Settings", "saveSuccess": "Setting<PERSON> saved successfully!", "saveError": "Error saving settings"}, "currencies": {"RON": "RON - Romanian Leu", "EUR": "EUR - Euro", "USD": "USD - US Dollar", "GBP": "GBP - British Pound"}, "languages": {"ro": "Română", "en": "English", "fr": "Français", "de": "De<PERSON>ch"}, "landing": {"header": {"features": "Features", "testimonials": "Testimonials", "pricing": "Pricing", "login": "<PERSON><PERSON>", "register": "Register", "startFree": "Start Free"}, "hero": {"title": {"part1": "Take Control of Your", "part2": "Finances with Ease"}, "subtitle": "FinanceFlow is the modern app that helps you track expenses, analyze financial trends, and achieve your savings goals.", "cta": {"primary": "Start Free", "secondary": "View Demo"}, "startFree": "Start Free", "viewDemo": "View Demo", "disclaimer": "No credit card required • 2-minute setup • Cancel anytime", "features": "No credit card required • 2-minute setup • Cancel anytime"}, "features": {"title": "Powerful Features", "subtitle": "All the tools you need to manage your finances in one application.", "items": {"analytics": {"title": "Advanced Analytics", "description": "Visualize spending trends with interactive charts and detailed reports."}, "categories": {"title": "Category Management", "description": "Organize expenses into customizable categories for better control."}, "security": {"title": "Maximum Security", "description": "Your data is protected with advanced encryption and secure authentication."}, "mobile": {"title": "Mobile Access", "description": "The app is optimized for all devices - desktop, tablet, and mobile."}, "realtime": {"title": "Real Time", "description": "Track expenses in real time and receive notifications for your budget."}, "sharing": {"title": "Easy Sharing", "description": "Share reports with family or accountant for collaborative management."}}}, "stats": {"activeUsers": {"number": "10,000+", "label": "Active Users"}, "expensesTracked": {"number": "1M+", "label": "Expenses Tracked"}, "averageSavings": {"number": "25%", "label": "Average Savings"}, "uptime": {"number": "99.9%", "label": "Uptime"}}, "testimonials": {"title": "What Users Say", "subtitle": "Thousands of people already manage their finances with FinanceFlow", "items": {"maria": {"name": "<PERSON>", "role": "Entrepreneur", "content": "FinanceFlow helped me reduce my expenses by 30% in just 3 months. The interface is intuitive and the reports are very useful."}, "alexandru": {"name": "<PERSON><PERSON><PERSON>", "role": "IT Manager", "content": "The best financial tracking app I've ever used. Data security and advanced features are impressive."}, "elena": {"name": "<PERSON>", "role": "Freelancer", "content": "Perfect for freelancers! I can track all business expenses and generate reports for accounting."}}}, "pricing": {"title": "Simple and Transparent Plans", "subtitle": "Choose the plan that fits your needs", "popular": "Most popular", "perMonth": "per month", "plans": {"free": {"name": "Free", "period": "per month", "features": {"transactions": "Up to 50 transactions/month", "categories": "3 custom categories", "reports": "Basic reports", "support": "Email support"}}, "pro": {"name": "Pro", "period": "per month", "description": "Most popular", "features": {"transactions": "Unlimited transactions", "categories": "Unlimited categories", "reports": "Advanced reports", "export": "Excel/PDF export", "support": "Priority support", "backup": "Automatic backup"}}, "business": {"name": "Business", "period": "per month", "description": "For teams and companies", "features": {"allPro": "All Pro features", "multipleAccounts": "Multiple accounts", "apiAccess": "API access", "integrations": "Advanced integrations", "manager": "Dedicated manager", "sla": "99.9% SLA"}}}, "currency": "USD", "buttons": {"free": "Start Free", "choose": "Choose <PERSON>"}, "choosePlan": "Choose <PERSON>"}, "cta": {"title": "Ready to Transform Your Finances?", "subtitle": "Join thousands of users who have improved their financial situation with FinanceFlow.", "button": "Start Free Now", "features": "2-minute setup • No obligations • Cancel anytime", "disclaimer": "2-minute setup • No obligations • Cancel anytime"}, "footer": {"description": "The modern app for personal finance management.", "product": {"title": "Product", "features": "Features", "pricing": "Pricing", "api": "API", "integrations": "Integrations"}, "support": {"title": "Support", "documentation": "Documentation", "guides": "Guides", "contact": "Contact", "status": "Status"}, "legal": {"title": "Legal", "terms": "Terms", "privacy": "Privacy", "cookies": "Cookies", "gdpr": "GDPR"}, "copyright": "© 2024 FinanceFlow. All rights reserved."}}, "legal": {"terms": {"title": "Terms and Conditions", "subtitle": "Terms and conditions for using the FinanceFlow service", "lastUpdated": "Last updated: December 15, 2024", "sections": {"acceptance": {"title": "1. Acceptance of Terms", "content": "By accessing and using the FinanceFlow service, you agree to be bound by these terms and conditions. If you do not agree with any of these terms, please do not use our service."}, "service": {"title": "2. Service Description", "content": "FinanceFlow is a personal finance management application that allows you to track expenses, analyze financial trends, and manage your budget. The service is provided 'as is' and may be modified or discontinued at any time."}, "account": {"title": "3. User Account", "content": "To use certain features of the service, you must create an account. You are responsible for maintaining the confidentiality of your password and for all activities that occur under your account."}, "data": {"title": "4. User Data", "content": "You retain rights to the data you enter into the service. By using the service, you grant us a limited license to process this data for the purpose of providing the service."}, "prohibited": {"title": "5. Prohibited Uses", "content": "You may not use the service for illegal activities, to violate the rights of others, or to compromise system security. We reserve the right to suspend or close accounts that violate these terms."}, "liability": {"title": "6. Limitation of Liability", "content": "FinanceFlow will not be liable for direct, indirect, incidental, or consequential damages resulting from the use or inability to use the service."}, "changes": {"title": "7. Changes to Terms", "content": "We reserve the right to modify these terms at any time. Changes will be communicated via email or in-app notification at least 30 days before taking effect."}, "termination": {"title": "8. Service Termination", "content": "You may terminate use of the service at any time by deleting your account. We reserve the right to suspend or close accounts that violate these terms."}, "contact": {"title": "9. <PERSON>", "content": "For questions about these terms, you can contact <NAME_EMAIL> or through the contact form in the application."}}}, "privacy": {"title": "Privacy Policy", "subtitle": "How we collect, use, and protect your personal information", "lastUpdated": "Last updated: December 15, 2024", "sections": {"introduction": {"title": "1. Introduction", "content": "This privacy policy describes how FinanceFlow collects, uses, and protects your personal information when you use our service."}, "collection": {"title": "2. Information We Collect", "content": "We collect information you provide directly to us (such as name, email, and financial data), information about service usage, and technical information about your device."}, "usage": {"title": "3. How We Use Information", "content": "We use information to provide and improve the service, to contact you regarding your account, and to comply with legal obligations."}, "sharing": {"title": "4. Information Sharing", "content": "We do not sell, rent, or share your personal information with third parties, except as provided in this policy or when we have your consent."}, "security": {"title": "5. Data Security", "content": "We implement technical and organizational security measures to protect your information against unauthorized access, modification, disclosure, or destruction."}, "retention": {"title": "6. Data Retention", "content": "We retain your personal information only as long as necessary to fulfill the purposes for which it was collected or as required by legal requirements."}, "rights": {"title": "7. Your Rights", "content": "You have the right to access, correct, delete, or restrict the processing of your personal information. You also have the right to data portability."}, "cookies": {"title": "8. Cookies and Similar Technologies", "content": "We use cookies and similar technologies to improve your experience, analyze service usage, and personalize content."}, "contact": {"title": "9. <PERSON>", "content": "For questions about this privacy policy, you can contact <NAME_EMAIL>."}}}, "cookies": {"title": "<PERSON><PERSON>", "subtitle": "How we use cookies and similar technologies", "lastUpdated": "Last updated: December 15, 2024", "manage": {"title": "Manage Cookie Preferences", "description": "You can control what types of cookies you accept:", "necessary": {"title": "Necessary Cookies", "description": "Essential for website functionality", "required": "(Required)"}, "functional": {"title": "Functional Cookies", "description": "Enhance user experience"}, "analytics": {"title": "Analytics Cookies", "description": "Help us understand how you use the site"}, "marketing": {"title": "Marketing Cookies", "description": "Used for personalized advertising"}, "savePreferences": "Save Preferences", "acceptAll": "Accept All", "rejectAll": "Reject All"}, "sections": {"what": {"title": "1. What are Cookies?", "content": "Cookies are small text files that are stored on your device when you visit a website. They help us provide you with a better experience and understand how you use our service."}, "how": {"title": "2. How We Use Cookies", "content": "We use cookies to authenticate you, remember your preferences, analyze site traffic, and improve service functionality."}, "types": {"title": "3. Types of Cookies", "content": "We use necessary cookies (for basic functionality), functional cookies (for experience enhancement), analytics cookies (for statistics), and marketing cookies (for relevant advertising)."}, "thirdParty": {"title": "4. Third-Party Cookies", "content": "Some cookies are set by third-party services we use, such as Google Analytics for analysis and payment services for transaction processing."}, "control": {"title": "5. <PERSON><PERSON>", "content": "You can control and delete cookies through your browser settings. You can also use our preferences panel to manage the types of cookies you accept."}, "contact": {"title": "6. <PERSON>", "content": "For questions about cookie usage, you can contact <NAME_EMAIL>."}}}}, "product": {"features": {"title": "Features", "subtitle": "Discover all the powerful features of FinanceFlow", "hero": {"title": "Complete Features for Financial Management", "subtitle": "From expense tracking to advanced analytics, FinanceFlow provides all the tools you need to control your finances."}, "sections": {"analytics": {"title": "Advanced Analytics", "description": "Get detailed insights into your spending with interactive charts and customizable reports.", "features": ["Interactive charts and visualizations", "Customizable reports", "Trend analysis", "Period comparisons", "Export in multiple formats"]}, "transactions": {"title": "Transaction Management", "description": "Add, edit, and organize transactions with ease using our intuitive interface.", "features": ["Quick transaction entry", "Customizable categories", "Tags and notes", "Advanced search and filtering", "CSV file import"]}, "notifications": {"title": "Smart Notifications", "description": "Stay on top of your spending with personalized notifications and budget alerts.", "features": ["Budget alerts", "Large expense notifications", "Weekly reports", "Bill reminders", "Push and email notifications"]}, "security": {"title": "Security and Privacy", "description": "Your data is protected with the highest industry security standards.", "features": ["End-to-end encryption", "Two-factor authentication", "Automatic backup", "GDPR compliance", "Regular security audits"]}, "budgeting": {"title": "Smart Budgeting", "description": "Create and manage budgets for different categories and track progress in real-time.", "features": ["Category budgets", "Real-time tracking", "Overspend alerts", "Recurring budgets", "Performance analysis"]}, "reports": {"title": "Detailed Reports", "description": "Generate comprehensive reports to better understand your financial habits.", "features": ["Monthly and annual reports", "Category analysis", "Previous period comparisons", "PDF and Excel export", "Custom reports"]}, "collaboration": {"title": "Family Collaboration", "description": "Share budgets and expenses with family for collaborative financial management.", "features": ["Family accounts", "Granular permissions", "Shared budgets", "Member notifications", "Consolidated reports"]}, "sync": {"title": "Cloud Sync", "description": "Access your data from any device with automatic cloud synchronization.", "features": ["Real-time sync", "Multi-device access", "Automatic backup", "Version history", "Offline functionality"]}, "mobile": {"title": "Mobile App", "description": "Manage finances on the go with our optimized mobile application.", "features": ["Responsive design", "Quick expense entry", "Push notifications", "Home screen widget", "Offline functionality"]}}, "integration": {"title": "Powerful Integrations", "subtitle": "Connect FinanceFlow with your favorite services", "description": "Integrate with banks, payment services, and accounting applications for a complete experience."}, "cta": {"title": "Ready to Get Started?", "subtitle": "Try all these features free for 30 days.", "button": "Start Free Trial"}}, "pricing": {"title": "Pricing", "subtitle": "Simple and transparent plans for all needs", "hero": {"title": "Choose the Perfect Plan for You", "subtitle": "From individual users to large teams, we have the right plan for every need."}, "billing": {"monthly": "Monthly", "annually": "Annually", "save": "Save 20%"}, "plans": {"free": {"name": "Free", "description": "Perfect for beginners", "price": "0", "period": "per month", "features": ["Up to 50 transactions/month", "3 custom categories", "Basic reports", "Email support", "Mobile app"], "button": "Start Free"}, "personal": {"name": "Personal", "description": "For active users", "price": "29", "priceAnnual": "24", "period": "per month", "popular": "Most popular", "features": ["Unlimited transactions", "Unlimited categories", "Advanced reports", "Budgets and goals", "Excel/PDF export", "Priority support", "Automatic backup"], "button": "<PERSON>ose <PERSON>"}, "family": {"name": "Family", "description": "For families and couples", "price": "49", "priceAnnual": "39", "period": "per month", "features": ["All Personal features", "Up to 5 members", "Shared budgets", "Granular permissions", "Consolidated reports", "In-app chat", "Family manager"], "button": "Choose Family"}, "business": {"name": "Business", "description": "For teams and companies", "price": "99", "priceAnnual": "79", "period": "per month", "features": ["All Family features", "Unlimited members", "API access", "Advanced integrations", "SSO and SAML", "Dedicated manager", "99.9% SLA"], "button": "Contact Sales"}}, "comparison": {"title": "Detailed Comparison", "features": {"transactions": "Transactions", "categories": "Categories", "reports": "Reports", "budgets": "Budgets", "export": "Data Export", "support": "Support", "backup": "Backup", "members": "Members", "api": "API Access", "sso": "SSO/SAML", "manager": "Dedicated Manager", "sla": "SLA"}, "values": {"limited": "Limited", "unlimited": "Unlimited", "basic": "Basic", "advanced": "Advanced", "email": "Email", "priority": "Priority", "dedicated": "Dedicated", "yes": "Yes", "no": "No"}}, "faq": {"title": "Frequently Asked Questions", "items": {"trial": {"question": "Is there a free trial?", "answer": "Yes, we offer a 30-day free trial for all paid plans. No credit card required."}, "cancel": {"question": "Can I cancel my subscription anytime?", "answer": "Absolutely! You can cancel your subscription anytime from your account settings. No penalties or cancellation fees."}, "upgrade": {"question": "Can I change my plan anytime?", "answer": "Yes, you can upgrade or downgrade your plan anytime. Changes apply immediately and billing is adjusted proportionally."}, "data": {"question": "What happens to my data if I cancel?", "answer": "Your data remains available for 90 days after cancellation. You can export all data before permanent deletion."}, "security": {"question": "How secure is my data?", "answer": "We use bank-level encryption and comply with all industry security standards. Data is stored in certified data centers."}, "support": {"question": "What type of support do you offer?", "answer": "We offer email support for all plans, priority support for paid plans, and dedicated manager for Business plan."}}}, "cta": {"title": "Ready to Get Started?", "subtitle": "Join thousands of users managing their finances with FinanceFlow.", "button": "Start Free Trial"}}}, "support": {"documentation": {"title": "Documentation", "subtitle": "Complete guides and technical documentation for FinanceFlow", "search": {"placeholder": "Search documentation...", "button": "Search"}, "categories": {"all": "All", "getting_started": "Getting Started", "features": "Features", "api": "API", "security": "Security", "faq": "FAQ"}, "sections": {"getting_started": {"title": "Getting Started", "description": "Everything you need to know to get started with FinanceFlow", "articles": {"setup": {"title": "Account <PERSON><PERSON>", "description": "Step-by-step guide to setting up your first account", "readTime": "5 min"}, "first_transaction": {"title": "Adding Your First Transaction", "description": "Learn to add and manage transactions", "readTime": "3 min"}, "categories": {"title": "Organizing with Categories", "description": "How to create and use categories effectively", "readTime": "7 min"}}}, "features": {"title": "Features", "description": "Detailed guides for all features", "articles": {"budgets": {"title": "Budget Management", "description": "Create and monitor effective budgets", "readTime": "10 min"}, "reports": {"title": "Report Generation", "description": "How to generate useful and customized reports", "readTime": "8 min"}, "notifications": {"title": "Notification Setup", "description": "Set up personalized alerts and notifications", "readTime": "5 min"}}}, "api": {"title": "API Documentation", "description": "Integrate FinanceFlow into your applications", "articles": {"authentication": {"title": "Authentication", "description": "How to authenticate and manage tokens", "readTime": "15 min"}, "endpoints": {"title": "Available Endpoints", "description": "Complete list of API endpoints", "readTime": "20 min"}, "examples": {"title": "Code Examples", "description": "Practical examples in different languages", "readTime": "25 min"}}}, "security": {"title": "Security", "description": "Information about data security and privacy", "articles": {"data_protection": {"title": "Data Protection", "description": "How we protect your data and personal information", "readTime": "12 min"}, "two_factor": {"title": "Two-Factor Authentication", "description": "Setting up and using 2FA", "readTime": "6 min"}, "best_practices": {"title": "Best Practices", "description": "Recommendations for account security", "readTime": "8 min"}}}, "faq": {"title": "Frequently Asked Questions", "description": "Answers to the most common questions", "articles": {"general": {"title": "General Questions", "description": "Frequently asked questions about the service", "readTime": "10 min"}, "billing": {"title": "Billing and Payments", "description": "Questions about plans and billing", "readTime": "8 min"}, "technical": {"title": "Technical Issues", "description": "Solutions for common technical problems", "readTime": "15 min"}}}}, "quickStart": {"title": "Quick Start", "subtitle": "Start using FinanceFlow in just a few steps", "steps": ["Create your free account", "Add your first transaction", "Set up categories", "Create your first budget"], "button": "Start Now"}, "help": {"title": "Need Help?", "subtitle": "Can't find what you're looking for? Our team is here to help.", "contact": "Contact Support", "chat": "Live Chat"}}, "contact": {"title": "Contact", "subtitle": "We're here to help! Choose the contact method that works best for you."}, "help": {"title": "Help Center", "subtitle": "Find answers to your questions and learn to use FinanceFlow to its fullest."}}, "contact": {"categories": {"general": "General Question", "technical": "Technical Issue", "billing": "Billing", "feature": "Feature Request"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "methods": {"title": "Contact Methods", "chat": {"title": "Live Chat", "description": "Immediate response for urgent questions", "availability": "Monday - Friday, 9:00 AM - 6:00 PM", "action": "Start Chat"}, "email": {"title": "Email", "description": "For detailed questions and documentation", "availability": "Response within 24 hours"}, "phone": {"title": "Phone", "description": "Phone support for Premium customers", "availability": "Monday - Friday, 10:00 AM - 5:00 PM"}}, "office": {"title": "Our Office", "address": {"title": "Address", "line1": "123 Example Street", "line2": "Sector 1, Bucharest", "line3": "Romania, 010101"}, "hours": {"title": "Hours", "weekdays": "Monday - Friday: 9:00 AM - 6:00 PM", "weekend": "Saturday - Sunday: Closed"}}, "form": {"title": "Send us a Message", "name": "Full Name", "name_placeholder": "Enter your name", "email": "Email", "email_placeholder": "<EMAIL>", "subject": "Subject", "subject_placeholder": "Briefly describe the issue", "category": "Category", "priority": "Priority", "message": "Message", "message_placeholder": "Describe your issue or question in detail...", "required": "Required fields", "send": "Send Message", "sending": "Sending...", "success": "Message sent successfully! We'll get back to you soon.", "error": "An error occurred. Please try again."}, "faq": {"title": "Frequently Asked Questions", "subtitle": "You might find the answer here before contacting us", "response_time": {"question": "How long does it take to get a response?", "answer": "We typically respond within 24 hours for email and immediately for live chat during business hours."}, "technical_support": {"question": "Do you provide technical support?", "answer": "Yes, our technical team is available to help you with any technical issues."}, "billing_support": {"question": "Can I get help with billing?", "answer": "Absolutely! Our billing team can help you with any account and payment related questions."}, "feature_request": {"question": "How can I request a new feature?", "answer": "Use the contact form and select \"Feature Request\" as the category. We appreciate feedback!"}}}, "help": {"categories": {"all": "All", "getting_started": "Getting Started", "features": "Features", "settings": "Settings", "security": "Security", "billing": "Billing", "mobile": "Mobile App"}, "search": {"placeholder": "Search help center..."}, "sections": {"quick_start": {"title": "Quick Start", "description": "Guides to get started quickly with FinanceFlow", "items": {"setup": "Account <PERSON><PERSON>", "setup_desc": "How to set up your account in 5 minutes", "first_transaction": "First Transaction", "first_transaction_desc": "Add your first transaction", "categories": "Organizing Categories", "categories_desc": "Create and manage categories"}}, "features": {"title": "Features", "description": "Explore all available features", "items": {"budgets": "Budget Management", "budgets_desc": "Create and monitor budgets", "reports": "Reports and Analytics", "reports_desc": "Generate detailed reports", "goals": "Financial Goals", "goals_desc": "Set and track goals", "notifications": "Smart Notifications", "notifications_desc": "Configure personalized alerts"}}, "troubleshooting": {"title": "Troubleshooting", "description": "Solutions for common problems", "items": {"sync": "Sync Issues", "sync_desc": "Resolve synchronization problems", "login": "Login Issues", "login_desc": "Can't log into your account?", "performance": "App Performance", "performance_desc": "App running slowly?"}}, "advanced": {"title": "Advanced Features", "description": "For experienced users", "items": {"api": "API Integration", "api_desc": "Connect external applications", "automation": "Automation", "automation_desc": "Set up automatic rules", "export": "Data Export", "export_desc": "Export data in various formats"}}}, "popular": {"title": "Popular Articles", "views": "views", "article1": {"title": "How to set up my first budget?"}, "article2": {"title": "Connecting bank accounts"}, "article3": {"title": "Financial data security"}, "article4": {"title": "Using the mobile app"}, "article5": {"title": "Managing subscriptions"}}, "quick_actions": {"title": "Quick Actions", "contact": "Contact Support", "contact_desc": "Get personalized help", "docs": "API Documentation", "docs_desc": "For developers", "video_tour": "Video Tour", "video_tour_desc": "General overview"}, "cta": {"title": "Didn't find what you were looking for?", "description": "Our support team is ready to help you with any question or issue.", "contact": "Contact Support", "chat": "Live Chat"}}}