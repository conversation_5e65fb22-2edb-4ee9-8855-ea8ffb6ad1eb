import Stripe from 'stripe';
import { config } from 'dotenv';

config();

const stripe = new Stripe(process.env['STRIPE_SECRET_KEY'] || '', {
  apiVersion: '2023-10-16',
});

const WEBHOOK_EVENTS = [
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.payment_succeeded',
  'invoice.payment_failed',
  'customer.created',
  'customer.updated',
  'checkout.session.completed',
  'checkout.session.expired'
];

async function setupWebhooks() {
  try {
    console.log('🔗 Setting up Stripe webhooks...');
    
    const webhookUrl = process.env.WEBHOOK_URL || 'http://localhost:3001/api/webhooks/stripe';
    
    // List existing webhooks
    const existingWebhooks = await stripe.webhookEndpoints.list();
    
    // Check if webhook already exists for this URL
    const existingWebhook = existingWebhooks.data.find(
      webhook => webhook.url === webhookUrl
    );
    
    if (existingWebhook) {
      console.log(`✅ Webhook already exists for ${webhookUrl}`);
      console.log(`   Webhook ID: ${existingWebhook.id}`);
      console.log(`   Status: ${existingWebhook.status}`);
      console.log(`   Events: ${existingWebhook.enabled_events.join(', ')}`);
      
      // Update webhook if needed
      const needsUpdate = !WEBHOOK_EVENTS.every(event => 
        existingWebhook.enabled_events.includes(event)
      );
      
      if (needsUpdate) {
        console.log('🔄 Updating webhook events...');
        await stripe.webhookEndpoints.update(existingWebhook.id, {
          enabled_events: WEBHOOK_EVENTS as any
        });
        console.log('✅ Webhook events updated!');
      }
      
      return existingWebhook;
    }
    
    // Create new webhook
    console.log(`📦 Creating new webhook for ${webhookUrl}...`);
    const webhook = await stripe.webhookEndpoints.create({
      url: webhookUrl,
      enabled_events: WEBHOOK_EVENTS as any,
      description: 'Expense Tracker App Webhook'
    });
    
    console.log('✅ Webhook created successfully!');
    console.log(`   Webhook ID: ${webhook.id}`);
    console.log(`   Webhook Secret: ${webhook.secret}`);
    console.log('');
    console.log('🔑 IMPORTANT: Add this webhook secret to your .env file:');
    console.log(`   STRIPE_WEBHOOK_SECRET=${webhook.secret}`);
    console.log('');
    console.log('📋 Webhook Events:');
    WEBHOOK_EVENTS.forEach(event => {
      console.log(`   - ${event}`);
    });
    
    return webhook;
    
  } catch (error) {
    console.error('❌ Error setting up webhooks:', error);
    throw error;
  }
}

async function listWebhooks() {
  try {
    console.log('📋 Listing all Stripe webhooks...');
    
    const webhooks = await stripe.webhookEndpoints.list();
    
    if (webhooks.data.length === 0) {
      console.log('   No webhooks found.');
      return;
    }
    
    webhooks.data.forEach((webhook, index) => {
      console.log(`\n${index + 1}. Webhook ID: ${webhook.id}`);
      console.log(`   URL: ${webhook.url}`);
      console.log(`   Status: ${webhook.status}`);
      console.log(`   Created: ${new Date(webhook.created * 1000).toISOString()}`);
      console.log(`   Events: ${webhook.enabled_events.join(', ')}`);
    });
    
  } catch (error) {
    console.error('❌ Error listing webhooks:', error);
    throw error;
  }
}

async function deleteWebhook(webhookId: string) {
  try {
    console.log(`🗑️  Deleting webhook ${webhookId}...`);
    
    await stripe.webhookEndpoints.del(webhookId);
    
    console.log('✅ Webhook deleted successfully!');
    
  } catch (error) {
    console.error('❌ Error deleting webhook:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const webhookId = process.argv[3];
  
  switch (command) {
    case 'setup':
      setupWebhooks()
        .then(() => {
          console.log('🎉 Webhook setup completed!');
          process.exit(0);
        })
        .catch((error) => {
          console.error('💥 Webhook setup failed:', error);
          process.exit(1);
        });
      break;
      
    case 'list':
      listWebhooks()
        .then(() => {
          process.exit(0);
        })
        .catch((error) => {
          console.error('💥 Failed to list webhooks:', error);
          process.exit(1);
        });
      break;
      
    case 'delete':
      if (!webhookId) {
        console.error('❌ Please provide a webhook ID to delete.');
        console.log('Usage: node setupStripeWebhooks.js delete <webhook_id>');
        process.exit(1);
      }
      deleteWebhook(webhookId)
        .then(() => {
          process.exit(0);
        })
        .catch((error) => {
          console.error('💥 Failed to delete webhook:', error);
          process.exit(1);
        });
      break;
      
    default:
      console.log('🔗 Stripe Webhook Management');
      console.log('');
      console.log('Usage:');
      console.log('  node setupStripeWebhooks.js setup   - Create or update webhook');
      console.log('  node setupStripeWebhooks.js list    - List all webhooks');
      console.log('  node setupStripeWebhooks.js delete <id> - Delete a webhook');
      console.log('');
      console.log('Environment variables required:');
      console.log('  STRIPE_SECRET_KEY - Your Stripe secret key');
      console.log('  WEBHOOK_URL - Your webhook endpoint URL (optional, defaults to localhost)');
      process.exit(0);
  }
}

module.exports = { setupWebhooks, listWebhooks, deleteWebhook };