import { Sequelize, Options } from 'sequelize';
import { config as dotenvConfig } from 'dotenv';
import { safeLog } from '../utils/safeLogger';

dotenvConfig();

// Database configuration interface
interface DatabaseConfig {
  dialect: 'sqlite' | 'postgres';
  storage?: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  logging?: boolean | ((sql: string, timing?: number) => void);
  define: {
    timestamps: boolean;
    underscored: boolean;
    freezeTableName: boolean;
  };
  pool?: {
    max: number;
    min: number;
    acquire: number;
    idle: number;
  };
  dialectOptions?: {
    ssl?: {
      require: boolean;
      rejectUnauthorized: boolean;
    } | false;
  };
}

// Database configuration based on environment
const databaseConfig: Record<string, DatabaseConfig> = {
  development: {
    dialect: 'sqlite',
    storage: process.env.DB_PATH || './database.sqlite',
    logging: console.log,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  test: {
    dialect: 'sqlite',
    storage: ':memory:',
    logging: false,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  production: {
    dialect: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'expense_tracker',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    logging: false,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    dialectOptions: {
      ssl: process.env.DB_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false,
      } : false,
    },
  },
};

const env: string = process.env['NODE_ENV'] || 'development';
const dbConfig: DatabaseConfig = (env in databaseConfig ? databaseConfig[env as keyof typeof databaseConfig] : databaseConfig.development) as DatabaseConfig;

// Create Sequelize instance
let sequelize: Sequelize;

if (env === 'production' && process.env.DATABASE_URL) {
  // Use DATABASE_URL for production (Heroku, Railway, etc.)
  sequelize = new Sequelize(process.env.DATABASE_URL, {
    dialect: 'postgres',
    logging: false,
    define: dbConfig.define,
    pool: dbConfig.pool,
    dialectOptions: dbConfig.dialectOptions,
  } as Options);
} else {
  // Use individual config parameters
  if (dbConfig.dialect === 'sqlite') {
    sequelize = new Sequelize({
      dialect: dbConfig.dialect,
      storage: dbConfig.storage,
      logging: dbConfig.logging,
      define: dbConfig.define,
    } as Options);
  } else {
    sequelize = new Sequelize(
      dbConfig.database!,
      dbConfig.username!,
      dbConfig.password!,
      {
        host: dbConfig.host,
        port: dbConfig.port,
        dialect: dbConfig.dialect,
        logging: dbConfig.logging,
        define: dbConfig.define,
        pool: dbConfig.pool,
        dialectOptions: dbConfig.dialectOptions,
      } as Options,
    );
  }
}

// Test connection function
const testConnection = async (): Promise<boolean> => {
  try {
    await sequelize.authenticate();
    safeLog.debug(`✅ Database connection established successfully (${env}).`);
    return true;
  } catch (error) {
    safeLog.error('❌ Unable to connect to the database:', error);
    return false;
  }
};

// Initialize database function
const initializeDatabase = async (): Promise<boolean> => {
  try {
    // Import models
    const User = require('../models/User');
    const Category = require('../models/Category');
    const Expense = require('../models/Expense');

    // Define associations
    User.hasMany(Category, { foreignKey: 'user_id', as: 'categories' });
    Category.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

    User.hasMany(Expense, { foreignKey: 'user_id', as: 'expenses' });
    Expense.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

    Category.hasMany(Expense, { foreignKey: 'category_id', as: 'expenses' });
    Expense.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

    // Sync database
    if (env === 'development') {
      await sequelize.sync({ alter: true });
      safeLog.debug('✅ Database synchronized successfully.');
    } else if (env === 'test') {
      await sequelize.sync({ force: true });
      safeLog.debug('✅ Test database synchronized successfully.');
    }

    return true;
  } catch (error) {
    safeLog.error('❌ Database initialization failed:', error);
    return false;
  }
};

// Close connection function
const closeConnection = async (): Promise<void> => {
  try {
    await sequelize.close();
    safeLog.debug('✅ Database connection closed successfully.');
  } catch (error) {
    safeLog.error('❌ Error closing database connection:', error);
  }
};

export {
  sequelize,
  testConnection,
  initializeDatabase,
  closeConnection,
  dbConfig as config,
};

export default sequelize;