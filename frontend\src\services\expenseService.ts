import api from './api';
import { safeLog } from '../utils/safeLogger';
import type {
  Expense,
  CreateExpenseForm,
  UpdateExpenseForm,
  PaginatedResponse,
  ExpenseStats,
  ApiResponse,
  UseExpensesParams,
  ExpenseFilters,
} from '../types';

// Tipuri pentru serviciul de cheltuieli (folosind tipurile din index.ts)
interface ExpenseStatsParams {
  startDate?: string;
  endDate?: string;
  categoryId?: string;
  period?: 'week' | 'month' | 'quarter' | 'year';
}

/**
 * Serviciu pentru gestionarea cheltuielilor utilizatorilor
 */
class ExpenseService {
  /**
   * Exportă cheltuielile în format specificat
   */
  async exportExpenses(
    format: 'csv' | 'pdf' | 'excel',
    params: ExpenseFilters = {},
  ): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams(params as Record<string, string>);
      const response = await api.get(`/export/${format}?${queryParams}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la exportul cheltuielilor:', error);
      throw error;
    }
  }

  /**
   * Obține lista cheltuielilor
   */
  async getExpenses(
    params: UseExpensesParams = {},
  ): Promise<ApiResponse<PaginatedResponse<Expense>>> {
    try {
      const queryParams = new URLSearchParams(params as Record<string, string>);
      const response = await api.get(`/expenses?${queryParams}`);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la obținerea cheltuielilor:', error);
      throw error;
    }
  }

  /**
   * Creează o cheltuială nouă
   */
  async createExpense(expenseData: CreateExpenseForm): Promise<ApiResponse<Expense>> {
    try {
      const response = await api.post('/expenses', expenseData);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la crearea cheltuielii:', error);
      throw error;
    }
  }

  /**
   * Actualizează o cheltuială
   */
  async updateExpense(
    id: string,
    expenseData: Partial<UpdateExpenseForm>,
  ): Promise<ApiResponse<Expense>> {
    try {
      const response = await api.put(`/expenses/${id}`, expenseData);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la actualizarea cheltuielii:', error);
      throw error;
    }
  }

  /**
   * Șterge o cheltuială
   */
  async deleteExpense(id: string): Promise<void> {
    try {
      await api.delete(`/expenses/${id}`);
    } catch (error) {
      safeLog.error('Eroare la ștergerea cheltuielii:', error);
      throw error;
    }
  }

  /**
   * Obține statisticile cheltuielilor
   */
  async getExpenseStats(params: ExpenseStatsParams = {}): Promise<ApiResponse<ExpenseStats>> {
    try {
      const queryParams = new URLSearchParams(params as Record<string, string>);
      const response = await api.get(`/expenses/stats?${queryParams}`);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la obținerea statisticilor:', error);
      throw error;
    }
  }
}

export default new ExpenseService();
