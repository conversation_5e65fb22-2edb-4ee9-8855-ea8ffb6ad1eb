import {
  ArrowLeftIcon,
  EnvelopeIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  MapPinIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BugAntIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Contact = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    category: 'general',
    priority: 'medium',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);

  const categories = [
    {
      id: 'general',
      name: t('contact.categories.general', 'Înt<PERSON>bar<PERSON> Generală'),
      icon: InformationCircleIcon,
    },
    {
      id: 'technical',
      name: t('contact.categories.technical', 'Problemă Tehnică'),
      icon: BugAntIcon,
    },
    {
      id: 'billing',
      name: t('contact.categories.billing', 'Facturare'),
      icon: EnvelopeIcon,
    },
    {
      id: 'feature',
      name: t('contact.categories.feature', 'Cerere Funcționalitate'),
      icon: CheckCircleIcon,
    },
  ];

  const priorities = [
    {
      id: 'low',
      name: t('contact.priority.low', 'Scăzută'),
      color: 'text-green-600',
    },
    {
      id: 'medium',
      name: t('contact.priority.medium', 'Medie'),
      color: 'text-yellow-600',
    },
    {
      id: 'high',
      name: t('contact.priority.high', 'Înaltă'),
      color: 'text-red-600',
    },
  ];

  const contactMethods = [
    {
      icon: ChatBubbleLeftRightIcon,
      title: t('contact.methods.chat.title', 'Chat Live'),
      description: t('contact.methods.chat.description', 'Răspuns imediat pentru întrebări urgente'),
      availability: t('contact.methods.chat.availability', 'Luni - Vineri, 9:00 - 18:00'),
      action: t('contact.methods.chat.action', 'Începeți Chat'),
      color: 'bg-blue-500',
    },
    {
      icon: EnvelopeIcon,
      title: t('contact.methods.email.title', 'Email'),
      description: t('contact.methods.email.description', 'Pentru întrebări detaliate și documentație'),
      availability: t('contact.methods.email.availability', 'Răspuns în 24 ore'),
      action: '<EMAIL>',
      color: 'bg-green-500',
    },
    {
      icon: PhoneIcon,
      title: t('contact.methods.phone.title', 'Telefon'),
      description: t('contact.methods.phone.description', 'Suport telefonic pentru clienții Premium'),
      availability: t('contact.methods.phone.availability', 'Luni - Vineri, 10:00 - 17:00'),
      action: '+40 21 123 4567',
      color: 'bg-purple-500',
    },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        subject: '',
        category: 'general',
        priority: 'medium',
        message: '',
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-3xl font-bold text-gray-900">
                {t('support.contact.title', 'Contact')}
              </h1>
            </div>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl">
              {t('support.contact.subtitle', 'Suntem aici să vă ajutăm! Alegeți metoda de contact care vi se potrivește cel mai bine.')}
            </p>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Methods */}
            <div className="lg:col-span-1">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                {t('contact.methods.title', 'Metode de Contact')}
              </h2>

              <div className="space-y-4">
                {contactMethods.map((method, index) => {
                  const IconComponent = method.icon;
                  return (
                    <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                      <div className="flex items-start">
                        <div className={`${method.color} p-3 rounded-lg mr-4`}>
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {method.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-3">
                            {method.description}
                          </p>
                          <div className="flex items-center text-sm text-gray-500 mb-3">
                            <ClockIcon className="w-4 h-4 mr-1" />
                            {method.availability}
                          </div>
                          <button className="text-blue-600 hover:text-blue-800 font-medium text-sm">
                            {method.action}
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Office Info */}
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {t('contact.office.title', 'Biroul Nostru')}
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <MapPinIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-gray-900 font-medium">
                        {t('contact.office.address.title', 'Adresa')}
                      </p>
                      <p className="text-gray-600 text-sm">
                        {t('contact.office.address.line1', 'Strada Exemplu nr. 123')}<br />
                        {t('contact.office.address.line2', 'Sector 1, București')}<br />
                        {t('contact.office.address.line3', 'România, 010101')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <ClockIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-gray-900 font-medium">
                        {t('contact.office.hours.title', 'Program')}
                      </p>
                      <p className="text-gray-600 text-sm">
                        {t('contact.office.hours.weekdays', 'Luni - Vineri: 9:00 - 18:00')}<br />
                        {t('contact.office.hours.weekend', 'Sâmbătă - Duminică: Închis')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                  {t('contact.form.title', 'Trimiteți-ne un Mesaj')}
                </h2>

                {submitStatus === 'success' && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3" />
                      <p className="text-green-800">
                        {t('contact.form.success', 'Mesajul a fost trimis cu succes! Vă vom răspunde în curând.')}
                      </p>
                    </div>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-3" />
                      <p className="text-red-800">
                        {t('contact.form.error', 'A apărut o eroare. Vă rugăm să încercați din nou.')}
                      </p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('contact.form.name', 'Nume Complet')} *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t('contact.form.name_placeholder', 'Introduceți numele dvs.')}
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('contact.form.email', 'Email')} *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={t('contact.form.email_placeholder', '<EMAIL>')}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('contact.form.subject', 'Subiect')} *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={t('contact.form.subject_placeholder', 'Descrieți pe scurt problema')}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('contact.form.category', 'Categorie')}
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
                        {t('contact.form.priority', 'Prioritate')}
                      </label>
                      <select
                        id="priority"
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {priorities.map((priority) => (
                          <option key={priority.id} value={priority.id}>
                            {priority.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('contact.form.message', 'Mesaj')} *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={t('contact.form.message_placeholder', 'Descrieți detaliat problema sau întrebarea dvs...')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                    * {t('contact.form.required', 'Câmpuri obligatorii')}
                    </p>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isSubmitting
                        ? t('contact.form.sending', 'Se trimite...')
                        : t('contact.form.send', 'Trimite Mesajul')
                      }
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t('contact.faq.title', 'Întrebări Frecvente')}
              </h2>
              <p className="text-lg text-gray-600">
                {t('contact.faq.subtitle', 'Poate găsiți răspunsul aici înainte de a ne contacta')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('contact.faq.response_time.question', 'Cât durează să primiți un răspuns?')}
                </h3>
                <p className="text-gray-600">
                  {t('contact.faq.response_time.answer', 'De obicei răspundem în 24 de ore pentru email și imediat pentru chat live în timpul programului.')}
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('contact.faq.technical_support.question', 'Oferiti suport tehnic?')}
                </h3>
                <p className="text-gray-600">
                  {t('contact.faq.technical_support.answer', 'Da, echipa noastră tehnică este disponibilă pentru a vă ajuta cu orice problemă tehnică.')}
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('contact.faq.billing_support.question', 'Pot primi ajutor cu facturarea?')}
                </h3>
                <p className="text-gray-600">
                  {t('contact.faq.billing_support.answer', 'Absolut! Echipa noastră de facturare vă poate ajuta cu orice întrebări legate de cont și plăți.')}
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('contact.faq.feature_request.question', 'Cum pot cere o funcționalitate nouă?')}
                </h3>
                <p className="text-gray-600">
                  {t('contact.faq.feature_request.answer', 'Folosiți formularul de contact și selectați "Cerere Funcționalitate" ca categorie. Apreciem feedback-ul!')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Contact;
