import express from 'express';
import { body, param } from 'express-validator';
import { authenticateToken } from '../middleware/auth';
import subscriptionController from '../controllers/subscriptionController';

const router = express.Router();

/**
 * Rute pentru gestionarea abonamentelor
 */

// Toate rutele necesită autentificare
router.use(authenticateToken);

/**
 * @route   GET /api/subscriptions/plans
 * @desc    Obține toate planurile disponibile
 * @access  Private
 */
router.get('/plans', subscriptionController.getPlans);

/**
 * @route   GET /api/subscriptions/current
 * @desc    Obține abonamentul curent al utilizatorului
 * @access  Private
 */
router.get('/current', subscriptionController.getCurrentSubscription);

/**
 * @route   POST /api/subscriptions/checkout
 * @desc    Creează o sesiune de checkout pentru un plan
 * @access  Private
 */
router.post('/checkout', [
  body('planId')
    .isInt({ min: 1 })
    .withMessage('Plan ID must be a valid integer')
], subscriptionController.createCheckoutSession);

/**
 * @route   POST /api/subscriptions/portal
 * @desc    Creează portal pentru gestionarea abonamentului
 * @access  Private
 */
router.post('/portal', subscriptionController.createCustomerPortal);

/**
 * @route   POST /api/subscriptions/cancel
 * @desc    Anulează abonamentul curent
 * @access  Private
 */
router.post('/cancel', subscriptionController.cancelSubscription);

/**
 * @route   POST /api/subscriptions/reactivate
 * @desc    Reactivează un abonament anulat
 * @access  Private
 */
router.post('/reactivate', subscriptionController.reactivateSubscription);

/**
 * @route   GET /api/subscriptions/checkout/:sessionId
 * @desc    Verifică statusul unei sesiuni de checkout
 * @access  Private
 */
router.get('/checkout/:sessionId', [
  param('sessionId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Session ID is required')
], subscriptionController.checkCheckoutSession);

/**
 * @route   GET /api/subscriptions/usage
 * @desc    Obține statistici de utilizare
 * @access  Private
 */
router.get('/usage', subscriptionController.getUsageStats);

/**
 * @route   GET /api/subscriptions/permission/:action
 * @desc    Verifică dacă utilizatorul poate efectua o acțiune
 * @access  Private
 */
router.get('/permission/:action', [
  param('action')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Action is required')
], subscriptionController.checkPermission);

export default router;