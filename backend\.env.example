# Server Configuration
PORT=3000
NODE_ENV=development
API_VERSION=1.0.0

# Database Configuration
# For SQLite (development)
DB_TYPE=sqlite
DB_PATH=./data/expense_tracker.db

# For PostgreSQL (production)
# DB_TYPE=postgres
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=expense_tracker
# DB_USER=your_username
# DB_PASSWORD=your_password
# DB_SSL=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
# Comma-separated list of allowed origins (e.g., http://localhost:3000,https://your-frontend.com)
CORS_ALLOWED_ORIGINS=http://localhost:5173
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for password reset and verification)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# FROM_EMAIL=<EMAIL>
# FROM_NAME=Expense Tracker

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:5173

# Database Backup (optional)
# BACKUP_ENABLED=true
# BACKUP_INTERVAL=24h
# BACKUP_RETENTION=30d
# BACKUP_PATH=./backups

# Monitoring (optional)
# SENTRY_DSN=your-sentry-dsn
# ANALYTICS_ENABLED=false

# Cache Configuration (Redis)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
CACHE_TTL=3600
CACHE_USER_TTL=1800
CACHE_CATEGORY_TTL=7200
CACHE_EXPENSE_TTL=1800
CACHE_REPORT_TTL=3600
CACHE_PUBLIC_TTL=86400

# Security Configuration
SECURITY_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
SECURITY_RATE_LIMIT_WINDOW=900000
SECURITY_RATE_LIMIT_MAX=100
SECURITY_SLOW_DOWN_DELAY_AFTER=50
SECURITY_SLOW_DOWN_DELAY_MS=500
SECURITY_SLOW_DOWN_MAX_DELAY_MS=20000
SECURITY_BOT_DETECTION_ENABLED=true
SECURITY_ATTACK_DETECTION_ENABLED=true

# Audit Configuration
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=info
AUDIT_LOG_RETENTION_DAYS=90
AUDIT_ADMIN_LOG_ENABLED=true
AUDIT_DATA_CHANGE_LOG_ENABLED=true

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_SLOW_QUERY_THRESHOLD=1000
PERFORMANCE_MEMORY_THRESHOLD=512
PERFORMANCE_CPU_THRESHOLD=80

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_SUCCESS_URL=http://localhost:5173/subscription/success
STRIPE_CANCEL_URL=http://localhost:5173/subscription/cancel

# Subscription Plans (Stripe Price IDs)
STRIPE_BASIC_PLAN_ID=price_basic_plan_id_here
STRIPE_PREMIUM_PLAN_ID=price_premium_plan_id_here

# Plan Limits
FREE_PLAN_EXPENSE_LIMIT=50
BASIC_PLAN_EXPENSE_LIMIT=500
PREMIUM_PLAN_EXPENSE_LIMIT=unlimited