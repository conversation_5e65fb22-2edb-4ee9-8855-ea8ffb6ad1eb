# 📚 GHID PRINCIPAL - EXPENSE TRACKER

## 🎯 DESPRE PROIECT

**Expense Tracker** este o aplicație web modernă pentru gestionarea cheltuielilor personale, construită cu tehnologii de ultimă generație și un model de monetizare sustenabil.

### Obiectivul Principal
Dezvoltarea unui MVP care să genereze minimum $50/lună prin funcționalități utile și abonamente premium.

### Tehnologii Utilizate
- **Backend**: Node.js + Express + TypeScript + Prisma + PostgreSQL
- **Frontend**: React 18 + TypeScript + Tailwind CSS + Zustand + React Query
- **Monetizare**: Stripe pentru abonamente și plăți
- **Securitate**: JWT, Helmet, Rate Limiting, Audit Trail
- **Cache**: Redis pentru performanță optimizată

---

## 🚀 START RAPID

### 1. Cerințe de Sistem
- **Node.js**: 18.0.0 sau mai nou
- **npm**: 8.0.0 sau mai nou
- **PostgreSQL**: 13+ (pentru producție)
- **Redis**: 6+ (opțional, pentru cache)

### 2. Instalare și Configurare
```bash
# 1. Clonează repository-ul
git clone [repository-url]
cd expense-tracker

# 2. Instalează dependențele backend
cd backend
npm install

# 3. Configurează variabilele de mediu
cp .env.example .env
# Editează .env cu configurările tale

# 4. Inițializează baza de date
npm run db:generate
npm run db:migrate
npm run db:seed

# 5. Pornește backend-ul
npm run dev

# 6. În alt terminal, instalează frontend
cd ../frontend
npm install

# 7. Pornește frontend-ul
npm run dev
```

### 3. Verificare Instalare
- **Backend**: http://localhost:3000/api/health
- **Frontend**: http://localhost:5173
- **Accesibilitate rețea**: http://[IP-local]:5173

---

## 📋 DOCUMENTAȚIA COMPLETĂ

### Ghiduri Esențiale
1. **[01-SETUP-DEZVOLTARE.md](./01-SETUP-DEZVOLTARE.md)** - Configurare completă mediu dezvoltare
2. **[02-ARHITECTURA.md](./02-ARHITECTURA.md)** - Arhitectura aplicației și design patterns
3. **[03-SECURITATE.md](./03-SECURITATE.md)** - Implementarea securității și best practices
4. **[04-TESTARE.md](./04-TESTARE.md)** - Ghid complet de testare și CI/CD

### Ghiduri Avansate
5. **[05-PERFORMANTA.md](./05-PERFORMANTA.md)** - Optimizări de performanță și scalabilitate
6. **[06-MONETIZARE.md](./06-MONETIZARE.md)** - Setup Stripe și strategii de monetizare
7. **[07-CALITATE-COD.md](./07-CALITATE-COD.md)** - Standardele de calitate și code review
8. **[08-DEPLOYMENT.md](./08-DEPLOYMENT.md)** - Ghid de deployment și configurări producție

### Referințe și Istoric
9. **[09-API-REFERENCE.md](./09-API-REFERENCE.md)** - Documentația completă API
10. **[10-CHANGELOG.md](./10-CHANGELOG.md)** - Istoric modificări și versiuni
11. **[11-TROUBLESHOOTING.md](./11-TROUBLESHOOTING.md)** - Rezolvarea problemelor comune
12. **[12-CONTRIBUTII.md](./12-CONTRIBUTII.md)** - Ghid pentru contribuitori

---

## 🔧 COMENZI UTILE

### Backend
```bash
# Dezvoltare
npm run dev              # Pornește serverul în watch mode
npm run build            # Compilează TypeScript
npm run start            # Pornește serverul de producție

# Testare
npm test                 # Rulează toate testele
npm run test:watch       # Testare în watch mode
npm run test:coverage    # Testare cu coverage

# Baza de date
npm run db:migrate       # Aplică migrările
npm run db:seed          # Populează cu date de test
npm run db:reset         # Resetează baza de date

# Calitate cod
npm run lint             # Verifică codul cu ESLint
npm run format           # Formatează cu Prettier
npm run type-check       # Verifică tipurile TypeScript
```

### Frontend
```bash
# Dezvoltare
npm run dev              # Pornește dev server cu hot reload
npm run build            # Build pentru producție
npm run preview          # Preview build de producție

# Testare
npm test                 # Rulează testele cu Vitest
npm run test:ui          # Interfață grafică pentru teste
npm run test:coverage    # Coverage report

# Calitate cod
npm run lint             # ESLint pentru React/TypeScript
npm run format           # Prettier pentru formatare
npm run type-check       # Verifică tipurile TypeScript
```

---

## 👥 UTILIZATORI DE TEST

### Conturi Predefinite
```
Admin:
- Email: <EMAIL>
- Parola: admin123
- Rol: Administrator sistem

Demo:
- Email: <EMAIL>  
- Parola: password123
- Rol: Admin cu date sample

Utilizatori Test:
- <EMAIL> (Plan gratuit)
- <EMAIL> (Plan Basic)
- <EMAIL> (Plan Premium)
- Parola pentru toate: Test123!
```

---

## 🆘 SUPORT ȘI AJUTOR

### Probleme Comune
- **Port ocupat**: Schimbă porturile în .env (3000 → 3001, 5173 → 5174)
- **Erori de dependențe**: Șterge node_modules și rulează `npm install`
- **Probleme bază de date**: Rulează `npm run db:reset`

### Resurse Utile
- **Documentația Prisma**: https://www.prisma.io/docs
- **React Query**: https://tanstack.com/query/latest
- **Tailwind CSS**: https://tailwindcss.com/docs
- **Stripe Docs**: https://stripe.com/docs

### Contact
- **Issues**: Creează un issue în repository
- **Documentație**: Consultă ghidurile din docs/
- **Chat**: Folosește limba română pentru comunicare

---

## 📊 STATUS PROIECT

### ✅ Implementat
- Arhitectura backend și frontend
- Sistem de autentificare JWT
- CRUD pentru cheltuieli și categorii
- Integrare Stripe pentru monetizare
- Securitate avansată și audit trail
- Optimizări de performanță
- Documentație comprehensivă

### 🔄 În Dezvoltare
- Dashboard administrator complet
- Teste unitare și de integrare
- Pipeline CI/CD
- Funcționalități premium avansate

### 📅 Planificat
- Deployment în producție
- Monitoring și analytics
- Mobile app (React Native)
- Integrări externe (bănci, carduri)

---

*Ultima actualizare: Ianuarie 2025*
*Versiunea curentă: v1.1.4*