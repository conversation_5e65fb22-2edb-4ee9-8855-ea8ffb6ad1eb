/**
 * Tipuri pentru variabilele de mediu
 * Acest fișier definește tipurile pentru toate variabilele de mediu folosite în aplicație
 */

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Server configuration
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: string;
      
      // Database configuration
      DATABASE_URL: string;
      DB_HOST: string;
      DB_PORT: string;
      DB_NAME: string;
      DB_USER: string;
      DB_PASSWORD: string;
      DB_PATH: string;
      DB_SSL: string;
      
      // JWT configuration
      JWT_SECRET: string;
      JWT_EXPIRES_IN: string;
      JWT_REFRESH_EXPIRES_IN: string;
      
      // Bcrypt configuration
      BCRYPT_ROUNDS: string;
      
      // Stripe configuration
      STRIPE_SECRET_KEY: string;
      STRIPE_PUBLISHABLE_KEY: string;
      STRIPE_WEBHOOK_SECRET: string;
      STRIPE_BASIC_PLAN_ID: string;
      STRIPE_PREMIUM_PLAN_ID: string;
      
      // Plan limits
      FREE_PLAN_EXPENSE_LIMIT: string;
      BASIC_PLAN_EXPENSE_LIMIT: string;
      PREMIUM_PLAN_EXPENSE_LIMIT: string;
      
      // CORS configuration
      CORS_ALLOWED_ORIGINS: string;
      
      // Frontend URL
      FRONTEND_URL: string;
      
      // Redis configuration
      REDIS_URL: string;
      
      // Logging configuration
      LOG_LEVEL: string;
      
      // Security configuration
      HTTPS_ONLY: string;
      
      // Webhook configuration
      WEBHOOK_URL: string;
      
      // Slack configuration
      SLACK_WEBHOOK_URL: string;
      
      // Package version
      npm_package_version: string;
      
      // Development flags
      development: string;
      realtime: string;
      
      // Other environment variables
      type: string;
      free: string;
      
      // User properties (for session/context)
      id: string;
      stripeCustomerId: string;
      planType: string;
      subscriptionStatus: string;
      currency: string;
    }
  }
}

export {};
