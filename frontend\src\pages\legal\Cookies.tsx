import { ArrowLeftIcon, CogIcon, ChartBarIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Cookies = () => {
  const { t } = useTranslation();
  const [cookiePreferences, setCookiePreferences] = useState({
    necessary: true, // Always enabled
    functional: true,
    analytics: false,
    marketing: false,
  });

  const handlePreferenceChange = (type: string) => {
    if (type === 'necessary') return; // Cannot disable necessary cookies
    setCookiePreferences(prev => ({
      ...prev,
      [type]: !prev[type as keyof typeof prev],
    }));
  };

  const savePreferences = () => {
    // Save preferences to localStorage or send to backend
    localStorage.setItem('cookiePreferences', JSON.stringify(cookiePreferences));
    // Show success message
    alert(t('legal.cookies.preferences.saved', 'Preferințele au fost salvate!'));
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-2xl font-bold text-gray-900">
                {t('legal.cookies.title', 'Politica Cookies')}
              </h1>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            {/* Cookie Preferences Panel */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('legal.cookies.preferences.title', 'Setări Cookies')}
              </h2>
              <p className="text-gray-700 mb-6">
                {t(
                  'legal.cookies.preferences.description',
                  'Personalizați experiența dvs. prin gestionarea preferințelor pentru cookies.',
                )}
              </p>

              <div className="space-y-4">
                {/* Necessary Cookies */}
                <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <ShieldCheckIcon className="w-6 h-6 text-green-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {t('legal.cookies.types.necessary.title', 'Cookies Necesare')}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {t(
                          'legal.cookies.types.necessary.description',
                          'Esențiale pentru funcționarea aplicației',
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500 font-medium">
                    {t('legal.cookies.preferences.always', 'Întotdeauna active')}
                  </div>
                </div>

                {/* Functional Cookies */}
                <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <CogIcon className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {t('legal.cookies.types.functional.title', 'Cookies Funcționale')}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {t(
                          'legal.cookies.types.functional.description',
                          'Îmbunătățesc funcționalitatea aplicației',
                        )}
                      </p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={cookiePreferences.functional}
                      onChange={() => handlePreferenceChange('functional')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600" />
                  </label>
                </div>

                {/* Analytics Cookies */}
                <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <ChartBarIcon className="w-6 h-6 text-purple-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {t('legal.cookies.types.analytics.title', 'Cookies de Analiză')}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {t(
                          'legal.cookies.types.analytics.description',
                          'Ne ajută să înțelegem cum utilizați aplicația',
                        )}
                      </p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={cookiePreferences.analytics}
                      onChange={() => handlePreferenceChange('analytics')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600" />
                  </label>
                </div>
              </div>

              <button
                onClick={savePreferences}
                className="mt-6 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('legal.cookies.preferences.save', 'Salvează Preferințele')}
              </button>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-gray-600 mb-8">
                {t('legal.cookies.lastUpdated', 'Ultima actualizare: 1 ianuarie 2024')}
              </p>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.what.title', '1. Ce sunt Cookies-urile?')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.cookies.what.content',
                    'Cookies-urile sunt fișiere mici de text care sunt plasate pe computerul sau dispozitivul mobil atunci când vizitați un site web. Ele sunt utilizate pe scară largă pentru a face site-urile web să funcționeze sau să funcționeze mai eficient, precum și pentru a furniza informații proprietarilor site-ului.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.how.title', '2. Cum Utilizăm Cookies-urile')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.cookies.how.intro',
                    'Utilizăm cookies-urile pentru următoarele scopuri:',
                  )}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>{t('legal.cookies.how.authentication', 'Autentificare și securitate')}</li>
                  <li>{t('legal.cookies.how.preferences', 'Memorarea preferințelor dvs.')}</li>
                  <li>{t('legal.cookies.how.functionality', 'Îmbunătățirea funcționalității')}</li>
                  <li>{t('legal.cookies.how.analytics', 'Analiză și performanță')}</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.types.title', '3. Tipuri de Cookies')}
                </h2>

                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  {t('legal.cookies.types.necessary.title', 'Cookies Necesare')}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.cookies.types.necessary.detailed',
                    'Aceste cookies sunt esențiale pentru funcționarea aplicației și nu pot fi dezactivate. Ele includ cookies pentru autentificare, securitate și funcționalități de bază.',
                  )}
                </p>

                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  {t('legal.cookies.types.functional.title', 'Cookies Funcționale')}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.cookies.types.functional.detailed',
                    'Aceste cookies permit aplicației să își amintească alegerile pe care le faceți (cum ar fi limba sau regiunea) și să ofere funcționalități îmbunătățite și mai personale.',
                  )}
                </p>

                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  {t('legal.cookies.types.analytics.title', 'Cookies de Analiză')}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.cookies.types.analytics.detailed',
                    'Aceste cookies ne ajută să înțelegem cum interactionați cu aplicația prin colectarea și raportarea informațiilor în mod anonim. Acestea ne ajută să îmbunătățim serviciile noastre.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.thirdparty.title', '4. Cookies de la Terțe Părți')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.cookies.thirdparty.intro',
                    'Utilizăm servicii de la terțe părți care pot plasa cookies pe dispozitivul dvs.:',
                  )}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>
                    {t(
                      'legal.cookies.thirdparty.analytics',
                      'Google Analytics - pentru analiză și statistici',
                    )}
                  </li>
                  <li>
                    {t(
                      'legal.cookies.thirdparty.security',
                      'Servicii de securitate - pentru protecție împotriva amenințărilor',
                    )}
                  </li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.control.title', '5. Controlul Cookies-urilor')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.cookies.control.intro',
                    'Puteți controla și gestiona cookies-urile în mai multe moduri:',
                  )}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>
                    {t('legal.cookies.control.panel', 'Utilizați panoul de preferințe de mai sus')}
                  </li>
                  <li>
                    {t('legal.cookies.control.browser', 'Configurați setările browserului dvs.')}
                  </li>
                  <li>{t('legal.cookies.control.delete', 'Ștergeți cookies-urile existente')}</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.cookies.contact.title', '6. Contact')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.cookies.contact.content',
                    'Pentru întrebări despre utilizarea cookies-urilor, contactați-ne la: ',
                  )}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <EMAIL>
                  </a>
                </p>
              </section>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Cookies;
