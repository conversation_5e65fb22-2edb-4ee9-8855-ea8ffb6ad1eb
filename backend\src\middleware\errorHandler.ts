import { Request, Response } from 'express';
import logger from '../utils/logger';

// Interface for custom error
interface CustomError extends Error {
  statusCode?: number;
  code?: string | number;
  type?: string;
  errors?: unknown[];
  status?: number;
  field?: string;
  value?: unknown;
  path?: string;
  kind?: string;
}

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email?: string;
    role?: string;
    [key: string]: unknown;
  };
}

// Error types enum
enum ErrorTypes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}

// Structured error response interface
interface ErrorResponse {
  success: false;
  error: {
    type: string;
    message: string;
    code?: string | number;
    field?: string;
    details?: unknown;
  };
  timestamp: string;
  path: string;
  method: string;
  requestId?: string;
  stack?: string;
}

/**
 * Global error handling middleware
 * This should be the last middleware in the application
 */
const errorHandler = (
  err: CustomError,
  req: AuthenticatedRequest,
  res: Response,
): void => {
  // Generate unique request ID if not present
  const requestId = (req as any).id || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  let errorType = ErrorTypes.INTERNAL_SERVER_ERROR;
  let statusCode = err.statusCode || err.status || 500;
  let message = err.message || 'Internal Server Error';
  let code = err.code;
  let field = err.field;
  let details: unknown = null;

  // Log error cu detalii structurate
  logger.error({
    requestId,
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code,
      type: err.type
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      userEmail: req.user?.email,
      userRole: req.user?.role
    },
    timestamp: new Date().toISOString()
  });

  // Prisma Database Errors
  if (err.code && typeof err.code === 'string' && err.code.startsWith('P')) {
    errorType = ErrorTypes.DATABASE_ERROR;
    switch (err.code) {
      case 'P2002':
        message = 'Datele introduse există deja în sistem';
        statusCode = 409;
        errorType = ErrorTypes.CONFLICT_ERROR;
        details = { constraint: 'unique_constraint', fields: (err as any).meta?.target };
        break;
      case 'P2014':
        message = 'Datele introduse violează o restricție de relație';
        statusCode = 400;
        errorType = ErrorTypes.VALIDATION_ERROR;
        break;
      case 'P2003':
        message = 'Referința către o înregistrare inexistentă';
        statusCode = 400;
        errorType = ErrorTypes.VALIDATION_ERROR;
        details = { field: (err as any).meta?.fieldName };
        break;
      case 'P2025':
        message = 'Înregistrarea nu a fost găsită';
        statusCode = 404;
        errorType = ErrorTypes.NOT_FOUND_ERROR;
        break;
      case 'P2016':
        message = 'Eroare de interpretare a query-ului';
        statusCode = 400;
        errorType = ErrorTypes.VALIDATION_ERROR;
        break;
      case 'P2021':
        message = 'Tabela nu există în baza de date';
        statusCode = 500;
        break;
      case 'P2022':
        message = 'Coloana nu există în baza de date';
        statusCode = 500;
        break;
      default:
        message = 'Eroare de bază de date';
        statusCode = 500;
    }
  }

  // Mongoose Errors
  if (err.name === 'ValidationError') {
    errorType = ErrorTypes.VALIDATION_ERROR;
    message = Object.values(err.errors || {}).map((val: unknown) => (val as any).message).join(', ');
    statusCode = 400;
    details = { fields: Object.keys(err.errors || {}) };
  }

  if (err.name === 'CastError') {
    errorType = ErrorTypes.VALIDATION_ERROR;
    message = 'ID invalid';
    statusCode = 400;
    field = err.path;
    details = { value: err.value, kind: err.kind };
  }

  if (err.code === 11000) {
    errorType = ErrorTypes.CONFLICT_ERROR;
    const fieldName = Object.keys((err as any).keyValue)[0];
    field = fieldName;
    message = `${fieldName} există deja`;
    statusCode = 409;
    const keyValue = (err as any).keyValue;
    details = { duplicateValue: keyValue?.[fieldName as string] };
  }

  // Sequelize Errors
  if (err.name === 'SequelizeValidationError') {
    errorType = ErrorTypes.VALIDATION_ERROR;
    message = err.errors?.map((e: unknown) => (e as any).message).join(', ') || 'Eroare de validare';
    statusCode = 400;
    details = { fields: err.errors?.map((e: unknown) => (e as any).path) };
  }

  if (err.name === 'SequelizeUniqueConstraintError') {
    errorType = ErrorTypes.CONFLICT_ERROR;
    message = 'Datele introduse există deja';
    statusCode = 409;
    details = { fields: err.errors?.map((e: unknown) => (e as any).path) };
  }

  if (err.name === 'SequelizeForeignKeyConstraintError') {
    errorType = ErrorTypes.VALIDATION_ERROR;
    message = 'Referință invalidă către o resursă';
    statusCode = 400;
    details = { table: (err as any).table, constraint: (err as any).constraint };
  }

  if (err.name === 'SequelizeDatabaseError') {
    errorType = ErrorTypes.DATABASE_ERROR;
    message = 'Eroare de bază de date';
    statusCode = 500;
  }

  // JWT Errors
  if (err.name === 'JsonWebTokenError') {
    errorType = ErrorTypes.AUTHENTICATION_ERROR;
    message = 'Token invalid';
    statusCode = 401;
  }

  if (err.name === 'TokenExpiredError') {
    errorType = ErrorTypes.AUTHENTICATION_ERROR;
    message = 'Token expirat';
    statusCode = 401;
    details = { expiredAt: (err as any).expiredAt };
  }

  if (err.name === 'NotBeforeError') {
    errorType = ErrorTypes.AUTHENTICATION_ERROR;
    message = 'Token nu este încă valid';
    statusCode = 401;
    details = { notBefore: (err as any).date };
  }

  // Stripe Errors
  if (err.type && err.type.startsWith('Stripe')) {
    errorType = ErrorTypes.PAYMENT_ERROR;
    switch (err.type) {
      case 'StripeCardError':
        message = 'Cardul dvs. a fost respins';
        statusCode = 402;
        code = (err as any).code;
        details = { 
          declineCode: (err as any).declineCode,
          paymentMethod: (err as any).paymentMethod
        };
        break;
      case 'StripeRateLimitError':
        message = 'Prea multe cereri către Stripe';
        statusCode = 429;
        errorType = ErrorTypes.RATE_LIMIT_ERROR;
        break;
      case 'StripeInvalidRequestError':
        message = 'Cerere invalidă către Stripe';
        statusCode = 400;
        errorType = ErrorTypes.VALIDATION_ERROR;
        details = { param: (err as any).param };
        break;
      case 'StripeAPIError':
        message = 'Eroare internă Stripe';
        statusCode = 500;
        errorType = ErrorTypes.EXTERNAL_API_ERROR;
        break;
      case 'StripeConnectionError':
        message = 'Eroare de conexiune cu Stripe';
        statusCode = 503;
        errorType = ErrorTypes.EXTERNAL_API_ERROR;
        break;
      case 'StripeAuthenticationError':
        message = 'Eroare de autentificare Stripe';
        statusCode = 401;
        errorType = ErrorTypes.AUTHENTICATION_ERROR;
        break;
      default:
        message = 'Eroare de plată';
        statusCode = 500;
    }
  }

  // PayPal Errors
  if (err.name === 'PayPalError' || (err as any).paypal) {
    errorType = ErrorTypes.PAYMENT_ERROR;
    message = 'Eroare PayPal: ' + (err.message || 'Eroare necunoscută');
    statusCode = 400;
    details = { paypalError: (err as any).details };
  }

  // Multer Errors (File Upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    errorType = ErrorTypes.FILE_UPLOAD_ERROR;
    message = 'Fișierul este prea mare';
    statusCode = 413;
    details = { limit: (err as any).limit, field: (err as any).field };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    errorType = ErrorTypes.FILE_UPLOAD_ERROR;
    message = 'Prea multe fișiere';
    statusCode = 413;
    details = { limit: (err as any).limit };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    errorType = ErrorTypes.FILE_UPLOAD_ERROR;
    message = 'Tip de fișier neașteptat';
    statusCode = 400;
    field = (err as any).field;
  }

  if (err.code === 'LIMIT_PART_COUNT') {
    errorType = ErrorTypes.FILE_UPLOAD_ERROR;
    message = 'Prea multe părți în cerere';
    statusCode = 413;
  }

  // Rate Limiting Errors
  if (err.message && err.message.includes('Too many requests')) {
    errorType = ErrorTypes.RATE_LIMIT_ERROR;
    message = 'Prea multe cereri. Încercați din nou mai târziu.';
    statusCode = 429;
    details = { retryAfter: (err as any).retryAfter };
  }

  // Validation Errors (Joi, express-validator)
  if (err.name === 'ValidationError' && (err as any).details) {
    errorType = ErrorTypes.VALIDATION_ERROR;
    message = ((err as any).details || []).map((detail: unknown) => (detail as any).message).join(', ');
    statusCode = 400;
    details = { fields: (err as any).details.map((detail: unknown) => (detail as any).path) };
  }

  // Authorization Errors
  if (err.message && (err.message.includes('Forbidden') || err.message.includes('Access denied'))) {
    errorType = ErrorTypes.AUTHORIZATION_ERROR;
    message = 'Acces interzis';
    statusCode = 403;
  }

  // Not Found Errors
  if (err.message && err.message.includes('Not found')) {
    errorType = ErrorTypes.NOT_FOUND_ERROR;
    message = 'Resursa nu a fost găsită';
    statusCode = 404;
  }

  // Build structured error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      type: errorType,
      message,
      ...(code && { code }),
      ...(field && { field }),
      ...(details && typeof details === 'object' ? { details } : {})
    },
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    requestId,
    ...(process.env['NODE_ENV'] === 'development' && { stack: err.stack })
  };

  res.status(statusCode).json(errorResponse);
};

export default errorHandler;
export { ErrorTypes, ErrorResponse };