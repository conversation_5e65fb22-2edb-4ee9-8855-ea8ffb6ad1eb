import { PrismaClient } from '@prisma/client';
import logger from '../utils/logger';
import Stripe from 'stripe';

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  features: unknown;
  limits: unknown;
  stripeId: string;
  isActive: boolean;
  sortOrder: number;
}

interface SubscriptionStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  cancelledSubscriptions: number;
  revenueThisMonth: number;
  revenueLastMonth: number;
  planDistribution: Record<string, number>;
}

class SubscriptionService {
  /**
   * Obține planurile disponibile din baza de date
   */
  async getAvailablePlans(): Promise<Plan[]> {
    try {
      const plans = await prisma.plan.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          sortOrder: 'asc',
        },
      });

      return plans.map(plan => ({
        id: plan.id.toString(),
        name: plan.name,
        description: plan.description || '',
        price: parseFloat(plan.price.toString()),
        currency: plan.currency,
        interval: plan.interval,
        features: plan.features as any,
        limits: plan.limits as any,
        stripeId: plan.stripeId,
        isActive: plan.isActive,
        sortOrder: plan.sortOrder,
      }));
    } catch (error) {
      logger.error('Error getting available plans:', error);
      throw error;
    }
  }

  /**
   * Verifică limitele utilizatorului pentru o acțiune specifică
   */
  async checkUserLimits(userId: string, action: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          subscriptionStatus: true,
        },
      });

      if (!user) {
        return false;
      }

      // Dacă utilizatorul nu are abonament activ, folosește planul gratuit
      const planType = user.subscriptionStatus === 'active' ? user.planType : 'free';

      // Obține limitele planului
      const plan = await this.getPlanByType(planType || 'free');
      if (!plan) {
        return false;
      }

      const limits = plan.limits as any;

      // Verifică limitele în funcție de acțiune
      switch (action) {
        case 'createExpense':
          if (limits.expensesPerMonth === -1) return true;
          return await this.checkExpenseLimit(userId, limits.expensesPerMonth);

        case 'createCategory':
          if (limits.categories === -1) return true;
          return await this.checkCategoryLimit(userId, limits.categories);

        case 'exportData':
          if (limits.exportsPerMonth === -1) return true;
          return await this.checkExportLimit(userId, limits.exportsPerMonth);

        default:
          return true;
      }
    } catch (error) {
      logger.error('Error checking user limits:', error);
      return false;
    }
  }

  /**
   * Actualizează utilizarea utilizatorului
   */
  async updateUsage(userId: string, action: string): Promise<void> {
    try {
      // Înregistrează acțiunea în usage_logs
      await prisma.usageLog.create({
        data: {
          userId: userId,
          action,
          resource: this.getResourceFromAction(action),
          createdAt: new Date(),
        },
      });

      logger.info(`Usage updated for user ${userId}, action: ${action}`);
    } catch (error) {
      logger.error('Error updating usage:', error);
      throw error;
    }
  }

  /**
   * Obține statistici despre abonamente
   */
  async getSubscriptionStats(): Promise<SubscriptionStats> {
    try {
      const now = new Date();
      const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      const [
        totalSubscriptions,
        activeSubscriptions,
        cancelledSubscriptions,
        thisMonthRevenue,
        lastMonthRevenue,
        planDistribution,
      ] = await Promise.all([
        prisma.subscription.count(),
        prisma.subscription.count({
          where: {
            status: 'active',
          },
        }),
        prisma.subscription.count({
          where: {
            status: 'canceled',
          },
        }),
        this.calculateRevenue(startOfThisMonth, now),
        this.calculateRevenue(startOfLastMonth, endOfLastMonth),
        this.getPlanDistribution(),
      ]);

      return {
        totalSubscriptions: totalSubscriptions,
        activeSubscriptions: activeSubscriptions,
        cancelledSubscriptions: cancelledSubscriptions,
        revenueThisMonth: thisMonthRevenue,
        revenueLastMonth: lastMonthRevenue,
        planDistribution: planDistribution,
      };
    } catch (error) {
      logger.error('Error getting subscription stats:', error);
      throw error;
    }
  }

  /**
   * Sincronizează planurile din Stripe
   */
  async syncPlansFromStripe(): Promise<void> {
    try {
      const stripePlans = await stripe.prices.list({
        active: true,
        expand: ['data.product'],
      });

      for (const stripePrice of stripePlans.data) {
        const product = stripePrice.product as Stripe.Product;

        await prisma.plan.upsert({
          where: { stripeId: stripePrice.id },
          update: {
            name: product.name,
            description: product.description,
            price: stripePrice.unit_amount ? stripePrice.unit_amount / 100 : 0,
            currency: stripePrice.currency.toUpperCase(),
            interval: stripePrice.recurring?.interval || 'month',
            isActive: stripePrice.active,
          },
          create: {
            stripeId: stripePrice.id,
            name: product.name,
            description: product.description || '',
            price: stripePrice.unit_amount ? stripePrice.unit_amount / 100 : 0,
            currency: stripePrice.currency.toUpperCase(),
            interval: stripePrice.recurring?.interval || 'month',
            features: {},
            limits: {},
            isActive: stripePrice.active,
            sortOrder: 0,
          },
        });
      }

      logger.info('Plans synced from Stripe successfully');
    } catch (error) {
      logger.error('Error syncing plans from Stripe:', error);
      throw error;
    }
  }

  // Helper methods
  private getResourceFromAction(action: string): string {
    switch (action) {
      case 'createExpense':
      case 'updateExpense':
      case 'deleteExpense':
        return 'expense';
      case 'createCategory':
      case 'updateCategory':
      case 'deleteCategory':
        return 'category';
      case 'exportData':
        return 'export';
      default:
        return 'unknown';
    }
  }

  private async getPlanByType(planType: string): Promise<Plan | null> {
    const planMapping: Record<string, string> = {
      free: 'freePlan',
      basic: 'priceBasicMonthly',
      premium: 'pricePremiumMonthly',
    };

    const stripeId = planMapping[planType];
    if (!stripeId) return null;

    const plan = await prisma.plan.findFirst({
      where: { stripeId: stripeId },
    });

    if (!plan) return null;

    return {
      id: plan.id.toString(),
      name: plan.name,
      description: plan.description || '',
      price: parseFloat(plan.price.toString()),
      currency: plan.currency,
      interval: plan.interval,
      features: plan.features as any,
      limits: plan.limits as any,
      stripeId: plan.stripeId,
      isActive: plan.isActive,
      sortOrder: plan.sortOrder,
    };
  }

  private async checkExpenseLimit(userId: string, limit: number): Promise<boolean> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const count = await prisma.expense.count({
      where: {
        userId: userId,
        createdAt: {
          gte: startOfMonth,
        },
      },
    });

    return count < limit;
  }

  private async checkCategoryLimit(userId: string, limit: number): Promise<boolean> {
    const count = await prisma.category.count({
      where: {
        userId: userId,
        isDefault: false,
      },
    });

    return count < limit;
  }

  private async checkExportLimit(userId: string, limit: number): Promise<boolean> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const count = await prisma.usageLog.count({
      where: {
        userId: userId,
        action: 'exportData',
        createdAt: {
          gte: startOfMonth,
        },
      },
    });

    return count < limit;
  }

  private async calculateRevenue(startDate: Date, endDate: Date): Promise<number> {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        status: 'active',
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        plan: true,
      },
    });

    return subscriptions.reduce((total, sub) => {
      return total + parseFloat(sub.plan.price.toString());
    }, 0);
  }

  private async getPlanDistribution(): Promise<Record<string, number>> {
    const distribution = await prisma.subscription.groupBy({
      by: ['planId'],
      where: {
        status: 'active',
      },
      _count: {
        planId: true,
      },
    });

    const result: Record<string, number> = {};

    for (const item of distribution) {
      const plan = await prisma.plan.findUnique({
        where: { id: item.planId },
      });

      if (plan) {
        result[plan.name] = item._count.planId;
      }
    }

    return result;
  }
}

const subscriptionService = new SubscriptionService();
export default subscriptionService;
