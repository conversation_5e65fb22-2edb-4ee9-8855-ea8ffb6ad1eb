@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Variabile CSS pentru teme și culori */
:root {
  /* Culori principale */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
}

/* Reset și stiluri de bază */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white text-gray-900 font-sans antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  /* Stiluri pentru scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Stiluri pentru focus */
  :focus {
    @apply outline-none;
  }

  :focus-visible {
    @apply ring-2 ring-primary-500 ring-offset-2;
  }

  /* Stiluri pentru selecție text */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }

  /* Stiluri pentru input-uri */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
  }
}

/* Componente personalizate */
@layer components {
  /* Butoane */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 active:bg-green-800;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
  }

  .btn-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-50 hover:text-gray-900;
  }

  .btn-ghost {
    @apply hover:bg-gray-100 hover:text-gray-900;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Input-uri */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-transparent px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .input-error {
    @apply border-red-500 focus-visible:ring-red-500;
  }

  /* Card-uri */
  .card {
    @apply rounded-lg border bg-white text-gray-900 shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-gray-600;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Badge-uri */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-primary-600 text-white hover:bg-primary-700;
  }

  .badge-secondary {
    @apply border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200;
  }

  .badge-success {
    @apply border-transparent bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply border-transparent bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply border-transparent bg-red-100 text-red-800;
  }

  .badge-outline {
    @apply text-gray-900;
  }

  /* Alerte */
  .alert {
    @apply relative w-full rounded-lg border p-4;
  }

  .alert-success {
    @apply border-green-200 bg-green-50 text-green-800;
  }

  .alert-warning {
    @apply border-yellow-200 bg-yellow-50 text-yellow-800;
  }

  .alert-error {
    @apply border-red-200 bg-red-50 text-red-800;
  }

  .alert-info {
    @apply border-blue-200 bg-blue-50 text-blue-800;
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse rounded-md bg-gray-200;
  }

  /* Separator */
  .separator {
    @apply shrink-0 bg-gray-200;
  }

  .separator-horizontal {
    @apply h-[1px] w-full;
  }

  .separator-vertical {
    @apply h-full w-[1px];
  }
}

/* Utilități personalizate */
@layer utilities {
  /* Animații personalizate */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-bounce-soft {
    animation: bounceSoft 0.6s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .bg-gradient-success {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  .bg-gradient-warning {
    @apply bg-gradient-to-r from-yellow-500 to-orange-500;
  }

  .bg-gradient-danger {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }

  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  /* Shadows personalizate */
  .shadow-soft {
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.08);
  }

  .shadow-medium {
    box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.12);
  }

  .shadow-strong {
    box-shadow: 0 8px 24px 0 rgb(0 0 0 / 0.16);
  }

  /* Responsive utilities */
  .container-responsive {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Print utilities */
  @media print {
    .print-hidden {
      @apply hidden;
    }

    .print-visible {
      @apply block;
    }
  }
}

/* Keyframes pentru animații */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceSoft {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animații pentru landing page */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Efecte de hover îmbunătățite */
.hover\:shadow-3xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Backdrop blur pentru browsere care nu suportă */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

@supports not (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Stiluri pentru accesibilitate */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Stiluri pentru high contrast */
@media (prefers-contrast: high) {
  .btn {
    @apply border-2;
  }

  .input {
    @apply border-2;
  }

  .card {
    @apply border-2;
  }
}