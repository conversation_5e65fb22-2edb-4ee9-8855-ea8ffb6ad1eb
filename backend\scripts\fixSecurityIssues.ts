/**
 * Script pentru rezolvarea problemelor de securitate identificate de ESLint
 */

import { promises as fs } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Tipuri pentru configurare
interface SecurityFix {
  pattern: RegExp;
  replacement: string;
  description: string;
  fileTypes: string[];
}

interface FixResult {
  file: string;
  fixes: number;
  errors: string[];
}

// Configurări pentru fix-uri automate
const securityFixes: SecurityFix[] = [
  {
    pattern: /console\.log\(/g,
    replacement: 'safeLog.debug(',
    description: 'Replace console.log with safe logging',
    fileTypes: ['.ts', '.js']
  },
  {
    pattern: /console\.error\(/g,
    replacement: 'safeLog.error(',
    description: 'Replace console.error with safe logging',
    fileTypes: ['.ts', '.js']
  },
  {
    pattern: /console\.warn\(/g,
    replacement: 'safeLog.warn(',
    description: 'Replace console.warn with safe logging',
    fileTypes: ['.ts', '.js']
  },
  {
    pattern: /console\.info\(/g,
    replacement: 'safeLog.info(',
    description: 'Replace console.info with safe logging',
    fileTypes: ['.ts', '.js']
  },
  {
    pattern: /: any\b/g,
    replacement: ': unknown',
    description: 'Replace explicit any with unknown',
    fileTypes: ['.ts']
  },
  {
    pattern: /\bany\[\]/g,
    replacement: 'unknown[]',
    description: 'Replace any[] with unknown[]',
    fileTypes: ['.ts']
  },
  {
    pattern: /Array<any>/g,
    replacement: 'Array<unknown>',
    description: 'Replace Array<any> with Array<unknown>',
    fileTypes: ['.ts']
  }
];

// Funcții utilitare
class SecurityFixer {
  private srcDir: string;
  private results: FixResult[] = [];

  constructor(srcDir: string) {
    this.srcDir = srcDir;
  }

  /**
   * Găsește toate fișierele TypeScript/JavaScript
   */
  private async findFiles(dir: string, extensions: string[]): Promise<string[]> {
    const files: string[] = [];
    
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        const subFiles = await this.findFiles(fullPath, extensions);
        files.push(...subFiles);
      } else if (entry.isFile() && extensions.some(ext => entry.name.endsWith(ext))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Aplică fix-urile pe un fișier
   */
  private async fixFile(filePath: string): Promise<FixResult> {
    const result: FixResult = {
      file: filePath,
      fixes: 0,
      errors: []
    };

    try {
      let content = await fs.readFile(filePath, 'utf8');
      const originalContent = content;
      
      // Verifică dacă fișierul are deja import pentru safeLog
      const hasSafeLogImport = content.includes('safeLog') || content.includes('from \'../utils/safeLogger\'');
      
      // Aplică fix-urile
      for (const fix of securityFixes) {
        if (fix.fileTypes.some(ext => filePath.endsWith(ext))) {
          const matches = content.match(fix.pattern);
          if (matches) {
            content = content.replace(fix.pattern, fix.replacement);
            result.fixes += matches.length;
            
            // Adaugă import pentru safeLog dacă este necesar
            if (fix.replacement.includes('safeLog') && !hasSafeLogImport) {
              const importStatement = "import { safeLog } from '../utils/safeLogger';\n";
              
              // Găsește locul potrivit pentru import
              const importRegex = /^import.*from.*['"];?$/gm;
              const imports = content.match(importRegex);
              
              if (imports && imports.length > 0) {
                // Adaugă după ultimul import
                const lastImport = imports[imports.length - 1];
                const lastImportIndex = content.lastIndexOf(lastImport);
                const insertIndex = lastImportIndex + lastImport.length + 1;
                content = content.slice(0, insertIndex) + importStatement + content.slice(insertIndex);
              } else {
                // Adaugă la începutul fișierului
                content = importStatement + '\n' + content;
              }
            }
          }
        }
      }
      
      // Scrie fișierul doar dacă s-au făcut modificări
      if (content !== originalContent) {
        await fs.writeFile(filePath, content, 'utf8');
      }
      
    } catch (error) {
      result.errors.push(`Error processing file: ${error}`);
    }

    return result;
  }

  /**
   * Rulează fix-urile pe toate fișierele
   */
  async fixAll(): Promise<void> {
    console.log('🔍 Searching for files to fix...');
    
    const files = await this.findFiles(this.srcDir, ['.ts', '.js']);
    console.log(`📁 Found ${files.length} files to process`);
    
    console.log('🔧 Applying security fixes...');
    
    for (const file of files) {
      const result = await this.fixFile(file);
      this.results.push(result);
      
      if (result.fixes > 0) {
        console.log(`✅ ${path.relative(this.srcDir, file)}: ${result.fixes} fixes applied`);
      }
      
      if (result.errors.length > 0) {
        console.log(`❌ ${path.relative(this.srcDir, file)}: ${result.errors.join(', ')}`);
      }
    }
    
    this.printSummary();
  }

  /**
   * Afișează rezumatul fix-urilor
   */
  private printSummary(): void {
    const totalFixes = this.results.reduce((sum, result) => sum + result.fixes, 0);
    const filesWithFixes = this.results.filter(result => result.fixes > 0).length;
    const filesWithErrors = this.results.filter(result => result.errors.length > 0).length;
    
    console.log('\n📊 Summary:');
    console.log(`   Total fixes applied: ${totalFixes}`);
    console.log(`   Files modified: ${filesWithFixes}`);
    console.log(`   Files with errors: ${filesWithErrors}`);
    
    if (totalFixes > 0) {
      console.log('\n✨ Security fixes completed!');
      console.log('   Next steps:');
      console.log('   1. Review the changes');
      console.log('   2. Run tests to ensure functionality');
      console.log('   3. Run ESLint again to check remaining issues');
    }
  }

  /**
   * Rulează ESLint pentru a verifica progresul
   */
  async checkProgress(): Promise<void> {
    console.log('\n🔍 Running ESLint to check progress...');
    
    try {
      const { stdout, stderr } = await execAsync('npm run lint', {
        cwd: path.dirname(this.srcDir)
      });
      
      console.log('ESLint output:');
      console.log(stdout);
      
      if (stderr) {
        console.log('ESLint errors:');
        console.log(stderr);
      }
    } catch (error) {
      console.log('ESLint found issues (expected):');
      if (error instanceof Error && 'stdout' in error) {
        console.log((error as any).stdout);
      }
    }
  }
}

// Funcția principală
async function main(): Promise<void> {
  const srcDir = path.join(__dirname, '..', 'src');
  const fixer = new SecurityFixer(srcDir);
  
  console.log('🚀 Starting security fixes for backend...');
  console.log(`📂 Source directory: ${srcDir}`);
  
  try {
    await fixer.fixAll();
    
    // Opțional: verifică progresul cu ESLint
    const shouldCheckProgress = process.argv.includes('--check-progress');
    if (shouldCheckProgress) {
      await fixer.checkProgress();
    }
    
  } catch (error) {
    console.error('❌ Error during security fixes:', error);
    process.exit(1);
  }
}

// Rulează scriptul dacă este apelat direct
if (require.main === module) {
  main().catch(console.error);
}

export { SecurityFixer };