# 🧪 Frontend Tests Organization

## 📁 Directory Structure

```
src/
├── components/
│   └── __tests__/            # Component tests
│       └── Button.test.tsx
├── hooks/
│   └── __tests__/            # Hook tests
│       └── useAuth.test.ts
├── services/
│   └── __tests__/            # Service tests
├── utils/
│   └── __tests__/            # Utility function tests
└── tests/
    ├── README.md             # This documentation
    └── setup.ts              # Test setup and utilities
```

## 🎯 Test Types

### Component Tests (`/components/__tests__`)
- Test component rendering
- Test user interactions
- Test props and state changes
- Test accessibility

### Hook Tests (`/hooks/__tests__`)
- Test custom hook behavior
- Test hook state management
- Test hook side effects
- Test hook error handling

### Service Tests (`/services/__tests__`)
- Test API service methods
- Test data transformation
- Test error handling
- Test caching behavior

### Utility Tests (`/utils/__tests__`)
- Test pure functions
- Test helper methods
- Test data validation
- Test formatting functions

## 🔧 Configuration

- **Test Runner**: Vitest
- **Test Environment**: jsdom (for DOM testing)
- **Testing Library**: React Testing Library
- **Setup**: `src/tests/setup.ts`

## 🚀 Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test Button.test.tsx

# Run tests in specific directory
npm test src/components/__tests__
```

## 📝 Test Naming Conventions

- **Files**: `*.test.tsx` for components, `*.test.ts` for utilities/hooks
- **Describe blocks**: Use the component/function name being tested
- **Test cases**: Use descriptive "should" statements

### Example:
```typescript
describe('Button Component', () => {
  it('should render with default props', () => {
    // Test implementation
  });
  
  it('should handle click events', () => {
    // Test implementation
  });
  
  it('should be disabled when loading', () => {
    // Test implementation
  });
});
```

## 🛠️ Test Utilities

### Custom Render Function
```typescript
import { customRender } from '../tests/setup';

// Renders component with all providers
const { getByText } = customRender(<MyComponent />);
```

### Mock Functions
```typescript
import { createMockUser } from '../tests/setup';

const mockUser = createMockUser({ role: 'admin' });
```

## 📊 Coverage Requirements

- **Global**: 85% (branches, functions, lines, statements)
- **Components**: 80% (focused on user interactions)
- **Hooks**: 90% (higher requirement for state management)
- **Services**: 90% (critical for data handling)

## 🎨 Testing Best Practices

### Component Testing
- Test user behavior, not implementation details
- Use semantic queries (`getByRole`, `getByLabelText`)
- Test accessibility attributes
- Mock external dependencies

### Hook Testing
- Use `renderHook` from Testing Library
- Test all hook states and transitions
- Test error boundaries
- Mock API calls

### Service Testing
- Mock HTTP requests
- Test error scenarios
- Test data transformation
- Test caching behavior

## 🔍 Debugging Tests

```typescript
// Debug component output
screen.debug();

// Debug specific element
screen.debug(screen.getByRole('button'));

// Use testing playground
screen.logTestingPlaygroundURL();
```

## 🚫 Common Pitfalls

- Don't test implementation details
- Don't use `container.querySelector` unless necessary
- Always clean up after tests
- Mock external dependencies properly
- Use `waitFor` for asynchronous operations