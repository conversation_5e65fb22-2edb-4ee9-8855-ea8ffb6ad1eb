import React, { useState, useEffect, type ReactNode } from 'react';
import { Outlet, useLocation } from 'react-router-dom';

import { useAuthStore } from '../../store/authStore';
import { cn } from '../../utils/helpers';

import Footer from './Footer';
import Header from './Header';
import Sidebar from './Sidebar';

interface LayoutProps {
  children?: ReactNode;
  showSidebar?: boolean | null;
  showFooter?: boolean;
  className?: string;
}

/**
 * Componenta principală de layout pentru aplicație
 * Gestionează structura generală: header, sidebar, conținut principal și footer
 */
const Layout: React.FC<LayoutProps> = ({
  children,
  showSidebar = null,
  showFooter = true,
  className = '',
}) => {
  const { isAuthenticated } = useAuthStore();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Determină dacă să afișeze sidebar-ul
  const shouldShowSidebar = showSidebar !== null ? showSidebar : isAuthenticated;

  // Închide sidebar-ul mobil la schimbarea rutei
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Gestionează redimensionarea ferestrei pentru sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false); // Închide overlay-ul pe desktop
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Salvează preferința de collapse în localStorage
  useEffect(() => {
    const savedCollapsed = localStorage.getItem('sidebar-collapsed');
    if (savedCollapsed !== null) {
      setSidebarCollapsed(JSON.parse(savedCollapsed));
    }
  }, []);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarCollapse = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <div className={cn('min-h-screen bg-gray-50 flex flex-col', className)}>
      {/* Header */}
      <Header
        onSidebarToggle={handleSidebarToggle}
        showSidebarToggle={shouldShowSidebar}
        sidebarOpen={sidebarOpen}
      />

      {/* Main Content Area */}
      <div className="flex flex-1 relative">
        {/* Sidebar */}
        {shouldShowSidebar && (
          <>
            {/* Mobile Overlay */}
            {sidebarOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
                onClick={handleSidebarClose}
                aria-hidden="true"
              />
            )}

            {/* Sidebar Component */}
            <Sidebar
              isOpen={sidebarOpen}
              isCollapsed={sidebarCollapsed}
              onClose={handleSidebarClose}
              onCollapse={handleSidebarCollapse}
            />
          </>
        )}

        {/* Main Content */}
        <main
          className={cn(
            'flex-1 flex flex-col min-h-0 transition-all duration-300 ease-in-out',
            shouldShowSidebar && {
              'lg:ml-64': !sidebarCollapsed,
              'lg:ml-16': sidebarCollapsed,
            },
          )}
        >
          {/* Page Content */}
          <div className="flex-1 p-4 lg:p-6 xl:p-8">
            <div className="max-w-7xl mx-auto">{children || <Outlet />}</div>
          </div>

          {/* Footer */}
          {showFooter && (
            <Footer
              className={cn(
                shouldShowSidebar && {
                  'lg:ml-64': !sidebarCollapsed,
                  'lg:ml-16': sidebarCollapsed,
                },
              )}
              minimal={true}
              showLinks={false}
              showContact={false}
            />
          )}
        </main>
      </div>

      {/* Keyboard Navigation Helper */}
      <div className="sr-only">
        <a href="#main-content" className="skip-link">
          Sari la conținutul principal
        </a>
      </div>
    </div>
  );
};

interface PublicLayoutProps {
  children: ReactNode;
}

/**
 * Layout simplu fără sidebar pentru pagini publice
 */
export const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  return (
    <Layout showSidebar={false} showFooter={true} className="bg-white">
      {children}
    </Layout>
  );
};

interface AuthLayoutProps {
  children: ReactNode;
}

/**
 * Layout pentru pagini de autentificare
 */
export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">{children}</div>
    </div>
  );
};

interface ErrorLayoutProps {
  children: ReactNode;
}

/**
 * Layout pentru pagini de eroare
 */
export const ErrorLayout: React.FC<ErrorLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="text-center">{children}</div>
    </div>
  );
};

interface PrintLayoutProps {
  children: ReactNode;
}

/**
 * Layout pentru printare
 */
export const PrintLayout: React.FC<PrintLayoutProps> = ({ children }) => {
  return (
    <div className="print:block hidden">
      <div className="max-w-none mx-auto p-4">{children}</div>
    </div>
  );
};

interface UseLayoutResult {
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  toggleCollapse: () => void;
}

/**
 * Hook pentru controlul layout-ului
 */
export const useLayout = (): UseLayoutResult => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const closeSidebar = () => setSidebarOpen(false);
  const toggleCollapse = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return {
    sidebarOpen,
    sidebarCollapsed,
    toggleSidebar,
    closeSidebar,
    toggleCollapse,
  };
};

export default Layout;
