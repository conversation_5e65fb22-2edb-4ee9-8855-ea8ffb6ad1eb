const { execSync } = require('child_process');
const { join } = require('path');
const { config } = require('dotenv');

module.exports = async function globalSetup() {
  console.log('🔧 Setting up test environment...');
  
  try {
    // Încarcă variabilele de mediu din .env.test
    const envPath = join(__dirname, '../.env.test');
    config({ path: envPath });
    
    // Setează variabilele de mediu pentru teste
    process.env['NODE_ENV'] = 'test';
    process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
    process.env['JWT_REFRESH_SECRET'] = 'test-refresh-secret-key-for-testing-only';
    process.env['DATABASE_URL'] = '************************************************/expense_tracker_test';
    process.env['REDIS_URL'] = 'redis://localhost:6379/1';
    
    // Clientul Prisma ar trebui să fie deja generat
    console.log('📦 Using existing Prisma client');
    
    console.log('⚠️ Skipping database reset - ensure test database is properly configured');
    // TODO: Configure test database setup
    // execSync('npx prisma db push --force-reset', {
    //   stdio: 'inherit',
    //   cwd: join(__dirname, '..'),
    //   env: { ...process.env }
    // });
    
    console.log('✅ Test environment setup complete');
  } catch (error) {
    console.error('❌ Failed to setup test environment:', error);
    throw error;
  }
}