import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email?: string;
    role?: string;
    [key: string]: unknown;
  };
  auditId?: string;
  startTime?: number;
  sessionID?: string;
}

// Interface for response with custom data
interface AuditResponse extends Response {
  responseData?: unknown;
}

// Interface for audit data
interface AuditData {
  auditId: string;
  userId: string | null;
  userEmail: string | null;
  action: string;
  resource: string;
  resourceId: string | null;
  method: string;
  url: string;
  path: string;
  statusCode: number;
  duration: number;
  ip: string | undefined;
  userAgent: string | undefined;
  referer: string | undefined;
  timestamp: string;
  success: boolean;
  requestData: unknown;
  responseData: unknown;
  sessionId: string | null;
  metadata: {
    contentType: string | undefined;
    contentLength: string | undefined;
    acceptLanguage: string | undefined;
    origin: string | undefined;
  };
}

// Interface for admin audit data
interface AdminAuditData {
  adminId: string;
  adminEmail: string | undefined;
  action: string;
  targetResource: string;
  targetId: string | null;
  method: string;
  url: string;
  statusCode: number;
  ip: string | undefined;
  userAgent: string | undefined;
  timestamp: string;
  requestData: unknown;
  responseData: unknown;
  success: boolean;
}

// Interface for data change audit
interface DataChangeAuditData {
  userId: string | undefined;
  userEmail: string | undefined;
  model: string;
  operation: string;
  recordId: string | null;
  changes: unknown;
  timestamp: string;
  ip: string | undefined;
  userAgent: string | undefined;
}

/**
 * Middleware pentru audit trail - înregistrează toate acțiunile utilizatorilor
 */
const auditLogger = (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction): void => {
  // Generează un ID unic pentru această cerere
  req.auditId = uuidv4();
  
  // Salvează timpul de început
  req.startTime = Date.now();
  
  // Salvează metoda originală de răspuns
  const originalSend = res.send;
  const originalJson = res.json;
  
  // Interceptează răspunsul pentru a înregistra rezultatul
  res.send = function(data: unknown) {
    res.responseData = data;
    return originalSend.call(this, data);
  };
  
  res.json = function(data: unknown) {
    res.responseData = data;
    return originalJson.call(this, data);
  };
  
  // Înregistrează cererea la sfârșitul procesării
  res.on('finish', () => {
    const duration = Date.now() - (req.startTime || 0);
    
    // Determină tipul de acțiune bazat pe rută și metodă
    const action = determineAction(req.method, req.route?.path || req.path);
    
    // Extrage datele relevante pentru audit
    const auditData: AuditData = {
      auditId: req.auditId || '',
      userId: req.user?.id || null,
      userEmail: req.user?.email || null,
      action,
      resource: extractResource(req.route?.path || req.path),
      resourceId: req.params?.['id'] || null,
      method: req.method,
      url: req.originalUrl,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer'),
      timestamp: new Date().toISOString(),
      success: res.statusCode < 400,
      
      // Datele cererii (fără informații sensibile)
      requestData: sanitizeRequestData(req),
      
      // Datele răspunsului (doar pentru erori sau acțiuni importante)
      responseData: shouldLogResponse(action, res.statusCode) 
        ? sanitizeResponseData(res.responseData) 
        : null,
      
      // Informații despre sesiune
      sessionId: req.sessionID || null,
      
      // Metadate suplimentare
      metadata: {
        contentType: req.get('Content-Type'),
        contentLength: req.get('Content-Length'),
        acceptLanguage: req.get('Accept-Language'),
        origin: req.get('Origin')
      }
    };
    
    // Înregistrează în funcție de tipul de acțiune
    if (isSecuritySensitiveAction(action)) {
      logger.logSecurity('security_action', auditData);
    } else if (isBusinessCriticalAction(action)) {
      logger.logAudit('business_action', req.user?.id || 'anonymous', auditData);
    } else {
      logger.logRequest(req, res, duration);
    }
    
    // Înregistrează separat erorile
    if (res.statusCode >= 400) {
      logger.logSecurity('error_response', {
        ...auditData,
        errorType: categorizeError(res.statusCode),
        errorDetails: res.responseData
      });
    }
  });
  
  next();
};

/**
 * Determină tipul de acțiune bazat pe metodă și rută
 */
function determineAction(method: string, path: string): string {
  const pathSegments = path.split('/').filter(Boolean);
  const resource = pathSegments[1] || 'unknown'; // primul segment după /api
  
  const actionMap: Record<string, { default: string; patterns: Record<string, string> }> = {
    'GET': {
      default: 'read',
      patterns: {
        '/auth/me': 'profile_view',
        '/auth/logout': 'logout',
        '/expenses/export': 'export_data',
        '/reports': 'generate_report'
      }
    },
    'POST': {
      default: 'create',
      patterns: {
        '/auth/register': 'register',
        '/auth/login': 'login',
        '/auth/forgot-password': 'forgot_password',
        '/auth/reset-password': 'reset_password',
        '/auth/verify-email': 'verify_email',
        '/expenses/bulk': 'bulk_create_expenses',
        '/categories/bulk': 'bulk_create_categories'
      }
    },
    'PUT': {
      default: 'update',
      patterns: {
        '/auth/change-password': 'change_password',
        '/users/profile': 'update_profile'
      }
    },
    'PATCH': {
      default: 'partial_update',
      patterns: {}
    },
    'DELETE': {
      default: 'delete',
      patterns: {
        '/auth/sessions': 'logout_all_sessions'
      }
    }
  };
  
  const methodActions = actionMap[method] || { default: 'unknown', patterns: {} };
  
  // Verifică pattern-urile specifice
  for (const [pattern, action] of Object.entries(methodActions.patterns)) {
    if (path.includes(pattern)) {
      return action;
    }
  }
  
  // Returnează acțiunea implicită cu resursa
  return `${methodActions.default}_${resource}`;
}

/**
 * Extrage resursa din rută
 */
function extractResource(path: string): string {
  const pathSegments = path.split('/').filter(Boolean);
  return pathSegments[1] || 'unknown';
}

/**
 * Sanitizează datele cererii pentru a elimina informațiile sensibile
 */
function sanitizeRequestData(req: AuthenticatedRequest): unknown {
  const sensitiveFields = [
    'password', 'currentPassword', 'newPassword', 'token', 
    'apiKey', 'secret', 'authorization', 'cookie'
  ];
  
  const sanitized = {
    query: { ...req.query },
    params: { ...req.params },
    body: { ...req.body }
  };
  
  // Elimină câmpurile sensibile
  function removeSensitiveData(obj: unknown): unknown {
    if (typeof obj !== 'object' || obj === null) return obj;

    const cleaned: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        cleaned[key] = '[REDACTED]';
      } else if (typeof value === 'object') {
        cleaned[key] = removeSensitiveData(value);
      } else {
        cleaned[key] = value;
      }
    }
    return cleaned;
  }
  
  return removeSensitiveData(sanitized);
}

/**
 * Sanitizează datele răspunsului
 */
function sanitizeResponseData(data: unknown): unknown {
  if (!data) return null;
  
  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data;
    
    // Limitează dimensiunea datelor loggate
    const stringified = JSON.stringify(parsed);
    if (stringified.length > 1000) {
      return {
        message: 'Response data truncated due to size',
        size: stringified.length,
        preview: stringified.substring(0, 500) + '...'
      };
    }
    
    return parsed;
  } catch (error) {
    return { error: 'Failed to parse response data' };
  }
}

/**
 * Determină dacă răspunsul trebuie logat
 */
function shouldLogResponse(action: string, statusCode: number): boolean {
  // Loghează întotdeauna erorile
  if (statusCode >= 400) return true;
  
  // Loghează răspunsurile pentru acțiuni sensibile
  const sensitiveActions = [
    'login', 'register', 'change_password', 'reset_password',
    'delete', 'bulk_create', 'export_data'
  ];
  
  return sensitiveActions.some(sensitive => action.includes(sensitive));
}

/**
 * Verifică dacă acțiunea este sensibilă din punct de vedere al securității
 */
function isSecuritySensitiveAction(action: string): boolean {
  const securityActions = [
    'login', 'register', 'logout', 'change_password', 'reset_password',
    'forgot_password', 'verify_email', 'delete', 'logout_all_sessions'
  ];
  
  return securityActions.some(sensitive => action.includes(sensitive));
}

/**
 * Verifică dacă acțiunea este critică pentru business
 */
function isBusinessCriticalAction(action: string): boolean {
  const businessActions = [
    'create_expense', 'update_expense', 'delete_expense',
    'create_category', 'update_category', 'delete_category',
    'bulk_create', 'export_data', 'generate_report'
  ];
  
  return businessActions.some(critical => action.includes(critical));
}

/**
 * Categorizează tipul de eroare
 */
function categorizeError(statusCode: number): string {
  if (statusCode >= 400 && statusCode < 500) {
    const clientErrors: Record<number, string> = {
      400: 'bad_request',
      401: 'unauthorized',
      403: 'forbidden',
      404: 'not_found',
      409: 'conflict',
      422: 'validation_error',
      429: 'rate_limit_exceeded'
    };
    return clientErrors[statusCode] || 'client_error';
  }
  
  if (statusCode >= 500) {
    return 'server_error';
  }
  
  return 'unknown_error';
}

/**
 * Middleware pentru audit-ul acțiunilor administrative
 */
const adminAuditLogger = (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction): void => {
  // Verifică dacă utilizatorul este admin
  if (!req.user || req.user.role !== 'admin') {
    return next();
  }
  
  const originalSend = res.send;
  const originalJson = res.json;
  
  res.send = function(data: unknown) {
    res.responseData = data;
    return originalSend.call(this, data);
  };
  
  res.json = function(data: unknown) {
    res.responseData = data;
    return originalJson.call(this, data);
  };
  
  res.on('finish', () => {
    const auditData: AdminAuditData = {
      adminId: req.user!.id,
      adminEmail: req.user!.email,
      action: determineAction(req.method, req.route?.path || req.path),
      targetResource: extractResource(req.route?.path || req.path),
      targetId: req.params?.['id'] || null,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      requestData: sanitizeRequestData(req),
      responseData: sanitizeResponseData(res.responseData),
      success: res.statusCode < 400
    };
    
    logger.logAudit('admin_action', req.user!.id, auditData);
  });
  
  next();
};

/**
 * Middleware pentru audit-ul modificărilor de date
 */
const dataChangeAuditLogger = (model: string, operation: string) => {
  return (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction): void => {
    const originalSend = res.send;
    const originalJson = res.json;
    
    res.send = function(data: unknown) {
      res.responseData = data;
      return originalSend.call(this, data);
    };
    
    res.json = function(data: unknown) {
      res.responseData = data;
      return originalJson.call(this, data);
    };
    
    res.on('finish', () => {
      if (res.statusCode < 400) {
        const auditData: DataChangeAuditData = {
          userId: req.user?.id,
          userEmail: req.user?.email,
          model,
          operation,
          recordId: req.params?.['id'] || null,
          changes: operation === 'update' ? req.body : null,
          timestamp: new Date().toISOString(),
          ip: req.ip,
          userAgent: req.get('User-Agent')
        };
        
        logger.logAudit('data_change', req.user?.id || 'anonymous', auditData);
      }
    });
    
    next();
  };
};

export {
  auditLogger,
  adminAuditLogger,
  dataChangeAuditLogger
};