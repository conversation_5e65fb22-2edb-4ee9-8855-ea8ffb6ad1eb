-- Migrare pentru a trece de la ID-uri numerice la CUID și de la name la firstName/lastName
-- ATENȚIE: Această migrare trebuie executată cu grijă și după backup-ul bazei de date

-- Pasul 1: Adăugăm coloanele noi pentru firstName și lastName
ALTER TABLE users ADD COLUMN firstName VARCHAR(255);
ALTER TABLE users ADD COLUMN lastName VARCHAR(255);

-- Pasul 2: Populăm firstName și lastName din name existent
-- Presupunem că name-ul este în format "Prenume Nume" sau doar "Prenume"
UPDATE users 
SET 
  firstName = CASE 
    WHEN POSITION(' ' IN name) > 0 THEN SUBSTRING(name FROM 1 FOR POSITION(' ' IN name) - 1)
    ELSE name
  END,
  lastName = CASE 
    WHEN POSITION(' ' IN name) > 0 THEN SUBSTRING(name FROM POSITION(' ' IN name) + 1)
    ELSE ''
  END;

-- Pasul 3: Facem firstName să fie NOT NULL
ALTER TABLE users ALTER COLUMN firstName SET NOT NULL;
ALTER TABLE users ALTER COLUMN lastName SET NOT NULL;

-- Pasul 4: Adăugăm coloanele noi pentru CUID-uri
ALTER TABLE users ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE categories ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE expenses ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE plans ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE subscriptions ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE usage_logs ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;
ALTER TABLE webhook_events ADD COLUMN new_id VARCHAR(30) DEFAULT gen_random_uuid()::text;

-- Pasul 5: Populăm CUID-urile (în practică, ar trebui să folosim o funcție CUID reală)
-- Pentru demonstrație, folosim UUID-uri
UPDATE users SET new_id = 'usr_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE categories SET new_id = 'cat_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE expenses SET new_id = 'exp_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE plans SET new_id = 'pln_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE subscriptions SET new_id = 'sub_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE usage_logs SET new_id = 'log_' || substr(gen_random_uuid()::text, 1, 25);
UPDATE webhook_events SET new_id = 'whe_' || substr(gen_random_uuid()::text, 1, 25);

-- Pasul 6: Adăugăm coloanele pentru foreign key-urile noi
ALTER TABLE categories ADD COLUMN new_user_id VARCHAR(30);
ALTER TABLE expenses ADD COLUMN new_user_id VARCHAR(30);
ALTER TABLE expenses ADD COLUMN new_category_id VARCHAR(30);
ALTER TABLE expenses ADD COLUMN new_original_expense_id VARCHAR(30);
ALTER TABLE subscriptions ADD COLUMN new_user_id VARCHAR(30);
ALTER TABLE subscriptions ADD COLUMN new_plan_id VARCHAR(30);
ALTER TABLE usage_logs ADD COLUMN new_user_id VARCHAR(30);
ALTER TABLE usage_logs ADD COLUMN new_resource_id VARCHAR(30);

-- Pasul 7: Populăm foreign key-urile noi
UPDATE categories SET new_user_id = (SELECT new_id FROM users WHERE users.id = categories.user_id);
UPDATE expenses SET new_user_id = (SELECT new_id FROM users WHERE users.id = expenses.user_id);
UPDATE expenses SET new_category_id = (SELECT new_id FROM categories WHERE categories.id = expenses.category_id);
UPDATE expenses SET new_original_expense_id = (SELECT new_id FROM expenses e2 WHERE e2.id = expenses.original_expense_id);
UPDATE subscriptions SET new_user_id = (SELECT new_id FROM users WHERE users.id = subscriptions.user_id);
UPDATE subscriptions SET new_plan_id = (SELECT new_id FROM plans WHERE plans.id = subscriptions.plan_id);
UPDATE usage_logs SET new_user_id = (SELECT new_id FROM users WHERE users.id = usage_logs.user_id);
-- Pentru resource_id, ar trebui să determinăm tipul de resursă și să mapăm corespunzător

-- Pasul 8: Eliminăm constraint-urile vechi
ALTER TABLE categories DROP CONSTRAINT categories_user_id_fkey;
ALTER TABLE expenses DROP CONSTRAINT expenses_user_id_fkey;
ALTER TABLE expenses DROP CONSTRAINT expenses_category_id_fkey;
ALTER TABLE expenses DROP CONSTRAINT expenses_original_expense_id_fkey;
ALTER TABLE subscriptions DROP CONSTRAINT subscriptions_user_id_fkey;
ALTER TABLE subscriptions DROP CONSTRAINT subscriptions_plan_id_fkey;
ALTER TABLE usage_logs DROP CONSTRAINT usage_logs_user_id_fkey;

-- Pasul 9: Eliminăm coloanele vechi și redenumim cele noi
-- Users
ALTER TABLE users DROP CONSTRAINT users_pkey;
ALTER TABLE users DROP COLUMN id;
ALTER TABLE users DROP COLUMN name;
ALTER TABLE users RENAME COLUMN new_id TO id;
ALTER TABLE users ADD PRIMARY KEY (id);

-- Categories
ALTER TABLE categories DROP CONSTRAINT categories_pkey;
ALTER TABLE categories DROP COLUMN id;
ALTER TABLE categories DROP COLUMN user_id;
ALTER TABLE categories RENAME COLUMN new_id TO id;
ALTER TABLE categories RENAME COLUMN new_user_id TO user_id;
ALTER TABLE categories ADD PRIMARY KEY (id);
ALTER TABLE categories ALTER COLUMN user_id SET NOT NULL;

-- Expenses
ALTER TABLE expenses DROP CONSTRAINT expenses_pkey;
ALTER TABLE expenses DROP COLUMN id;
ALTER TABLE expenses DROP COLUMN user_id;
ALTER TABLE expenses DROP COLUMN category_id;
ALTER TABLE expenses DROP COLUMN original_expense_id;
ALTER TABLE expenses RENAME COLUMN new_id TO id;
ALTER TABLE expenses RENAME COLUMN new_user_id TO user_id;
ALTER TABLE expenses RENAME COLUMN new_category_id TO category_id;
ALTER TABLE expenses RENAME COLUMN new_original_expense_id TO original_expense_id;
ALTER TABLE expenses ADD PRIMARY KEY (id);
ALTER TABLE expenses ALTER COLUMN user_id SET NOT NULL;
ALTER TABLE expenses ALTER COLUMN category_id SET NOT NULL;

-- Plans
ALTER TABLE plans DROP CONSTRAINT plans_pkey;
ALTER TABLE plans DROP COLUMN id;
ALTER TABLE plans RENAME COLUMN new_id TO id;
ALTER TABLE plans ADD PRIMARY KEY (id);

-- Subscriptions
ALTER TABLE subscriptions DROP CONSTRAINT subscriptions_pkey;
ALTER TABLE subscriptions DROP COLUMN id;
ALTER TABLE subscriptions DROP COLUMN user_id;
ALTER TABLE subscriptions DROP COLUMN plan_id;
ALTER TABLE subscriptions RENAME COLUMN new_id TO id;
ALTER TABLE subscriptions RENAME COLUMN new_user_id TO user_id;
ALTER TABLE subscriptions RENAME COLUMN new_plan_id TO plan_id;
ALTER TABLE subscriptions ADD PRIMARY KEY (id);
ALTER TABLE subscriptions ALTER COLUMN user_id SET NOT NULL;
ALTER TABLE subscriptions ALTER COLUMN plan_id SET NOT NULL;

-- Usage Logs
ALTER TABLE usage_logs DROP CONSTRAINT usage_logs_pkey;
ALTER TABLE usage_logs DROP COLUMN id;
ALTER TABLE usage_logs DROP COLUMN user_id;
ALTER TABLE usage_logs DROP COLUMN resource_id;
ALTER TABLE usage_logs RENAME COLUMN new_id TO id;
ALTER TABLE usage_logs RENAME COLUMN new_user_id TO user_id;
ALTER TABLE usage_logs RENAME COLUMN new_resource_id TO resource_id;
ALTER TABLE usage_logs ADD PRIMARY KEY (id);
ALTER TABLE usage_logs ALTER COLUMN user_id SET NOT NULL;

-- Webhook Events
ALTER TABLE webhook_events DROP CONSTRAINT webhook_events_pkey;
ALTER TABLE webhook_events DROP COLUMN id;
ALTER TABLE webhook_events RENAME COLUMN new_id TO id;
ALTER TABLE webhook_events ADD PRIMARY KEY (id);

-- Pasul 10: Recreăm constraint-urile foreign key
ALTER TABLE categories ADD CONSTRAINT categories_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE expenses ADD CONSTRAINT expenses_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE expenses ADD CONSTRAINT expenses_category_id_fkey 
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT;

ALTER TABLE expenses ADD CONSTRAINT expenses_original_expense_id_fkey 
  FOREIGN KEY (original_expense_id) REFERENCES expenses(id) ON DELETE SET NULL;

ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_plan_id_fkey 
  FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT;

ALTER TABLE usage_logs ADD CONSTRAINT usage_logs_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Pasul 11: Recreăm indexurile
CREATE INDEX idx_categories_user_id ON categories(user_id);
CREATE INDEX idx_expenses_user_id ON expenses(user_id);
CREATE INDEX idx_expenses_category_id ON expenses(category_id);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_plan_id ON subscriptions(plan_id);
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);

-- Pasul 12: Recreăm constraint-urile unique
ALTER TABLE categories ADD CONSTRAINT categories_user_id_name_key UNIQUE (user_id, name);
ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_user_id_key UNIQUE (user_id);

-- Notă: Această migrare este complexă și ar trebui testată pe o copie a bazei de date
-- înainte de a fi aplicată în producție. De asemenea, ar trebui să existe un plan
-- de rollback în cazul în care ceva nu merge bine.