import fs from 'fs';
import path from 'path';
import * as winston from 'winston';
import { Request, Response } from 'express';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
}

// Interface for logger with custom methods
interface CustomLogger {
  stream: {
    write: (message: string) => void;
  };
  logRequest: (req: AuthenticatedRequest, res: Response, responseTime: number) => void;
  logPerformance: (operation: string, duration: number | string, metadata?: Record<string, any>) => void;
  logAudit: (action: string, userId: string, details?: Record<string, any>) => void;
  logSecurity: (event: string, details?: Record<string, any>) => void;
  logDatabase: (query: string, duration: number, metadata?: Record<string, any>) => void;
  // Winston logger methods
  info: (message: unknown, ...meta: unknown[]) => winston.Logger;
  error: (message: unknown, ...meta: unknown[]) => winston.Logger;
  warn: (message: unknown, ...meta: unknown[]) => winston.Logger;
  debug: (message: unknown, ...meta: unknown[]) => winston.Logger;
  add: (transport: winston.transport) => winston.Logger;
}

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { 
    service: 'finance-app-backend',
    environment: process.env['NODE_ENV'] || 'development'
  },
  transports: [
    // Write all logs with level 'error' and below to error.log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Write all logs to combined.log
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Log-uri de audit pentru acțiuni administrative
    new winston.transports.File({
      filename: path.join(logsDir, 'audit.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf((info) => {
          // Filtrează doar log-urile de audit
          if (info.type === 'audit') {
            return JSON.stringify(info);
          }
          return '';
        })
      )
    })
  ],
  
  // Gestionarea excepțiilor necaptate
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log')
    })
  ],

  // Gestionarea promisiunilor respinse
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log')
    })
  ]
});

// If we're not in production, log to the console with a simple format
if (process.env['NODE_ENV'] !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
    ),
  }));
}

// Create custom logger object
const customLogger: CustomLogger = {
  // Stream object for Morgan HTTP request logging
  stream: {
    write(message: string): void {
      logger.info(message.trim());
    },
  },

  // Winston logger methods
  info: (message: unknown, ...meta: unknown[]) => logger.info(message, ...meta),
  error: (message: unknown, ...meta: unknown[]) => logger.error(message, ...meta),
  warn: (message: unknown, ...meta: unknown[]) => logger.warn(message, ...meta),
  debug: (message: unknown, ...meta: unknown[]) => logger.debug(message, ...meta),
  add: (transport: winston.transport) => logger.add(transport),

  // Custom logging methods
  logRequest: (req: AuthenticatedRequest, res: Response, responseTime: number): void => {
    logger.info({
      type: 'request',
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      contentLength: res.get('Content-Length')
    });
  },

  logPerformance: (operation: string, duration: number | string, metadata: Record<string, any> = {}): void => {
    logger.info({
      type: 'performance',
      operation,
      duration: typeof duration === 'number' ? `${duration}ms` : duration,
      ...metadata
    });
  },

  logAudit: (action: string, userId: string, details: Record<string, any> = {}): void => {
    logger.info({
      type: 'audit',
      action,
      userId,
      timestamp: new Date().toISOString(),
      ...details
    });
  },

  logSecurity: (event: string, details: Record<string, any> = {}): void => {
    logger.warn({
      type: 'security',
      event,
      timestamp: new Date().toISOString(),
      ...details
    });
  },

  logDatabase: (query: string, duration: number, metadata: Record<string, any> = {}): void => {
    logger.debug({
      type: 'database',
      query: query.substring(0, 200), // Limitează lungimea query-ului
      duration: `${duration}ms`,
      ...metadata
    });
  }
};

export default customLogger;