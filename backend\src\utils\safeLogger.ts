/**
 * Sistem de logging sigur pentru înlocuirea console.log
 */

import { LogContext } from '../types/security';

// Niveluri de logging
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

// Configurația logger-ului
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableProduction: boolean;
  maxLogSize: number;
  logDirectory: string;
}

// Configurația implicită
const defaultConfig: LoggerConfig = {
  level: process.env['NODE_ENV'] === 'production' ? LogLevel.INFO : LogLevel.DEBUG,
  enableConsole: process.env['NODE_ENV'] !== 'production',
  enableFile: true,
  enableProduction: false,
  maxLogSize: 10 * 1024 * 1024, // 10MB
  logDirectory: './logs'
};

// Interfața pentru log entry
interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context?: LogContext;
  metadata?: Record<string, unknown>;
  stack?: string;
}

// Clasa principală de logging
class SafeLogger {
  private config: LoggerConfig;
  private isProduction: boolean;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.isProduction = process.env['NODE_ENV'] === 'production';
  }

  /**
   * Sanitizează datele sensibile din log-uri
   */
  private sanitizeData(data: unknown): unknown {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sensitiveKeys = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'creditCard', 'ssn', 'email'
    ];

    const sanitized = { ...data as Record<string, unknown> };
    
    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Formatează mesajul de log
   */
  private formatMessage(
    level: LogLevel,
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      context: context ? this.sanitizeData(context) as LogContext : undefined,
      metadata: metadata ? this.sanitizeData(metadata) as Record<string, unknown> : undefined
    };
  }

  /**
   * Scrie log-ul în consolă (doar în development)
   */
  private writeToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole || this.isProduction) {
      return;
    }

    const colorMap = {
      ERROR: '\x1b[31m', // Red
      WARN: '\x1b[33m',  // Yellow
      INFO: '\x1b[36m',  // Cyan
      DEBUG: '\x1b[35m', // Magenta
      TRACE: '\x1b[37m'  // White
    };

    const reset = '\x1b[0m';
    const color = colorMap[entry.level as keyof typeof colorMap] || reset;
    
    console.log(
      `${color}[${entry.timestamp}] ${entry.level}:${reset} ${entry.message}`,
      entry.context ? `\nContext: ${JSON.stringify(entry.context, null, 2)}` : '',
      entry.metadata ? `\nMetadata: ${JSON.stringify(entry.metadata, null, 2)}` : ''
    );
  }

  /**
   * Scrie log-ul în fișier (în producție)
   */
  private async writeToFile(entry: LogEntry): Promise<void> {
    if (!this.config.enableFile) {
      return;
    }

    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const logFile = path.join(
        this.config.logDirectory,
        `${entry.level.toLowerCase()}.log`
      );
      
      const logLine = JSON.stringify(entry) + '\n';
      
      await fs.appendFile(logFile, logLine, 'utf8');
    } catch (error) {
      // Fallback la console dacă nu se poate scrie în fișier
      if (!this.isProduction) {
        console.error('Failed to write to log file:', error);
      }
    }
  }

  /**
   * Metodă generică de logging
   */
  private async log(
    level: LogLevel,
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    if (level > this.config.level) {
      return;
    }

    const entry = this.formatMessage(level, message, context, metadata);
    
    this.writeToConsole(entry);
    await this.writeToFile(entry);
  }

  /**
   * Log de eroare
   */
  async error(
    message: string,
    error?: Error,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    const entry = this.formatMessage(LogLevel.ERROR, message, context, metadata);
    if (error) {
      entry.stack = error.stack;
      entry.metadata = {
        ...entry.metadata,
        errorName: error.name,
        errorMessage: error.message
      };
    }
    
    this.writeToConsole(entry);
    await this.writeToFile(entry);
  }

  /**
   * Log de avertisment
   */
  async warn(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.log(LogLevel.WARN, message, context, metadata);
  }

  /**
   * Log informativ
   */
  async info(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.log(LogLevel.INFO, message, context, metadata);
  }

  /**
   * Log de debug
   */
  async debug(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.log(LogLevel.DEBUG, message, context, metadata);
  }

  /**
   * Log de trace
   */
  async trace(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    await this.log(LogLevel.TRACE, message, context, metadata);
  }

  /**
   * Log pentru audit de securitate
   */
  async security(
    action: string,
    userId?: string,
    resource?: string,
    success: boolean = true,
    details?: Record<string, unknown>
  ): Promise<void> {
    const context: LogContext = {
      userId,
      action,
      resource,
      timestamp: new Date()
    };

    const message = `Security event: ${action} ${success ? 'succeeded' : 'failed'}`;
    
    await this.log(
      success ? LogLevel.INFO : LogLevel.WARN,
      message,
      context,
      details
    );
  }

  /**
   * Log pentru performanță
   */
  async performance(
    operation: string,
    duration: number,
    context?: LogContext,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    const message = `Performance: ${operation} completed in ${duration}ms`;
    
    await this.log(
      LogLevel.INFO,
      message,
      context,
      {
        ...metadata,
        duration,
        operation
      }
    );
  }

  /**
   * Creează un logger cu context predefinit
   */
  createContextLogger(defaultContext: LogContext): SafeLogger {
    const contextLogger = new SafeLogger(this.config);
    
    // Override metodele pentru a include contextul implicit
    const originalLog = contextLogger.log.bind(contextLogger);
    contextLogger.log = async (
      level: LogLevel,
      message: string,
      context?: LogContext,
      metadata?: Record<string, unknown>
    ) => {
      const mergedContext = { ...defaultContext, ...context };
      return originalLog(level, message, mergedContext, metadata);
    };
    
    return contextLogger;
  }
}

// Instanța globală
export const logger = new SafeLogger();

// Export pentru utilizare în alte module
export { SafeLogger };

// Funcții de conveniență pentru înlocuirea console.log
export const safeLog = {
  error: (message: string, error?: Error, context?: LogContext) => 
    logger.error(message, error, context),
  
  warn: (message: string, context?: LogContext) => 
    logger.warn(message, context),
  
  info: (message: string, context?: LogContext) => 
    logger.info(message, context),
  
  debug: (message: string, context?: LogContext) => 
    logger.debug(message, context),
  
  trace: (message: string, context?: LogContext) => 
    logger.trace(message, context),
  
  security: (action: string, userId?: string, success: boolean = true) => 
    logger.security(action, userId, undefined, success),
  
  performance: (operation: string, duration: number) => 
    logger.performance(operation, duration)
};

// Macro pentru înlocuirea console.log în development - DISABLED pentru a evita recursia
// if (process.env['NODE_ENV'] !== 'production') {
//   // Override console methods în development pentru a folosi logger-ul nostru
//   const originalConsole = {
//     log: console.log,
//     error: console.error,
//     warn: console.warn,
//     info: console.info,
//     debug: console.debug
//   };
//   
//   console.log = (...args: unknown[]) => {
//     logger.debug(args.map(arg => 
//       typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
//     ).join(' '));
//   };
// }
  
//   console.error = (...args: unknown[]) => {
//     logger.error(args.map(arg => 
//       typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
//     ).join(' '));
//   };
//   
//   console.warn = (...args: unknown[]) => {
//     logger.warn(args.map(arg => 
//       typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
//     ).join(' '));
//   };
//   
//   console.info = (...args: unknown[]) => {
//     logger.info(args.map(arg => 
//       typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
//     ).join(' '));
//   };
// }