import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
}

/**
 * Middleware pentru detectarea și logarea tentativelor de atac
 */
export const attackDetection = (req: AuthenticatedRequest, _res: Response, next: NextFunction): void => {
  const suspiciousPatterns = [
    // SQL Injection
    /('|(--)|(;)|(\||\|)|(\*|\*))/i,
    // XSS
    /(<script[^>]*>.*?<\/script>)|(<iframe[^>]*>.*?<\/iframe>)|(<object[^>]*>.*?<\/object>)/i,
    // Path traversal
    /(\.\.[/\\])|(\.\.\.)/, 
    // Command injection
    /(;\s*(rm|del|format|shutdown|reboot))/i
  ];

  const checkForAttacks = (data: unknown, source: string): boolean => {
    if (typeof data === 'string') {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(data)) {
          logger.error('Potential attack detected', {
            ip: req.ip,
            url: req.originalUrl,
            method: req.method,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            source,
            pattern: pattern.toString(),
            data: data.substring(0, 100) // Limitează datele loggate
          });
          return true;
        }
      }
    } else if (typeof data === 'object' && data !== null) {
      for (const key in data) {
        if (checkForAttacks((data as Record<string, unknown>)[key], `${source}.${key}`)) {
          return true;
        }
      }
    }
    return false;
  };

  // Verifică parametrii URL
  checkForAttacks(req.query, 'query');
  
  // Verifică body-ul cererii
  checkForAttacks(req.body, 'body');
  
  // Verifică parametrii rutei
  checkForAttacks(req.params, 'params');

  next();
};

export default attackDetection;