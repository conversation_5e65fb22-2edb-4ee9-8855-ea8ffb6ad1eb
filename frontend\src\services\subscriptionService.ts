import api from './api';
import type {
  SubscriptionPlan,
  ApiResponse,
  Subscription,
  CheckoutSession,
  UsageStats,
  Permission,
} from '../types';
import { safeLog } from '../utils/safeLogger';

interface CustomerPortal {
  url: string;
}

/**
 * Serviciu pentru gestionarea abonamentelor utilizatorilor finali
 */
class SubscriptionService {
  /**
   * Obține planurile disponibile
   */
  async getPlans(): Promise<ApiResponse<SubscriptionPlan[]>> {
    try {
      const response = await api.get('/subscriptions/plans');
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la obținerea planurilor:', error as Error);
      throw error;
    }
  }

  /**
   * Obține abonamentul curent al utilizatorului
   */
  async getCurrentSubscription(): Promise<ApiResponse<Subscription | null>> {
    try {
      const response = await api.get('/subscriptions/current');
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la obținerea abonamentului curent:', error as Error);
      throw error;
    }
  }

  /**
   * Creează o sesiune de checkout pentru un plan
   */
  async createCheckoutSession(
    planId: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    successUrl: string,
    cancelUrl: string,
  ): Promise<ApiResponse<CheckoutSession>> {
    try {
      const response = await api.post('/subscriptions/checkout', {
        planId,
        billingCycle,
        successUrl,
        cancelUrl,
      });
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la crearea sesiunii de checkout:', error as Error);
      throw error;
    }
  }

  /**
   * Creează un portal pentru gestionarea abonamentului
   */
  async createCustomerPortal(returnUrl: string): Promise<ApiResponse<CustomerPortal>> {
    try {
      const response = await api.post('/subscriptions/portal', {
        returnUrl,
      });
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la crearea portalului de clienți:', error as Error);
      throw error;
    }
  }

  /**
   * Anulează abonamentul curent
   */
  async cancelSubscription(reason: string = ''): Promise<ApiResponse<Subscription>> {
    try {
      const response = await api.post('/subscriptions/cancel', {
        reason,
      });
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la anularea abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Reactivează un abonament anulat
   */
  async reactivateSubscription(): Promise<ApiResponse<Subscription>> {
    try {
      const response = await api.post('/subscriptions/reactivate');
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la reactivarea abonamentului:', error as Error);
      throw error;
    }
  }

  /**
   * Obține statisticile de utilizare ale utilizatorului
   */
  async getUsageStats(): Promise<ApiResponse<UsageStats>> {
    try {
      const response = await api.get('/subscriptions/usage');
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la obținerea statisticilor de utilizare:', error as Error);
      throw error;
    }
  }

  /**
   * Verifică statusul unei sesiuni de checkout
   */
  async checkCheckoutSession(sessionId: string): Promise<ApiResponse<CheckoutSession>> {
    try {
      const response = await api.get(`/subscriptions/checkout/${sessionId}`);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la verificarea sesiunii de checkout:', error as Error);
      throw error;
    }
  }

  /**
   * Verifică permisiunile utilizatorului pentru o anumită acțiune
   */
  async checkPermission(action: string): Promise<ApiResponse<Permission>> {
    try {
      const response = await api.get(`/subscriptions/permissions/${action}`);
      return response.data;
    } catch (error) {
      safeLog.error('Eroare la verificarea permisiunilor:', error as Error);
      throw error;
    }
  }
}

const subscriptionService = new SubscriptionService();
export default subscriptionService;
