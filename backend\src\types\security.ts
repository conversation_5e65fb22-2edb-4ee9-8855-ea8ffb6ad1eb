/**
 * Tipuri de securitate pentru înlocuirea tipurilor `any`
 */

// Tipuri pentru request și response
export interface SafeRequest {
  body?: Record<string, unknown>;
  query?: Record<string, string | string[]>;
  params?: Record<string, string>;
  headers?: Record<string, string>;
  user?: {
    id: string;
    email: string;
    role: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface SafeResponse {
  status: (code: number) => SafeResponse;
  json: (data: Record<string, unknown>) => SafeResponse;
  send: (data: string | Buffer) => SafeResponse;
  [key: string]: unknown;
}

// Tipuri pentru error handling
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: ValidationError[];
  stack?: string;
}

// Tipuri pentru middleware
export interface MiddlewareContext {
  req: SafeRequest;
  res: SafeResponse;
  next: () => void;
}

// Tipuri pentru database queries
export interface DatabaseQuery {
  where?: Record<string, unknown>;
  select?: Record<string, boolean>;
  include?: Record<string, boolean | DatabaseQuery>;
  orderBy?: Record<string, 'asc' | 'desc'>;
  take?: number;
  skip?: number;
}

// Tipuri pentru logging
export interface LogContext {
  userId?: string;
  action: string;
  resource?: string;
  ip?: string;
  userAgent?: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

// Tipuri pentru audit
export interface AuditEvent {
  id: string;
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ip: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

// Tipuri pentru cache
export interface CacheEntry {
  key: string;
  value: unknown;
  ttl?: number;
  createdAt: Date;
  expiresAt?: Date;
}

// Tipuri pentru webhook events
export interface WebhookEvent {
  id: string;
  type: string;
  data: Record<string, unknown>;
  timestamp: Date;
  source: string;
  signature?: string;
}

// Tipuri pentru Stripe events
export interface StripeSubscription {
  id: string;
  customer: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  currentPeriodStart: number;
  currentPeriodEnd: number;
  plan?: {
    id: string;
    nickname?: string;
    amount: number;
    currency: string;
    interval: 'day' | 'week' | 'month' | 'year';
  };
  metadata?: Record<string, string>;
}

export interface StripeCustomer {
  id: string;
  email?: string;
  name?: string;
  metadata?: Record<string, string>;
  created: number;
}

export interface StripeInvoice {
  id: string;
  customer: string;
  subscription?: string;
  amountPaid: number;
  amountDue: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void';
  created: number;
}

// Tipuri pentru transformări de date
export interface CaseTransformOptions {
  deep?: boolean;
  excludeKeys?: string[];
  includeKeys?: string[];
}

export interface TransformResult<T = unknown> {
  data: T;
  transformed: boolean;
  errors?: string[];
}

// Tipuri pentru validare
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  data?: Record<string, unknown>;
}

// Tipuri pentru paginare
export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T = unknown> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Tipuri pentru export
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'json';
  fields?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: Record<string, unknown>;
}

export interface ExportResult {
  filename: string;
  mimeType: string;
  size: number;
  recordCount: number;
  buffer: Buffer;
}

// Tipuri pentru statistici
export interface StatsQuery {
  userId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  groupBy?: 'day' | 'week' | 'month' | 'year';
  categories?: string[];
}

export interface StatsResult {
  totalAmount: number;
  totalCount: number;
  averageAmount: number;
  categoryBreakdown: Array<{
    category: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    amount: number;
    count: number;
  }>;
}

// Tipuri pentru JWT și autentificare
export interface JwtPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
  [key: string]: unknown;
}

export interface JwtTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean;
  lastLoginAt?: Date;
  permissions?: string[];
  [key: string]: unknown;
}

export interface AuthenticatedRequest extends SafeRequest {
  user: AuthenticatedUser;
  token?: string;
}

// Tipuri pentru Rate Limiting
export interface RateLimitConfig {
  windowMs: number;
  max: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: SafeRequest) => string;
}

export interface RateLimitInfo {
  limit: number;
  current: number;
  remaining: number;
  resetTime: Date;
}

// Tipuri pentru CORS
export interface CorsConfig {
  origin: string | string[] | boolean | ((origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void);
  methods?: string | string[];
  allowedHeaders?: string | string[];
  exposedHeaders?: string | string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
  optionsSuccessStatus?: number;
}

export type CorsCallback = (err: Error | null, allow?: boolean) => void;

// Tipuri pentru Security Headers
export interface SecurityHeaders {
  contentSecurityPolicy?: Record<string, string | string[]>;
  crossOriginEmbedderPolicy?: boolean | string;
  crossOriginOpenerPolicy?: boolean | string;
  crossOriginResourcePolicy?: boolean | string;
  dnsPrefetchControl?: boolean | Record<string, unknown>;
  frameguard?: boolean | Record<string, unknown>;
  hidePoweredBy?: boolean;
  hsts?: boolean | Record<string, unknown>;
  ieNoOpen?: boolean;
  noSniff?: boolean;
  originAgentCluster?: boolean;
  permittedCrossDomainPolicies?: boolean | string;
  referrerPolicy?: boolean | string | string[];
  xssFilter?: boolean | Record<string, unknown>;
}

// Tipuri pentru evenimente de securitate
export interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'login_success' | 'login_failure' | 'password_change' | 'account_locked' | 'suspicious_activity' | 'data_access' | 'permission_change';
  userId?: string;
  ip: string;
  userAgent: string;
  timestamp: Date;
  details: Record<string, unknown>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface SecurityAuditReport {
  id: string;
  generatedAt: Date;
  generatedBy: string;
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalEvents: number;
    criticalEvents: number;
    highSeverityEvents: number;
    resolvedEvents: number;
    pendingEvents: number;
  };
  events: SecurityEvent[];
  recommendations: string[];
  systemIntegrity: {
    jwtSecretLength: number;
    bcryptRounds: number;
    httpsEnabled: boolean;
    corsConfigured: boolean;
    rateLimitingEnabled: boolean;
  };
}

// Tipuri pentru managementul sesiunilor
export interface SessionData {
  userId: string;
  email: string;
  role: string;
  loginAt: Date;
  lastActivity: Date;
  ip: string;
  userAgent: string;
  isActive: boolean;
  expiresAt: Date;
  metadata?: Record<string, unknown>;
}

export interface SessionConfig {
  secret: string;
  name?: string;
  maxAge?: number;
  secure?: boolean;
  httpOnly?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  rolling?: boolean;
  saveUninitialized?: boolean;
  resave?: boolean;
}

// Tipuri pentru detectarea atacurilor
export interface AttackPattern {
  type: 'sql_injection' | 'xss' | 'path_traversal' | 'command_injection' | 'brute_force' | 'ddos';
  pattern: string | RegExp;
  severity: 'low' | 'medium' | 'high' | 'critical';
  action: 'log' | 'block' | 'rate_limit';
  description: string;
}

export interface AttackDetectionResult {
  detected: boolean;
  patterns: AttackPattern[];
  confidence: number;
  recommendation: string;
  shouldBlock: boolean;
}

// Type guards pentru validare runtime
export function isValidRequest(obj: unknown): obj is SafeRequest {
  return typeof obj === 'object' && obj !== null;
}

export function isValidError(obj: unknown): obj is ApiError {
  return typeof obj === 'object' && obj !== null && 'message' in obj;
}

export function isValidWebhookEvent(obj: unknown): obj is WebhookEvent {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'type' in obj &&
    'data' in obj
  );
}

export function isValidJwtPayload(obj: unknown): obj is JwtPayload {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'userId' in obj &&
    'email' in obj &&
    'role' in obj
  );
}

export function isValidSecurityEvent(obj: unknown): obj is SecurityEvent {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'type' in obj &&
    'ip' in obj &&
    'timestamp' in obj &&
    'severity' in obj
  );
}

export function isValidSessionData(obj: unknown): obj is SessionData {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'userId' in obj &&
    'email' in obj &&
    'loginAt' in obj &&
    'isActive' in obj
  );
}

export function isAuthenticatedRequest(obj: unknown): obj is AuthenticatedRequest {
  return (
    isValidRequest(obj) &&
    'user' in obj &&
    typeof obj.user === 'object' &&
    obj.user !== null &&
    'id' in obj.user &&
    'email' in obj.user &&
    'role' in obj.user
  );
}