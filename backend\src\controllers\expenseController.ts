import { prisma } from '../config/prisma';
import { Request, Response } from 'express';
import { Prisma } from '@prisma/client';
// Note: subscriptionService and usageService imports will be added when those services are created
// import { subscriptionService } from '../services/subscriptionService';

import { safeLog } from '../utils/safeLogger';
// import { logger } from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    [key: string]: unknown;
  };
  body: any; // Allow any body type for flexibility
}

// Helper function to get userId safely
const getUserId = (req: AuthenticatedRequest): string => {
  const userId = req.user?.id;
  if (!userId) {
    throw new Error('User not authenticated');
  }
  return userId;
};

// Get all expenses for the authenticated user
const getExpenses = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      categoryId,
      paymentMethod,
      tags,
      minAmount,
      maxAmount,
      search,
      sortBy = 'date',
      sortOrder = 'DESC',
      isRecurring,
    } = req.query;

    // Convert query parameters to proper types
    const pageNum = typeof page === 'string' ? parseInt(page) : 1;
    const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
    const paymentMethodStr = typeof paymentMethod === 'string' ? paymentMethod : undefined;
    const tagsStr = typeof tags === 'string' ? tags : undefined;
    const minAmountStr = typeof minAmount === 'string' ? minAmount : undefined;
    const maxAmountStr = typeof maxAmount === 'string' ? maxAmount : undefined;
    const searchStr = typeof search === 'string' ? search : undefined;
    const sortByStr = typeof sortBy === 'string' ? sortBy : 'date';
    const sortOrderStr = typeof sortOrder === 'string' ? sortOrder : 'DESC';

    const offset = (pageNum - 1) * limitNum;

    // Build where clause
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
    };

    if (startDateStr && endDateStr) {
      where.date = {
        gte: new Date(startDateStr),
        lte: new Date(endDateStr),
      };
    }

    if (categoryIdStr) {
      where.categoryId = categoryIdStr;
    }

    if (paymentMethodStr) {
      where.paymentMethod = paymentMethodStr;
    }

    if (minAmountStr !== undefined || maxAmountStr !== undefined) {
      where.amount = {};
      if (minAmountStr !== undefined) where.amount.gte = parseFloat(minAmountStr);
      if (maxAmountStr !== undefined) where.amount.lte = parseFloat(maxAmountStr);
    }

    if (searchStr) {
      where.OR = [
        { description: { contains: searchStr, mode: 'insensitive' } },
        { notes: { contains: searchStr, mode: 'insensitive' } },
      ];
    }

    if (tagsStr) {
      const tagsArray = tagsStr.split(',');
      where.tags = {
        hasSome: tagsArray,
      };
    }

    // Get total count
    const count = await prisma.expense.count({ where });

    // Get expenses with pagination
    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
      orderBy: {
        [sortByStr]: sortOrderStr.toLowerCase(),
      },
      skip: offset,
      take: limitNum,
    });

    const result = { rows: expenses, count };

    return res.json({
      success: true,
      data: {
        expenses: result.rows,
        pagination: {
          currentPage: pageNum,
          perPage: limitNum,
          totalItems: result.count,
          totalPages: Math.ceil(result.count / limitNum),
          hasNext: pageNum * limitNum < result.count,
          hasPrev: pageNum > 1,
        },
      },
    });
  } catch (error) {
    safeLog.error('Get expenses error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expenses',
    });
  }
};

// Get a single expense by ID
const getExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);

    const expense = await prisma.expense.findFirst({
      where: {
        id: id!,
        userId: String(userId),
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    return res.json({
      success: true,
      data: {
        expense,
      },
    });
  } catch (error) {
    safeLog.error('Get expense error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expense',
    });
  }
};

// Create a new expense
const createExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);

    // TODO: Implement subscription service check
    // Check if user can create expenses based on their subscription
    // const canCreate = await subscriptionService.canUserPerformAction(userId, 'create_expense');
    // if (!canCreate) {
    //   const usageProgress = await usageService.getUsageProgress(userId);
    //   return res.status(403).json({
    //     success: false,
    //     message: 'Monthly expense limit reached. Upgrade your plan to create more expenses.',
    //     code: 'EXPENSE_LIMIT_REACHED',
    //     data: {
    //       usage: usageProgress,
    //     },
    //   });
    // }

    const expenseData = {
      ...req.body,
      userId: String(userId),
    };

    // Verify that the category belongs to the user
    const category = await prisma.category.findFirst({
      where: {
        id: expenseData.categoryId,
        userId: String(userId),
        isActive: true,
      } as any, // Type assertion to handle Prisma strict types
    });

    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category or category does not belong to you',
      });
    }

    const expense = await prisma.expense.create({
      data: expenseData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    // Increment usage counter
    // await usageService.incrementExpenseCount(userId);

    const createdExpense = expense;

    return res.status(201).json({
      success: true,
      message: 'Expense created successfully',
      data: {
        expense: createdExpense,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Create expense error:', error as Error);

    if ((error as any).code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed - duplicate entry',
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while creating expense',
    });
  }
};

// Update an expense
const updateExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);
    const updateData = req.body;

    const expense = await prisma.expense.findFirst({
      where: {
        id: id!,
        userId: String(userId),
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // If category is being updated, verify it belongs to the user
    if (updateData.categoryId) {
      const category = await prisma.category.findFirst({
        where: {
          id: updateData.categoryId,
          userId: String(userId),
          isActive: true,
        },
      });

      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Invalid category or category does not belong to you',
        });
      }
    }

    const updatedExpense = await prisma.expense.update({
      where: { id: expense.id },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    return res.json({
      success: true,
      message: 'Expense updated successfully',
      data: {
        expense: updatedExpense,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Update expense error:', error as Error);

    if ((error as any).code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed - duplicate entry',
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while updating expense',
    });
  }
};

// Delete an expense
const deleteExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);

    const expense = await prisma.expense.findFirst({
      where: {
        id: id!,
        userId: String(userId),
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    await prisma.expense.delete({
      where: { id: expense.id },
    });

    // Decrement usage counter
    // await usageService.decrementExpenseCount(userId);

    return res.json({
      success: true,
      message: 'Expense deleted successfully',
    });
  } catch (error) {
    safeLog.error('Delete expense error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while deleting expense',
    });
  }
};

// Get expense statistics
const getExpenseStats = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { period = 'monthly', startDate, endDate } = req.query;

    // Convert query parameters to proper types
    const periodStr = typeof period === 'string' ? period : 'monthly';
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;

    let calculatedStartDate, calculatedEndDate;

    if (startDateStr && endDateStr) {
      calculatedStartDate = new Date(startDateStr);
      calculatedEndDate = new Date(endDateStr);
    } else {
      const now = new Date();
      switch (periodStr) {
        case 'daily':
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
          break;
        case 'yearly':
          calculatedStartDate = new Date(now.getFullYear(), 0, 1);
          calculatedEndDate = new Date(now.getFullYear() + 1, 0, 1);
          break;
        default: // monthly
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      }
    }

    // Get total expenses and count
    const totalExpensesResult = await prisma.expense.aggregate({
      where: {
        userId: String(userId),
        date: {
          gte: calculatedStartDate,
          lte: calculatedEndDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    const totalExpenses = totalExpensesResult._sum?.amount || 0;
    const expenseCount = totalExpensesResult._count?.id || 0;

    // Get expenses by category
    const expensesByCategory = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: {
        userId: String(userId),
        date: {
          gte: calculatedStartDate,
          lte: calculatedEndDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    // Get category names for the grouped results
    const categoryIds = expensesByCategory.map(item => item.categoryId);
    const categories = await prisma.category.findMany({
      where: {
        id: { in: categoryIds },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat.id] = cat.name;
      return acc;
    }, {} as Record<string, string>);

    const formattedByCategory = expensesByCategory.map(item => ({
      category: categoryMap[item.categoryId] || 'Unknown',
      categoryId: item.categoryId,
      totalAmount: item._sum.amount || 0,
      count: item._count.id,
    }));

    // Get expenses by payment method
    const expensesByPaymentMethod = await prisma.expense.groupBy({
      by: ['paymentMethod'],
      where: {
        userId: String(userId),
        date: {
          gte: calculatedStartDate,
          lte: calculatedEndDate,
        },
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _sum: {
          amount: 'desc',
        },
      },
    });

    const formattedByPaymentMethod = expensesByPaymentMethod.map(item => ({
      paymentMethod: item.paymentMethod,
      totalAmount: item._sum.amount || 0,
      count: item._count.id,
    }));

    // Get daily expenses for the period
    const dailyExpenses = await prisma.expense.groupBy({
      by: ['date'],
      where: {
        userId: String(userId),
        date: {
          gte: calculatedStartDate,
          lte: calculatedEndDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        date: 'asc',
      },
    });

    // Get popular tags - simplified version
    const expensesWithTags = await prisma.expense.findMany({
      where: {
        userId: String(userId),
        tags: {
          not: {} as any,
        },
      } as any,
      select: {
        tags: true,
      },
    });

    // Process tags to get popular ones
    const tagCounts: Record<string, number> = {};
    expensesWithTags.forEach(expense => {
      if (expense.tags && Array.isArray(expense.tags)) {
        const tags = expense.tags as any;
        if (Array.isArray(tags)) {
          tags.forEach((tag: unknown) => {
            if (typeof tag === 'string') {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            }
          });
        }
      }
    });

    const popularTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return res.json({
      success: true,
      data: {
        period: {
          type: periodStr,
          startDate: calculatedStartDate,
          endDate: calculatedEndDate,
        },
        summary: {
          totalExpenses: parseFloat(totalExpenses.toString()),
          expenseCount: expenseCount,
          averageExpense: expenseCount > 0 ? parseFloat(totalExpenses.toString()) / expenseCount : 0,
          dailyAverage: dailyExpenses.length > 0 ? parseFloat(totalExpenses.toString()) / dailyExpenses.length : 0,
        },
        byCategory: formattedByCategory,
        byPaymentMethod: formattedByPaymentMethod,
        dailyExpenses: dailyExpenses,
        popularTags: popularTags,
      },
    });
  } catch (error) {
    safeLog.error('Get expense stats error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expense statistics',
    });
  }
};

// Get monthly trends
const getMonthlyTrends = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { months = 12 } = req.query;

    // Convert query parameters to proper types
    const monthsNum = typeof months === 'string' ? parseInt(months) : 12;

    // Get monthly trends
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - monthsNum);

    const trends = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', date) as month,
        SUM(amount) as totalAmount,
        COUNT(*) as expenseCount,
        AVG(amount) as averageAmount
      FROM "Expense"
      WHERE userId = ${String(userId)}
        AND date >= ${startDate}
        AND date <= ${endDate}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;

    return res.json({
      success: true,
      data: {
        trends,
      },
    });
  } catch (error) {
    safeLog.error('Get monthly trends error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching monthly trends',
    });
  }
};

// Add tag to expense
const addTag = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { tag } = req.body;
    const userId = getUserId(req);

    if (!tag || typeof tag !== 'string' || tag.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Tag is required and must be a non-empty string',
      });
    }

    const expense = await prisma.expense.findFirst({
      where: {
        id: id!,
        userId: String(userId),
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // Add tag to expense
    const currentTags = Array.isArray(expense.tags) ? expense.tags : [];
    const trimmedTag = tag.trim().toLowerCase();

    const tagExists = currentTags.some((t: unknown) => typeof t === 'string' && t === trimmedTag);
    if (!tagExists) {
      const updatedTags = [...currentTags, trimmedTag];
      await prisma.expense.update({
        where: { id: expense.id },
        data: { tags: updatedTags },
      });
    }

    // Get updated expense
    const updatedExpense = await prisma.expense.findUnique({
      where: { id: expense.id },
      select: { tags: true },
    });

    return res.json({
      success: true,
      message: 'Tag added successfully',
      data: {
        tags: updatedExpense?.tags || [],
      },
    });
  } catch (error) {
    safeLog.error('Add tag error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while adding tag',
    });
  }
};

// Remove tag from expense
const removeTag = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { tag } = req.body;
    const userId = getUserId(req);

    if (!tag || typeof tag !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Tag is required and must be a string',
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Expense ID is required',
      });
    }

    const expense = await prisma.expense.findFirst({
      where: {
        id: id!,
        userId: String(userId),
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // Remove tag from expense
    const currentTags = Array.isArray(expense.tags) ? expense.tags : [];
    const trimmedTag = tag.trim().toLowerCase();
    const updatedTags = currentTags.filter((t: unknown) => typeof t === 'string' && t !== trimmedTag);

    await prisma.expense.update({
      where: { id: expense.id },
      data: { tags: updatedTags },
    });

    // Get updated expense
    const updatedExpense = await prisma.expense.findUnique({
      where: { id: expense.id },
      select: { tags: true },
    });

    return res.json({
      success: true,
      message: 'Tag removed successfully',
      data: {
        tags: updatedExpense?.tags || [],
      },
    });
  } catch (error) {
    safeLog.error('Remove tag error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while removing tag',
    });
  }
};

// Get popular tags
const getPopularTags = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { limit = 20 } = req.query;

    // Convert query parameters to proper types
    const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;

    // Get popular tags
    const expensesWithTags = await prisma.expense.findMany({
      where: {
        userId: String(userId),
        tags: {
          not: null as any,
        },
      },
      select: {
        tags: true,
      },
    });

    // Process tags to get popular ones
    const tagCounts: Record<string, number> = {};
    expensesWithTags.forEach(expense => {
      if (expense.tags && Array.isArray(expense.tags)) {
        (expense.tags as unknown[]).forEach((tag: unknown) => {
          if (typeof tag === 'string') {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          }
        });
      }
    });

    const tags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limitNum);

    return res.json({
      success: true,
      data: {
        tags,
      },
    });
  } catch (error) {
    safeLog.error('Get popular tags error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching popular tags',
    });
  }
};

// Bulk delete expenses
const bulkDeleteExpenses = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { expenseIds } = req.body;
    const userId = getUserId(req);

    if (!Array.isArray(expenseIds) || expenseIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'expenseIds must be a non-empty array',
      });
    }

    // Verify all expenses belong to the user
    const expenses = await prisma.expense.findMany({
      where: {
        id: { in: expenseIds },
        userId: String(userId),
      },
    });

    if (expenses.length !== expenseIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Some expenses do not exist or do not belong to you',
      });
    }

    // Delete expenses
    const deleteResult = await prisma.expense.deleteMany({
      where: {
        id: { in: expenseIds },
        userId: String(userId),
      },
    });

    const deletedCount = deleteResult.count;

    return res.json({
      success: true,
      message: `${deletedCount} expenses deleted successfully`,
      data: {
        deletedCount: deletedCount,
      },
    });
  } catch (error) {
    safeLog.error('Bulk delete expenses error:', error as Error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while deleting expenses',
    });
  }
};

export const expenseController = {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats,
  getMonthlyTrends,
  addTag,
  removeTag,
  getPopularTags,
  bulkDeleteExpenses,
};
