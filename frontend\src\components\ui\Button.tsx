import React from 'react';

import { cn } from '../../utils/helpers';

import LoadingSpinner from './LoadingSpinner';

type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'ghost'
  | 'danger'
  | 'success'
  | 'warning'
  | 'white';
type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type ButtonType = 'button' | 'submit' | 'reset';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  type?: ButtonType;
}

/**
 * Componenta Button reutilizabilă cu multiple variante și stări
 */
const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon = null,
  rightIcon = null,
  className = '',
  onClick,
  type = 'button',
  ...rest
}) => {
  const buttonType: 'button' | 'submit' | 'reset' = type || 'button';
  // Clase pentru variante
  const variantClasses = {
    primary: [
      'bg-primary-600 text-white border-primary-600',
      'hover:bg-primary-700 hover:border-primary-700',
      'focus:ring-primary-500',
      'disabled:bg-primary-300 disabled:border-primary-300',
    ].join(' '),
    secondary: [
      'bg-gray-600 text-white border-gray-600',
      'hover:bg-gray-700 hover:border-gray-700',
      'focus:ring-gray-500',
      'disabled:bg-gray-300 disabled:border-gray-300',
    ].join(' '),
    outline: [
      'bg-transparent text-primary-600 border-primary-600',
      'hover:bg-primary-50 hover:text-primary-700',
      'focus:ring-primary-500',
      'disabled:text-primary-300 disabled:border-primary-300',
    ].join(' '),
    ghost: [
      'bg-transparent text-gray-700 border-transparent',
      'hover:bg-gray-100 hover:text-gray-900',
      'focus:ring-gray-500',
      'disabled:text-gray-400',
    ].join(' '),
    danger: [
      'bg-red-600 text-white border-red-600',
      'hover:bg-red-700 hover:border-red-700',
      'focus:ring-red-500',
      'disabled:bg-red-300 disabled:border-red-300',
    ].join(' '),
    success: [
      'bg-green-600 text-white border-green-600',
      'hover:bg-green-700 hover:border-green-700',
      'focus:ring-green-500',
      'disabled:bg-green-300 disabled:border-green-300',
    ].join(' '),
    warning: [
      'bg-yellow-600 text-white border-yellow-600',
      'hover:bg-yellow-700 hover:border-yellow-700',
      'focus:ring-yellow-500',
      'disabled:bg-yellow-300 disabled:border-yellow-300',
    ].join(' '),
    white: [
      'bg-white text-gray-900 border-white',
      'hover:bg-gray-50 hover:text-gray-900',
      'focus:ring-gray-500',
      'disabled:bg-gray-100 disabled:text-gray-400',
    ].join(' '),
  };

  // Clase pentru mărimi
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  };

  // Clase pentru iconițe în funcție de mărime
  const iconSizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
    xl: 'h-6 w-6',
  };

  const isDisabled = disabled || loading;

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isDisabled) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  return (
    <button
      type={buttonType}
      onClick={handleClick}
      disabled={isDisabled}
      className={cn(
        // Clase de bază
        'inline-flex items-center justify-center',
        'border font-medium rounded-lg',
        'transition-all duration-200 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:cursor-not-allowed disabled:opacity-60',

        // Varianta
        variantClasses[variant],

        // Mărimea
        sizeClasses[size],

        // Lățime completă
        fullWidth && 'w-full',

        // Clase personalizate
        className,
      )}
      {...rest}
    >
      {/* Iconița din stânga */}
      {leftIcon && !loading && (
        <span className={cn(iconSizeClasses[size], children && 'mr-2')}>{leftIcon}</span>
      )}

      {/* Spinner de încărcare */}
      {loading && (
        <span className={cn(children && 'mr-2')}>
          <LoadingSpinner
            size={size === 'xs' || size === 'sm' ? 'sm' : 'md'}
            color="currentColor"
          />
        </span>
      )}

      {/* Conținutul butonului */}
      {children && <span className={loading ? 'opacity-70' : ''}>{children}</span>}

      {/* Iconița din dreapta */}
      {rightIcon && !loading && (
        <span className={cn(iconSizeClasses[size], children && 'ml-2')}>{rightIcon}</span>
      )}
    </button>
  );
};

/**
 * Buton primar - varianta principală
 */
export const PrimaryButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="primary" {...props} />
);

/**
 * Buton secundar
 */
export const SecondaryButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="secondary" {...props} />
);

/**
 * Buton cu contur
 */
export const OutlineButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="outline" {...props} />
);

/**
 * Buton transparent
 */
export const GhostButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="ghost" {...props} />
);

/**
 * Buton de pericol
 */
export const DangerButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="danger" {...props} />
);

/**
 * Buton de succes
 */
export const SuccessButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="success" {...props} />
);

/**
 * Buton de avertizare
 */
export const WarningButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant="warning" {...props} />
);

interface IconButtonProps extends Omit<ButtonProps, 'children'> {
  icon: React.ReactNode;
}

/**
 * Buton pentru iconițe - rotund și fără text
 */
export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  size = 'md',
  variant = 'ghost',
  className = '',
  ...props
}) => {
  const iconSizes = {
    xs: 'p-1',
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
    xl: 'p-4',
  };

  return (
    <Button variant={variant} className={cn('rounded-full', iconSizes[size], className)} {...props}>
      {icon}
    </Button>
  );
};

interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
}

/**
 * Grup de butoane
 */
export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className = '',
  orientation = 'horizontal',
  ...props
}) => {
  return (
    <div
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>button]:rounded-none',
        '[&>button:first-child]:rounded-l-lg',
        '[&>button:last-child]:rounded-r-lg',
        orientation === 'vertical' && [
          '[&>button:first-child]:rounded-t-lg [&>button:first-child]:rounded-l-none',
          '[&>button:last-child]:rounded-b-lg [&>button:last-child]:rounded-r-none',
        ],
        '[&>button:not(:first-child)]:border-l-0',
        orientation === 'vertical' &&
          '[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0',
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Buton de tip link
 */
export const LinkButton: React.FC<Omit<ButtonProps, 'variant'>> = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <Button
      variant="ghost"
      className={cn(
        'p-0 h-auto font-normal text-primary-600 hover:text-primary-700 hover:bg-transparent hover:underline',
        className,
      )}
      {...props}
    >
      {children}
    </Button>
  );
};

interface FloatingActionButtonProps extends Omit<ButtonProps, 'children'> {
  icon: React.ReactNode;
}

/**
 * Buton floating action (FAB)
 */
export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  className = '',
  ...props
}) => {
  return (
    <Button
      variant="primary"
      className={cn(
        'fixed bottom-6 right-6 rounded-full p-4 shadow-lg hover:shadow-xl z-50',
        className,
      )}
      {...props}
    >
      {icon}
    </Button>
  );
};

export default Button;
