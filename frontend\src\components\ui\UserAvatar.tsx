import React from 'react';
import { UserIcon } from '@heroicons/react/24/outline';
import { StarIcon } from '@heroicons/react/24/solid';
import { cn } from '../../utils/cn';

interface UserAvatarProps {
  user: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    subscription?: {
      plan?: {
        name?: string;
      };
    };
  } | null;
  size?: 'sm' | 'md' | 'lg';
  showBadge?: boolean;
  className?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'md',
  showBadge = true,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
  };

  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-6 w-6',
  };

  const badgeSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const badgePositionClasses = {
    sm: '-bottom-0.5 -right-0.5',
    md: '-bottom-1 -right-1',
    lg: '-bottom-1 -right-1',
  };

  const getPlanBadge = () => {
    const planName = user?.subscription?.plan?.name?.toLowerCase();
    
    switch (planName) {
      case 'premium':
        return (
          <div className={cn(
            'absolute rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 flex items-center justify-center shadow-sm border-2 border-white',
            badgeSizeClasses[size],
            badgePositionClasses[size]
          )}>
            <svg 
              className={cn('text-white', {
                'h-2 w-2': size === 'sm',
                'h-2.5 w-2.5': size === 'md',
                'h-3 w-3': size === 'lg',
              })}
              fill="currentColor" 
              viewBox="0 0 24 24"
            >
              <path d="M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z" />
            </svg>
          </div>
        );
      case 'basic':
        return (
          <div className={cn(
            'absolute rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center shadow-sm border-2 border-white',
            badgeSizeClasses[size],
            badgePositionClasses[size]
          )}>
            <StarIcon className={cn('text-white', {
              'h-2 w-2': size === 'sm',
              'h-2.5 w-2.5': size === 'md',
              'h-3 w-3': size === 'lg',
            })} />
          </div>
        );
      case 'free':
        return (
          <div className={cn(
            'absolute rounded-full bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center shadow-sm border-2 border-white',
            badgeSizeClasses[size],
            badgePositionClasses[size]
          )}>
            <svg 
              className={cn('text-white', {
                'h-2 w-2': size === 'sm',
                'h-2.5 w-2.5': size === 'md',
                'h-3 w-3': size === 'lg',
              })}
              fill="currentColor" 
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  const getInitials = () => {
    if (!user?.firstName) return 'U';
    return `${user.firstName?.[0] ?? ''}${user.lastName?.[0] ?? ''}`.toUpperCase();
  };

  // Dacă user este null, afișăm un avatar implicit
  if (!user) {
    return (
      <div className={cn('relative inline-block', className)}>
        <div className={cn(
          'rounded-full bg-gray-400 flex items-center justify-center',
          sizeClasses[size]
        )}>
          <UserIcon className={cn('text-white', iconSizeClasses[size])} />
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative inline-block', className)}>
      {user?.avatar ? (
        <img
          src={user.avatar}
          alt={`${user.firstName} ${user.lastName}` || 'Avatar utilizator'}
          className={cn(
            'rounded-full object-cover',
            sizeClasses[size]
          )}
        />
      ) : (
        <div className={cn(
          'rounded-full bg-primary-600 flex items-center justify-center',
          sizeClasses[size]
        )}>
          {user?.firstName ? (
            <span className={cn('text-white font-medium', {
              'text-xs': size === 'sm',
              'text-sm': size === 'md',
              'text-base': size === 'lg',
            })}>
              {getInitials()}
            </span>
          ) : (
            <UserIcon className={cn('text-white', iconSizeClasses[size])} />
          )}
        </div>
      )}
      
      {showBadge && getPlanBadge()}
    </div>
  );
};

export default UserAvatar;