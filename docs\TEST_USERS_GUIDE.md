# 👥 GHID UTILIZATORI DE TEST - VERSIUNEA 1.1.4

## 📋 PREZENTARE GENERALĂ

Acest ghid documentează utilizatorii de test disponibili în aplicația Expense Tracker MVP pentru testarea tuturor funcționalităților și tipurilor de abonament.

---

## 🔐 CONTURI DE TEST DISPONIBILE

### 🆓 Utilizator Gratuit
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Free
- **Status abonament**: Fără abonament
- **Limitări**:
  - 50 cheltuieli/lună
  - 5 categorii personalizate
  - Export doar CSV
  - Fără rapoarte avansate
- **Scop testare**: Validarea limitărilor pentru utilizatorii gratuit

### 💰 Utilizator Basic
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Basic ($5/lună)
- **Status abonament**: Active
- **Limitări**:
  - 500 cheltuieli/lună
  - Categorii nelimitate
  - Export CSV și Excel
  - Rapoarte de bază
- **Scop testare**: Validarea funcționalităților pentru planul Basic

### 🌟 Utilizator Premium
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Premium ($15/lună)
- **Status abonament**: Active
- **Limitări**: Fără limitări
- **Funcționalități complete**:
  - Cheltuieli nelimitate
  - Toate tipurile de export
  - Rapoarte avansate
  - Backup automat
  - Suport prioritar
- **Scop testare**: Validarea tuturor funcționalităților premium

### 🎯 Utilizator Demo
- **Email**: `<EMAIL>`
- **Parola**: `password123`
- **Plan**: Free (cu date sample)
- **Status**: Cont demo permanent
- **Scop**: Demonstrație pentru vizitatori și prezentări
- **Caracteristici**:
  - Date sample pre-populate
  - Categorii și cheltuieli de demonstrație
  - Resetare periodică (opțional)

### 🔧 Administrator
- **Email**: `<EMAIL>`
- **Parola**: `admin123`
- **Rol**: Administrator sistem
- **Acces special**:
  - Dashboard administrator
  - Gestionarea utilizatorilor
  - Statistici globale
  - Configurări sistem
  - Monitorizarea abonamentelor
- **Scop testare**: Validarea funcționalităților administrative

---

## 🛠️ GENERAREA UTILIZATORILOR DE TEST

### Script automatizat
Pentru a crea toți utilizatorii de test în baza de date:

```bash
# Din directorul backend
cd backend
node scripts/createTestUsers.js
```

### Ce face scriptul
1. **Verifică utilizatori existenți** - Nu suprascrie conturile existente
2. **Creează utilizatori noi** - Doar dacă nu există deja
3. **Hash-uiește parolele** - Securitate completă
4. **Configurează abonamentele** - Pentru planurile plătite
5. **Adaugă categorii default** - 6 categorii pentru fiecare utilizator
6. **Generează cheltuieli sample** - 4 cheltuieli de demonstrație
7. **Afișează rezumat** - Confirmarea creării cu succes

### Output exemplu
```
🚀 Creating test users for each subscription type...
📋 Found 3 active plans
👤 Creating user: <EMAIL> (free)
✅ User created with ID: 123
📂 Creating default <NAME_EMAIL>
✅ Created 6 <NAME_EMAIL>
💰 Creating sample <NAME_EMAIL>
✅ Created 4 sample <NAME_EMAIL>

🎉 All test users created successfully!
```

---

## 📊 DATE DE TEST INCLUSE

### Categorii default (pentru fiecare utilizator)
1. **Mâncare** 🍽️
   - Icon: `utensils`
   - Culoare: `#EF4444` (roșu)
   - Tip: Default

2. **Transport** 🚗
   - Icon: `car`
   - Culoare: `#3B82F6` (albastru)
   - Tip: Default

3. **Utilități** 🏠
   - Icon: `home`
   - Culoare: `#10B981` (verde)
   - Tip: Default

4. **Divertisment** 🎬
   - Icon: `film`
   - Culoare: `#8B5CF6` (violet)
   - Tip: Default

5. **Sănătate** ❤️
   - Icon: `heart`
   - Culoare: `#F59E0B` (portocaliu)
   - Tip: Default

6. **Cumpărături** 🛍️
   - Icon: `shopping-bag`
   - Culoare: `#EC4899` (roz)
   - Tip: Default

### Cheltuieli sample (pentru fiecare utilizator)
1. **Prânz la restaurant** - 25.50 RON (Categoria: Mâncare)
2. **Transport public** - 15.00 RON (Categoria: Transport)
3. **Factură electricitate** - 120.00 RON (Categoria: Utilități)
4. **Cinema** - 45.00 RON (Categoria: Divertisment)

### Configurări abonament
- **Utilizatori Basic și Premium**:
  - Stripe Customer ID simulat: `cus_test_{plan}_{timestamp}`
  - Stripe Subscription ID simulat: `sub_test_{plan}_{timestamp}`
  - Perioada curentă: 1 lună de la crearea contului
  - Status: `active`
  - Metadata: `{test_user: true, created_by: 'test_script'}`

---

## 🧪 SCENARII DE TESTARE

### Testarea limitărilor (Utilizator Free)
1. **Login** cu `<EMAIL>`
2. **Adaugă 50 cheltuieli** - Ar trebui să funcționeze
3. **Încearcă să adaugi a 51-a cheltuială** - Ar trebui să fie blocată
4. **Încearcă să creezi a 6-a categorie** - Ar trebui să fie blocată
5. **Testează export** - Doar CSV disponibil

### Testarea funcționalităților Basic
1. **Login** cu `<EMAIL>`
2. **Adaugă până la 500 cheltuieli** - Ar trebui să funcționeze
3. **Creează categorii nelimitate** - Ar trebui să funcționeze
4. **Testează export** - CSV și Excel disponibile
5. **Accesează rapoarte de bază** - Ar trebui să funcționeze

### Testarea funcționalităților Premium
1. **Login** cu `<EMAIL>`
2. **Adaugă cheltuieli nelimitate** - Fără restricții
3. **Accesează toate funcționalitățile** - Complet disponibile
4. **Testează rapoarte avansate** - Toate tipurile disponibile
5. **Testează backup automat** - Funcționalitate premium

### Testarea funcționalităților admin
1. **Login** cu `<EMAIL>`
2. **Accesează dashboard admin** - `/admin/dashboard`
3. **Vizualizează lista utilizatorilor** - Toți utilizatorii
4. **Monitorizează abonamentele** - Statistici globale
5. **Gestionează utilizatori** - Activare/dezactivare

---

## 🔄 RESETAREA DATELOR DE TEST

### Resetare manuală
Pentru a reseta datele unui utilizator de test:

```sql
-- Șterge cheltuielile utilizatorului
DELETE FROM expenses WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Resetează contorul de utilizare
UPDATE users SET monthly_expense_count = 0 WHERE email = '<EMAIL>';
```

### Resetare completă
Pentru a șterge și recrea toți utilizatorii de test:

```bash
# Șterge utilizatorii de test existenți
node scripts/cleanTestUsers.js

# Recreează utilizatorii de test
node scripts/createTestUsers.js
```

---

## 📝 NOTIȚE IMPORTANTE

### Securitate
- **Parolele sunt hash-uite** cu bcrypt (10 rounds)
- **Conturile sunt marcate** ca utilizatori de test în metadata
- **Nu folosiți în producție** - Doar pentru dezvoltare și testare

### Mentenanță
- **Verificați periodic** dacă utilizatorii de test există
- **Resetați datele** când este necesar pentru testare curată
- **Actualizați documentația** când se adaugă noi utilizatori

### Troubleshooting
- **Dacă scriptul eșuează**: Verificați conexiunea la baza de date
- **Dacă utilizatorii există deja**: Scriptul va sări peste ei
- **Pentru debugging**: Adăugați `console.log` în script

---

## 📞 SUPORT

Pentru probleme cu utilizatorii de test:
1. Verificați log-urile din `backend/logs/`
2. Rulați scriptul cu debugging activat
3. Consultați documentația de troubleshooting
4. Contactați echipa de dezvoltare

---

*Ultima actualizare: 8 ianuarie 2025 - Versiunea 1.1.4*
*Creat pentru: Expense Tracker MVP*
*Autor: Echipa de dezvoltare*