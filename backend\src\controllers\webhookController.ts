import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import stripeService from '../services/stripeService';
import subscriptionService from '../services/subscriptionService';
import usageService from '../services/usageService';
import { safeLog } from '../utils/safeLogger';

// Temporary logger replacement
const logger = {
  info: (message: string, data?: unknown) => safeLog.debug('INFO:', message, data),
  error: (message: string, error?: unknown) => safeLog.error('ERROR:', message, error),
};

interface AuthenticatedRequest extends Request {
  user?: unknown;
  userId?: string;
}

class WebhookController {
  /**
   * Gestionează webhook-urile Stripe
   */
  async handleStripeWebhook(req: Request, res: Response): Promise<Response> {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
      // Verifică semnătura webhook-ului
      // TODO: Implementează constructWebhookEvent în stripeService
      // event = stripeService.constructWebhookEvent(req.body, sig, endpointSecret);
      event = req.body; // Temporar până când metoda va fi implementată
    } catch (err: unknown) {
      logger.error('Webhook signature verification failed:', (err as Error)?.message || err);
      return res.status(400).send(`Webhook Error: ${(err as Error)?.message || 'Unknown error'}`);
    }

    try {
      // Înregistrează evenimentul în baza de date
      await this.logWebhookEvent(event);

      // Procesează evenimentul
      await this.processWebhookEvent(event);

      return res.json({ received: true });
    } catch (error) {
      logger.error('Error processing webhook:', (error as Error));
      return res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Înregistrează evenimentul webhook în baza de date
   */
  async logWebhookEvent(event: any): Promise<void> {
    try {
      await prisma.webhookEvent.create({
        data: {
          stripeId: event.id,
          type: event.type,
          data: event.data,
          processed: false,
          createdAt: new Date(event.created * 1000),
        },
      });

      logger.info(`Webhook event logged: ${event.type} - ${event.id}`);
    } catch (error: unknown) {
      // Dacă evenimentul există deja, îl ignorăm
      if ((error as any)?.code === 'P2002') {
        logger.info(`Webhook event already exists: ${(event as any).id}`);
        return;
      }
      throw error;
    }
  }

  /**
   * Procesează evenimentul webhook
   */
  async processWebhookEvent(event: any): Promise<void> {
    logger.info(`Processing webhook: ${event.type}`);

    switch (event.type) {
      case 'customer.subscription.created':
        await this.handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await this.handleSubscriptionDeleted(event.data.object);
        break;

      case 'customer.subscription.trial_will_end':
        await this.handleTrialWillEnd(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object);
        break;

      case 'customer.created':
        await this.handleCustomerCreated(event.data.object);
        break;

      case 'customer.updated':
        await this.handleCustomerUpdated(event.data.object);
        break;

      default:
        logger.info(`Unhandled webhook type: ${event.type}`);
    }

    // Marchează evenimentul ca procesat
    await this.markEventAsProcessed(event.id);
  }

  /**
   * Gestionează crearea unui abonament
   */
  async handleSubscriptionCreated(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription created: ${subscription.id}`);

      // Găsește utilizatorul bazat pe customer ID
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: subscription.customer },
      });

      if (!user) {
        logger.error(`User not found for customer: ${subscription.customer}`);
        return;
      }

      // Găsește planul bazat pe price ID
      const plan = await prisma.plan.findFirst({
        where: { stripeId: subscription.items.data[0].price.id },
      });

      if (!plan) {
        logger.error(`Plan not found for price: ${subscription.items.data[0].price.id}`);
        return;
      }

      // TODO: Creează abonamentul în baza de date când metoda va fi implementată
      // await subscriptionService.createSubscription({
      //   stripeSubscriptionId: subscription.id,
      //   userId: user.id,
      //   planId: plan.id,
      //   status: subscription.status,
      //   currentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
      //   currentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
      //   trialStart: subscription.trialStart ? new Date(subscription.trialStart * 1000) : null,
      //   trialEnd: subscription.trialEnd ? new Date(subscription.trialEnd * 1000) : null,
      //   metadata: subscription.metadata,
      // });

      // TODO: Înregistrează utilizarea când metoda va fi implementată
      // await usageService.logUsage(user.id, 'subscription_created', {
      //   subscriptionId: subscription.id,
      //   planId: plan.id,
      // });

      logger.info(`Subscription created successfully for user: ${user.id}`);
    } catch (error) {
      logger.error('Error handling subscription created:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează actualizarea unui abonament
   */
  async handleSubscriptionUpdated(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription updated: ${subscription.id}`);

      const updateData = {
        status: subscription.status,
        currentPeriodStart: new Date(subscription.currentPeriodStart * 1000),
        currentPeriodEnd: new Date(subscription.currentPeriodEnd * 1000),
        trialStart: subscription.trialStart ? new Date(subscription.trialStart * 1000) : null,
        trialEnd: subscription.trialEnd ? new Date(subscription.trialEnd * 1000) : null,
        canceledAt: subscription.canceledAt ? new Date(subscription.canceledAt * 1000) : null,
        metadata: subscription.metadata,
        updatedAt: new Date(),
      };

      // TODO: Actualizează abonamentul când metoda va fi implementată
      // await subscriptionService.updateSubscription(subscription.id, updateData);

      // Găsește utilizatorul pentru logging
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripeId: subscription.id },
        include: { user: true },
      });

      if (dbSubscription) {
        // TODO: Înregistrează utilizarea când metoda va fi implementată
        // await usageService.logUsage(dbSubscription.user.id, 'subscription_updated', {
        //   subscriptionId: subscription.id,
        //   status: subscription.status,
        // });
      }

      logger.info(`Subscription updated successfully: ${subscription.id}`);
    } catch (error) {
      logger.error('Error handling subscription updated:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează ștergerea unui abonament
   */
  async handleSubscriptionDeleted(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription deleted: ${subscription.id}`);

      // Găsește abonamentul în baza de date
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripeId: subscription.id },
        include: { user: true },
      });

      if (!dbSubscription) {
        logger.error(`Subscription not found in database: ${subscription.id}`);
        return;
      }

      // Actualizează statusul abonamentului
      await prisma.subscription.update({
        where: { stripeId: subscription.id },
        data: {
          status: 'canceled',
          canceledAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Downgradează utilizatorul la planul free
      await prisma.user.update({
        where: { id: dbSubscription.user.id },
        data: {
          planType: 'free',
          subscriptionStatus: 'canceled',
        },
      });

      // Înregistrează utilizarea
      await usageService.incrementUsage(dbSubscription.user.id.toString(), 'subscription_deleted');

      logger.info(`Subscription deleted successfully: ${subscription.id}`);
    } catch (error) {
      logger.error('Error handling subscription deleted:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează sfârșitul perioadei de trial
   */
  async handleTrialWillEnd(subscription: any): Promise<void> {
    try {
      logger.info(`Processing trial will end: ${subscription.id}`);

      // Găsește utilizatorul
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripeId: subscription.id },
        include: { user: true },
      });

      if (!dbSubscription) {
        logger.error(`Subscription not found: ${subscription.id}`);
        return;
      }

      // Înregistrează evenimentul
      await usageService.incrementUsage(dbSubscription.user.id.toString(), 'trial_will_end');

      // Aici poți adăuga logica pentru notificări email
      logger.info(`Trial will end notification logged for user: ${dbSubscription.user.id}`);
    } catch (error) {
      logger.error('Error handling trial will end:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează plata reușită
   */
  async handlePaymentSucceeded(invoice: any): Promise<void> {
    try {
      logger.info(`Processing payment succeeded: ${invoice.id}`);

      if (invoice.subscription) {
        // Găsește abonamentul
        const dbSubscription = await prisma.subscription.findUnique({
          where: { stripeId: invoice.subscription },
          include: { user: true },
        });

        if (dbSubscription) {
          // Înregistrează plata reușită
          await usageService.incrementUsage(dbSubscription.user.id.toString(), 'payment_succeeded');

          // Resetează contorul de utilizare pentru noua perioadă
          await usageService.resetUsageForPeriod(dbSubscription.user.id.toString(), 'month');
        }
      }

      logger.info(`Payment succeeded processed: ${invoice.id}`);
    } catch (error) {
      logger.error('Error handling payment succeeded:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează plata eșuată
   */
  async handlePaymentFailed(invoice: any): Promise<void> {
    try {
      logger.info(`Processing payment failed: ${invoice.id}`);

      if (invoice.subscription) {
        // Găsește abonamentul
        const dbSubscription = await prisma.subscription.findUnique({
          where: { stripeId: invoice.subscription },
          include: { user: true },
        });

        if (dbSubscription) {
          // Înregistrează plata eșuată
          await usageService.incrementUsage(dbSubscription.user.id.toString(), 'payment_failed');

          // Aici poți adăuga logica pentru notificări și gestionarea eșecurilor de plată
        }
      }

      logger.info(`Payment failed processed: ${invoice.id}`);
    } catch (error) {
      logger.error('Error handling payment failed:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează crearea unui client
   */
  async handleCustomerCreated(customer: any): Promise<void> {
    try {
      logger.info(`Processing customer created: ${customer.id}`);

      // Actualizează utilizatorul cu customer ID-ul
      if (customer.email) {
        await prisma.user.updateMany({
          where: { email: customer.email },
          data: { stripeCustomerId: customer.id },
        });
      }

      logger.info(`Customer created processed: ${customer.id}`);
    } catch (error) {
      logger.error('Error handling customer created:', (error as Error));
      throw error;
    }
  }

  /**
   * Gestionează actualizarea unui client
   */
  async handleCustomerUpdated(customer: any): Promise<void> {
    try {
      logger.info(`Processing customer updated: ${customer.id}`);

      // Găsește utilizatorul și actualizează informațiile
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: customer.id },
      });

      if (user) {
        await usageService.incrementUsage(user.id.toString(), 'customer_updated');
      }

      logger.info(`Customer updated processed: ${customer.id}`);
    } catch (error) {
      logger.error('Error handling customer updated:', (error as Error));
      throw error;
    }
  }

  /**
   * Marchează evenimentul ca procesat
   */
  async markEventAsProcessed(eventId: string): Promise<void> {
    try {
      await prisma.webhookEvent.update({
      where: { stripeId: eventId },
      data: {
        processed: true,
        processedAt: new Date(),
      },
    });
    } catch (error) {
      logger.error('Error marking event as processed:', (error as Error));
      // Nu aruncăm eroarea pentru că nu vrem să eșueze webhook-ul
    }
  }

  /**
   * Obține statistici despre webhook-uri (admin only)
   */
  async getWebhookStats(req: AuthenticatedRequest, res: Response): Promise<Response> {
    try {
      const { startDate, endDate } = req.query as { startDate?: string; endDate?: string };
      const dateFilter: any = {};

      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.gte = new Date(startDate as string);
        if (endDate) dateFilter.createdAt.lte = new Date(endDate as string);
      }

      const stats = await prisma.webhookEvent.groupBy({
        by: ['type', 'processed'],
        where: dateFilter,
        _count: {
          id: true,
        },
      });

      const recentEvents = await prisma.webhookEvent.findMany({
        where: dateFilter,
        orderBy: { createdAt: 'desc' },
        take: 20,
      });

      return res.json({
        success: true,
        data: {
          stats,
          recentEvents: recentEvents,
        },
      });
    } catch (error) {
      logger.error('Error getting webhook stats:', (error as Error));
      return res.status(500).json({
        success: false,
        message: 'Failed to get webhook statistics',
      });
    }
  }
}

export const webhookController = new WebhookController();
