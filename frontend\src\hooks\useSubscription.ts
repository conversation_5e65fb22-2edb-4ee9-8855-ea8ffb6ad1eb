import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import subscriptionService from '../services/subscriptionService';

// Tipuri pentru hook-urile de abonament
interface CreateCheckoutSessionParams {
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  successUrl: string;
  cancelUrl: string;
}

interface UseSubscriptionManagerReturn {
  plans: unknown;
  currentSubscription: unknown;
  usageStats: unknown;
  createCheckoutSession: unknown;
  createCustomerPortal: unknown;
  cancelSubscription: unknown;
  reactivateSubscription: unknown;
  isLoading: boolean;
  hasActiveSubscription: boolean;
  canUpgrade: boolean;
  isFreePlan: boolean;
}

/**
 * Hook pentru obținerea planurilor disponibile
 */
export function usePlans() {
  return useQuery({
    queryKey: ['plans'],
    queryFn: subscriptionService.getPlans,
    staleTime: 10 * 60 * 1000, // 10 minute
    gcTime: 30 * 60 * 1000, // 30 minute (renamed from cacheTime)
  });
}

/**
 * Hook pentru obținerea abonamentului curent
 */
export function useCurrentSubscription() {
  return useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: subscriptionService.getCurrentSubscription,
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
    retry: (failureCount, error: unknown) => {
      // Nu reîncerca dacă utilizatorul nu are abonament
      if ((error as any)?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

/**
 * Hook pentru obținerea statisticilor de utilizare
 */
export function useUsageStats() {
  return useQuery({
    queryKey: ['subscription', 'usage'],
    queryFn: subscriptionService.getUsageStats,
    staleTime: 2 * 60 * 1000, // 2 minute
    gcTime: 10 * 60 * 1000, // 10 minute
  });
}

/**
 * Hook pentru crearea unei sesiuni de checkout
 */
export function useCreateCheckoutSession() {
  return useMutation({
    mutationFn: ({ planId, billingCycle, successUrl, cancelUrl }: CreateCheckoutSessionParams) =>
      subscriptionService.createCheckoutSession(planId, billingCycle, successUrl, cancelUrl),
    onSuccess: data => {
      // Redirecționează către Stripe Checkout
      if (data.data?.url) {
        window.location.href = data.data.url;
      }
    },
    onError: (error: unknown) => {
      toast.error(
        (error as any).response?.data?.message ||
          'Eroare la crearea sesiunii de checkout. Vă rugăm să încercați din nou.',
      );
    },
  });
}

/**
 * Hook pentru crearea portalului de clienți
 */
export function useCreateCustomerPortal(): unknown {
  return useMutation({
    mutationFn: (returnUrl: string) => subscriptionService.createCustomerPortal(returnUrl),
    onSuccess: data => {
      // Redirecționează către Stripe Customer Portal
      if (data.data?.url) {
        window.location.href = data.data.url;
      }
    },
    onError: (error: unknown) => {
      toast.error(
        (error as any).response?.data?.message ||
          'Eroare la accesarea portalului de gestionare. Vă rugăm să încercați din nou.',
      );
    },
  });
}

/**
 * Hook pentru anularea abonamentului
 */
export function useCancelSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reason: string = '') => subscriptionService.cancelSubscription(reason),
    onSuccess: () => {
      // Invalidează cache-ul pentru abonamentul curent
      queryClient.invalidateQueries({ queryKey: ['subscription', 'current'] });
      queryClient.invalidateQueries({ queryKey: ['subscription', 'usage'] });
      toast.success('Abonamentul a fost anulat cu succes.');
    },
    onError: (error: unknown) => {
      toast.error(
        (error as any).response?.data?.message ||
          'Eroare la anularea abonamentului. Vă rugăm să încercați din nou.',
      );
    },
  });
}

/**
 * Hook pentru reactivarea abonamentului
 */
export function useReactivateSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => subscriptionService.reactivateSubscription(),
    onSuccess: () => {
      // Invalidează cache-ul pentru abonamentul curent
      queryClient.invalidateQueries({ queryKey: ['subscription', 'current'] });
      queryClient.invalidateQueries({ queryKey: ['subscription', 'usage'] });
      toast.success('Abonamentul a fost reactivat cu succes.');
    },
    onError: (error: unknown) => {
      toast.error(
        (error as any).response?.data?.message ||
          'Eroare la reactivarea abonamentului. Vă rugăm să încercați din nou.',
      );
    },
  });
}

/**
 * Hook pentru verificarea statusului unei sesiuni de checkout
 */
export function useCheckCheckoutSession(sessionId: string | null | undefined) {
  return useQuery({
    queryKey: ['checkout', 'session', sessionId],
    queryFn: () => subscriptionService.checkCheckoutSession(sessionId!),
    enabled: !!sessionId,
    staleTime: 0, // Verifică mereu statusul cel mai recent
    gcTime: 5 * 60 * 1000, // 5 minute
    refetchInterval: query => {
      // Oprește polling-ul dacă sesiunea este completă sau expirată
      const sessionData = query?.state?.data as any;
      if (sessionData?.data?.status === 'complete' || sessionData?.data?.status === 'expired') {
        return false;
      }
      return 5000; // Verifică la fiecare 5 secunde
    },
  });
}

/**
 * Hook pentru verificarea permisiunilor
 */
export function useCheckPermission(action: string | null | undefined) {
  return useQuery({
    queryKey: ['subscription', 'permissions', action],
    queryFn: () => subscriptionService.checkPermission(action!),
    enabled: !!action,
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
  });
}

/**
 * Hook compus pentru gestionarea completă a abonamentului
 */
export function useSubscriptionManager(): UseSubscriptionManagerReturn {
  const plans = usePlans();
  const currentSubscription = useCurrentSubscription();
  const usageStats = useUsageStats();
  const createCheckoutSession = useCreateCheckoutSession();
  const createCustomerPortal = useCreateCustomerPortal();
  const cancelSubscription = useCancelSubscription();
  const reactivateSubscription = useReactivateSubscription();

  return {
    // Queries
    plans,
    currentSubscription,
    usageStats,

    // Mutations
    createCheckoutSession,
    createCustomerPortal,
    cancelSubscription,
    reactivateSubscription,

    // Computed values
    isLoading: plans.isLoading || currentSubscription.isLoading,
    hasActiveSubscription: currentSubscription.data?.data?.status === 'active',
    canUpgrade: currentSubscription.data?.data?.plan?.name !== 'Business',
    isFreePlan:
      !currentSubscription.data?.data || currentSubscription.data?.data?.plan?.name === 'Free',
  };
}
