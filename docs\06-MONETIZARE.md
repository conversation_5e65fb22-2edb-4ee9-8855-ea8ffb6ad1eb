# 💰 MONETIZARE ȘI STRIPE INTEGRATION

## 🎯 STRATEGIA DE MONETIZARE

### Obiectivul Principal
Generarea a minimum $50/lună în primele 3-6 luni prin abonamente premium și funcționalități cu valoare adăugată.

### Modelul de Business
- **Freemium Model**: Funcționalități de bază gratuite cu limitări
- **Subscription Tiers**: Planuri Basic și Premium cu funcționalități avansate
- **Value-based Pricing**: Prețuri bazate pe valoarea oferită utilizatorilor

### Planurile de Abonament

#### Free Plan ($0/lună)
- **Limite**: 50 cheltuieli/lună, 5 categorii personalizate
- **Funcționalități**: CRUD cheltuieli, categorii de bază, export CSV
- **Restricții**: Fără rapoarte avansate, fără backup automat

#### Basic Plan ($9.99/lună)
- **Limite**: 500 cheltuieli/lună, categorii nelimitate
- **Funcționalități**: Export avansat (PDF, Excel), rapoarte de bază
- **Beneficii**: Suport email, backup lunar

#### Premium Plan ($19.99/lună)
- **Limite**: Nelimitat
- **Funcționalități**: Toate funcționalitățile, rapoarte avansate, AI insights
- **Beneficii**: Suport prioritar, backup zilnic, API access

---

## 🔧 STRIPE INTEGRATION

### Setup Stripe Account

#### Configurare Inițială
```bash
# 1. Creează cont Stripe (https://stripe.com)
# 2. Obține API keys din Dashboard
# 3. Configurează webhook endpoint
# 4. Creează produsele și prețurile
```

#### Environment Variables
```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51...
STRIPE_PUBLISHABLE_KEY=pk_test_51...
STRIPE_WEBHOOK_SECRET=whsec_...

# Stripe Product IDs
STRIPE_BASIC_PRICE_ID=price_1234567890
STRIPE_PREMIUM_PRICE_ID=price_0987654321

# URLs
STRIPE_SUCCESS_URL=http://localhost:5173/subscription/success
STRIPE_CANCEL_URL=http://localhost:5173/subscription/cancel
```

### Backend Implementation

#### Stripe Service
```typescript
// src/services/stripeService.ts
import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const prisma = new PrismaClient();

export class StripeService {
  async createCustomer(user: { id: string; email: string; name: string }) {
    try {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user.id,
        },
      });

      // Update user with Stripe customer ID
      await prisma.user.update({
        where: { id: user.id },
        data: { stripeCustomerId: customer.id },
      });

      return customer;
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw error;
    }
  }

  async createCheckoutSession(userId: string, priceId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Create customer if doesn't exist
      let customerId = user.stripeCustomerId;
      if (!customerId) {
        const customer = await this.createCustomer(user);
        customerId = customer.id;
      }

      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${process.env.STRIPE_SUCCESS_URL}?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: process.env.STRIPE_CANCEL_URL,
        metadata: {
          userId: userId,
        },
      });

      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }

  async createPortalSession(customerId: string) {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: process.env.FRONTEND_URL + '/settings/billing',
      });

      return session;
    } catch (error) {
      console.error('Error creating portal session:', error);
      throw error;
    }
  }

  async handleWebhook(payload: string, signature: string) {
    try {
      const event = stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
          break;
        
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;
        
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      console.error('Webhook error:', error);
      throw error;
    }
  }

  private async handleCheckoutCompleted(session: Stripe.Checkout.Session) {
    const userId = session.metadata?.userId;
    if (!userId) return;

    // Update user subscription status
    await prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionStatus: 'active',
        stripeCustomerId: session.customer as string,
      },
    });
  }

  private async handleSubscriptionCreated(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    const user = await prisma.user.findFirst({
      where: { stripeCustomerId: customerId },
    });

    if (!user) return;

    // Create subscription record
    await prisma.subscription.create({
      data: {
        userId: user.id,
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: customerId,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        planName: this.getPlanNameFromPriceId(subscription.items.data[0].price.id),
      },
    });

    // Update user plan
    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionPlan: this.getPlanNameFromPriceId(subscription.items.data[0].price.id),
        subscriptionStatus: subscription.status,
      },
    });
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription) {
    await prisma.subscription.updateMany({
      where: { stripeSubscriptionId: subscription.id },
      data: {
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      },
    });
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    const user = await prisma.user.findFirst({
      where: { stripeCustomerId: customerId },
    });

    if (!user) return;

    // Update user to free plan
    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionPlan: 'free',
        subscriptionStatus: 'canceled',
      },
    });

    // Update subscription record
    await prisma.subscription.updateMany({
      where: { stripeSubscriptionId: subscription.id },
      data: { status: 'canceled' },
    });
  }

  private getPlanNameFromPriceId(priceId: string): string {
    switch (priceId) {
      case process.env.STRIPE_BASIC_PRICE_ID:
        return 'basic';
      case process.env.STRIPE_PREMIUM_PRICE_ID:
        return 'premium';
      default:
        return 'free';
    }
  }
}
```

#### Subscription Controller
```typescript
// src/controllers/subscriptionController.ts
import { Request, Response, NextFunction } from 'express';
import { StripeService } from '../services/stripeService';
import { AuthenticatedRequest } from '../types';

const stripeService = new StripeService();

export const getPlans = async (req: Request, res: Response) => {
  try {
    const plans = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [
          '50 expenses per month',
          '5 custom categories',
          'Basic reports',
          'CSV export',
        ],
        limits: {
          expenses: 50,
          categories: 5,
          exports: 3,
        },
      },
      {
        id: 'basic',
        name: 'Basic',
        price: 9.99,
        currency: 'USD',
        interval: 'month',
        priceId: process.env.STRIPE_BASIC_PRICE_ID,
        features: [
          '500 expenses per month',
          'Unlimited categories',
          'Advanced reports',
          'PDF & Excel export',
          'Email support',
        ],
        limits: {
          expenses: 500,
          categories: -1,
          exports: -1,
        },
      },
      {
        id: 'premium',
        name: 'Premium',
        price: 19.99,
        currency: 'USD',
        interval: 'month',
        priceId: process.env.STRIPE_PREMIUM_PRICE_ID,
        features: [
          'Unlimited expenses',
          'Unlimited categories',
          'AI-powered insights',
          'All export formats',
          'Priority support',
          'API access',
        ],
        limits: {
          expenses: -1,
          categories: -1,
          exports: -1,
        },
        popular: true,
      },
    ];

    res.json({
      success: true,
      data: plans,
    });
  } catch (error) {
    next(error);
  }
};

export const createCheckoutSession = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { priceId } = req.body;
    
    if (!priceId) {
      return res.status(400).json({
        success: false,
        message: 'Price ID is required',
      });
    }

    const session = await stripeService.createCheckoutSession(
      req.user!.id,
      priceId
    );

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const createPortalSession = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user!;
    
    if (!user.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found',
      });
    }

    const session = await stripeService.createPortalSession(user.stripeCustomerId);

    res.json({
      success: true,
      data: {
        url: session.url,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const handleWebhook = async (req: Request, res: Response) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    const payload = req.body;

    await stripeService.handleWebhook(payload, signature);

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({ error: 'Webhook error' });
  }
};
```

### Frontend Implementation

#### Subscription Hook
```typescript
// src/hooks/useSubscription.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { subscriptionService } from '../services/subscriptionService';

export function useSubscriptionPlans() {
  return useQuery({
    queryKey: ['subscription', 'plans'],
    queryFn: subscriptionService.getPlans,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useCurrentSubscription() {
  return useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: subscriptionService.getCurrentSubscription,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateCheckoutSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: subscriptionService.createCheckoutSession,
    onSuccess: (data) => {
      // Redirect to Stripe Checkout
      window.location.href = data.url;
    },
    onError: (error) => {
      console.error('Checkout error:', error);
    },
  });
}

export function useCreatePortalSession() {
  return useMutation({
    mutationFn: subscriptionService.createPortalSession,
    onSuccess: (data) => {
      // Redirect to Stripe Customer Portal
      window.location.href = data.url;
    },
  });
}
```

#### Pricing Component
```typescript
// src/components/subscription/PricingPlans.tsx
import React from 'react';
import { useSubscriptionPlans, useCreateCheckoutSession } from '../../hooks/useSubscription';
import { useAuthStore } from '../../store/authStore';

const PricingPlans: React.FC = () => {
  const { data: plans, isLoading } = useSubscriptionPlans();
  const createCheckoutSession = useCreateCheckoutSession();
  const { user } = useAuthStore();

  const handleSelectPlan = (priceId: string) => {
    if (!user) {
      // Redirect to login
      window.location.href = '/login';
      return;
    }

    createCheckoutSession.mutate({ priceId });
  };

  if (isLoading) {
    return <div>Loading plans...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      {plans?.data.map((plan) => (
        <div
          key={plan.id}
          className={`relative bg-white rounded-lg shadow-lg p-8 ${
            plan.popular ? 'ring-2 ring-blue-500' : ''
          }`}
        >
          {plan.popular && (
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                Most Popular
              </span>
            </div>
          )}

          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
            <div className="mt-4">
              <span className="text-4xl font-bold text-gray-900">
                ${plan.price}
              </span>
              <span className="text-gray-500">/{plan.interval}</span>
            </div>
          </div>

          <ul className="mt-8 space-y-4">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-center">
                <svg
                  className="h-5 w-5 text-green-500 mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>

          <div className="mt-8">
            {plan.id === 'free' ? (
              <button
                disabled
                className="w-full bg-gray-100 text-gray-500 py-3 px-4 rounded-lg font-medium cursor-not-allowed"
              >
                Current Plan
              </button>
            ) : (
              <button
                onClick={() => handleSelectPlan(plan.priceId)}
                disabled={createCheckoutSession.isPending}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                {createCheckoutSession.isPending ? 'Processing...' : 'Get Started'}
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default PricingPlans;
```

---

## 📊 USAGE LIMITS ȘI TRACKING

### Middleware pentru Limite
```typescript
// src/middleware/usageLimits.ts
import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import { prisma } from '../config/prisma';

const PLAN_LIMITS = {
  free: {
    expenses: 50,
    categories: 5,
    exports: 3,
  },
  basic: {
    expenses: 500,
    categories: -1, // unlimited
    exports: -1,
  },
  premium: {
    expenses: -1,
    categories: -1,
    exports: -1,
  },
};

export const checkExpenseLimit = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user!;
    const plan = user.subscriptionPlan || 'free';
    const limit = PLAN_LIMITS[plan as keyof typeof PLAN_LIMITS]?.expenses;

    if (limit === -1) {
      return next(); // Unlimited
    }

    // Count current month expenses
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const expenseCount = await prisma.expense.count({
      where: {
        userId: user.id,
        createdAt: {
          gte: startOfMonth,
        },
      },
    });

    if (expenseCount >= limit) {
      return res.status(403).json({
        success: false,
        message: `Monthly expense limit reached. Upgrade to add more expenses.`,
        data: {
          currentCount: expenseCount,
          limit,
          planType: plan,
        },
      });
    }

    next();
  } catch (error) {
    next(error);
  }
};
```

---

*Ultima actualizare: Ianuarie 2025*
