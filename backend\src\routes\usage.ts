import express from 'express';
import { Request, Response, NextFunction } from 'express';
import { authenticateToken } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';
import usageService from '../services/usageService';
import subscriptionService from '../services/subscriptionService';
import logger from '../utils/logger';

const router = express.Router();

/**
 * @route   GET /api/usage/current
 * @desc    Get current usage statistics for the authenticated user
 * @access  Private
 */
router.get('/current', authenticateToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user!.id.toString();
    const stats = await usageService.getUserUsageStats(userId);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error('Error getting current usage:', error);
    next(error);
  }
});

/**
 * @route   GET /api/usage/progress
 * @desc    Get usage progress for the authenticated user
 * @access  Private
 */
router.get('/progress', authenticateToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user!.id.toString();
    const progress = await usageService.getUsageProgress(userId);

    res.json({
      success: true,
      data: progress,
    });
  } catch (error) {
    logger.error('Error getting usage progress:', error);
    next(error);
  }
});

/**
 * @route   GET /api/usage/limits
 * @desc    Get usage limits for the authenticated user's plan
 * @access  Private
 */
router.get('/limits', authenticateToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user!.id.toString();
    const stats = await usageService.getUserUsageStats(userId);

    res.json({
      success: true,
      data: {
        limits: stats.limits,
        planType: stats.planType,
    unlimitedFeatures: {
      expenses: stats.limits.expensesPerMonth === -1,
      categories: stats.limits.categories === -1,
      exports: stats.limits.exportsPerMonth === -1,
    },
      },
    });
  } catch (error) {
    logger.error('Error getting usage limits:', error);
    next(error);
  }
});

/**
 * @route   POST /api/usage/check
 * @desc    Check if user can perform a specific action
 * @access  Private
 */
router.post(
  '/check',
  authenticateToken,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
      const userId = req.user!.id.toString();
      const { action } = req.body;

      if (!action) {
        res.status(400).json({
          success: false,
          message: 'Action is required',
        });
        return;
      }

      const canPerform = await usageService.canPerformAction(userId, action);
      const stats = await usageService.getUserUsageStats(userId);

      return res.json({
        success: true,
        data: {
          canPerform: canPerform,
      resource,
      currentUsage: stats.currentPeriod,
      limits: stats.limits,
      planType: stats.planType,
        },
      });
    } catch (error) {
      logger.error('Error checking action permission:', error);
      next(error);
    }
  },
);

export default router;
