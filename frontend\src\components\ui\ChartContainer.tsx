import React from 'react';

import { cn } from '../../utils/helpers';

import Card from './Card';
import LoadingSpinner from './LoadingSpinner';

// Types
interface Metric {
  value: string | number;
  label: string;
  change?: number;
}

interface ChartContainerProps {
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  isLoading?: boolean;
  error?: string | null;
  actions?: React.ReactNode;
  className?: string;
  [key: string]: unknown;
}

interface MetricsContainerProps {
  title?: string;
  metrics?: Metric[];
  className?: string;
  [key: string]: unknown;
}

/**
 * Container pentru grafice cu loading state și error handling
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  subtitle,
  children,
  isLoading = false,
  error = null,
  actions,
  className,
  ...props
}) => {
  if (error) {
    return (
      <Card className={cn('p-6', className)} {...props}>
        <div className="flex items-center justify-between mb-4">
          <div>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-2">
              {actions}
            </div>
          )}
        </div>
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-gray-600 font-medium">Eroare la încărcarea graficului</p>
            <p className="text-sm text-gray-500 mt-1">{error}</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn('p-6', className)} {...props}>
      <div className="flex items-center justify-between mb-4">
        <div>
          {title && (
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center space-x-2">
            {actions}
          </div>
        )}
      </div>

      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
            <LoadingSpinner size="lg" />
          </div>
        )}
        <div className={cn('transition-opacity duration-200', isLoading && 'opacity-50')}>
          {children}
        </div>
      </div>
    </Card>
  );
}

/**
 * Container simplu pentru metrici rapide
 */
export const MetricsContainer: React.FC<MetricsContainerProps> = ({
  title,
  metrics = [],
  className,
  ...props
}) => {
  return (
    <Card className={cn('p-6', className)} {...props}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <div className="grid grid-cols-2 gap-4">
        {metrics.map((metric, index) => (
          <div key={index} className="text-center">
            <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
            <p className="text-sm text-gray-600">{metric.label}</p>
            {metric.change && (
              <p className={cn(
                'text-xs mt-1',
                metric.change > 0 ? 'text-green-600' :
                  metric.change < 0 ? 'text-red-600' : 'text-gray-600',
              )}>
                {metric.change > 0 ? '+' : ''}{metric.change}%
              </p>
            )}
          </div>
        ))}
      </div>
    </Card>
  );
}

export default ChartContainer;