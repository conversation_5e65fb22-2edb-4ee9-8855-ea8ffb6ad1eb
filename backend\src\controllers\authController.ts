import * as crypto from 'crypto';
import * as bcryptjs from 'bcryptjs';
import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import { generateToken, generateRefreshToken, verifyRefreshToken } from '../middleware/auth';
import { RegisterDto, AuthenticatedRequest } from '../types';
import { CaseConverter } from '../utils/caseConverter';
import { safeLog } from '../utils/safeLogger';
import { toError } from '../utils/errorHelpers';

// AuthenticatedRequest este acum importat din types

// Register a new user
const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, firstName, lastName, currency = 'USD', timezone = 'UTC' } = req.body as RegisterDto;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (existingUser) {
      res.status(409).json({
        success: false,
        message: 'User with this email already exists',
      });
      return;
    }

    // Hash password
    const hashedPassword = await bcryptjs.hash(password, 12);

    // Create new user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase().trim(),
        password: hashedPassword,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        currency: currency.toUpperCase(),
        timezone,
        role: 'user',
        isActive: true,
        emailVerified: false,
        loginCount: 0,
        lastUsageReset: new Date(),
        monthlyExpenseCount: 0,
        monthlyExpenseLimit: 50,
        planType: 'free',
        preferences: {},
      },
    });

    // Create default categories for the user
    const defaultCategories = [
      {
        name: 'Alimentație',
        description: 'Cheltuieli pentru mâncare și băuturi',
        color: '#FF6B6B',
        icon: 'utensils',
        isDefault: true,
        sortOrder: 1,
        userId: user.id,
      },
      {
        name: 'Transport',
        description: 'Cheltuieli pentru transport',
        color: '#4ECDC4',
        icon: 'car',
        sortOrder: 2,
        userId: user.id,
      },
      {
        name: 'Utilități',
        description: 'Facturi și utilități',
        color: '#45B7D1',
        icon: 'home',
        sortOrder: 3,
        userId: user.id,
      },
    ];

    await prisma.category.createMany({
      data: defaultCategories,
    });

    // Generate tokens
    const accessToken = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Save refresh token to database
    await prisma.user.update({
      where: { id: user.id },
      data: { refreshToken },
    });

    // Remove password from response
    const { password: _, ...userResponse } = user;

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseResponse = CaseConverter.toCamel(userResponse);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: camelCaseResponse,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error: unknown) {
    safeLog.error('Registration error:', toError(error));

    if ((error as any).name === 'SequelizeValidationError') {
      const errors = (error as any).errors.map((err: any): { field: string; message: string } => ({
        field: err.path,
        message: err.message,
      }));

      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during registration',
    });
  }
};

// Login user
const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        success: false,
        message: 'Account is deactivated. Please contact support.',
      });
      return;
    }

    // Verify password
    const isPasswordValid = await bcryptjs.compare(password, user.password);
    if (!isPasswordValid) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Generate tokens
    const accessToken = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Update user login details and save refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLogin: new Date(),
      loginCount: { increment: 1 },
      refreshToken,
      },
    });

    // Remove sensitive data from response
    const { password: _, passwordResetToken, passwordResetExpires, emailVerificationToken, ...userResponse } = user;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.planType ? {
        plan: {
          name: userResponse.planType,
        },
        status: userResponse.subscriptionStatus || 'free',
        currentPeriodEnd: userResponse.subscriptionCurrentPeriodEnd,
      } : null,
    };

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseResponse = CaseConverter.toCamel(formattedUser);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: camelCaseResponse,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error: unknown) {
    safeLog.error('Login error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error during login',
    });
  }
};

// Refresh access token
const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Refresh token is required',
      });
      return;
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);

    // Find user and verify refresh token
    const user = await prisma.user.findUnique({
      where: { id: String(decoded.userId) },
    });
    if (!user || user.refreshToken !== refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
      });
      return;
    }

    // Check if user is still active
    if (!user.isActive) {
      res.status(401).json({
        success: false,
        message: 'Account is deactivated',
      });
      return;
    }

    // Generate new tokens
    const newAccessToken = generateToken(user.id);
    const newRefreshToken = generateRefreshToken(user.id);

    // Update refresh token in database
    await prisma.user.update({
      where: { id: user.id },
      data: { refreshToken: newRefreshToken },
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        },
      },
    });
  } catch (error: unknown) {
    safeLog.error('Token refresh error:', toError(error));

    if ((error as any).name === 'JsonWebTokenError' || (error as any).name === 'TokenExpiredError') {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token',
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during token refresh',
    });
  }
};

// Logout user
const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    // Clear refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refreshToken: null },
    });

    res.json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error: unknown) {
    safeLog.error('Logout error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout',
    });
  }
};

// Get current user profile
const getProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    // Remove sensitive data
    const { password: _, refreshToken, passwordResetToken, passwordResetExpires, emailVerificationToken, ...userResponse } = user;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.planType ? {
        plan: {
          name: userResponse.planType,
        },
        status: userResponse.subscriptionStatus || 'free',
        currentPeriodEnd: userResponse.subscriptionCurrentPeriodEnd,
      } : null,
    };

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseResponse = CaseConverter.toCamel(formattedUser);

    res.json({
      success: true,
      data: {
        user: camelCaseResponse,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Get profile error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching profile',
    });
  }
};

// Update user profile
const updateProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }
    
    const { firstName, lastName, currency, timezone, avatar } = req.body;

    const updateData: any = {};
    if (firstName !== undefined) updateData.firstName = firstName.trim();
    if (lastName !== undefined) updateData.lastName = lastName.trim();
    if (currency !== undefined) updateData.currency = currency.toUpperCase();
    if (timezone !== undefined) updateData.timezone = timezone;
    if (avatar !== undefined) updateData.avatar = avatar;

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: updateData,
    });

    // Remove sensitive data from response
    const { password: _, refreshToken, passwordResetToken, passwordResetExpires, emailVerificationToken, ...userResponse } = updatedUser;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.planType ? {
        plan: {
          name: userResponse.planType,
        },
        status: userResponse.subscriptionStatus || 'free',
        currentPeriodEnd: userResponse.subscriptionCurrentPeriodEnd,
      } : null,
    };

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseResponse = CaseConverter.toCamel(formattedUser);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: camelCaseResponse,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Update profile error:', toError(error));

    if ((error as any).name === 'SequelizeValidationError') {
      const errors = (error as any).errors.map((err: any): { field: string; message: string } => ({
        field: err.path,
        message: err.message,
      }));

      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while updating profile',
    });
  }
};

// Change password
const changePassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }
    
    const { currentPassword, newPassword } = req.body;

    // Verify current password
    const isCurrentPasswordValid = await bcryptjs.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
      });
      return;
    }

    // Check if new password is different from current
    const isSamePassword = await bcryptjs.compare(newPassword, user.password);
    if (isSamePassword) {
      res.status(400).json({
        success: false,
        message: 'New password must be different from current password',
      });
      return;
    }

    // Hash new password and update user
    const hashedNewPassword = await bcryptjs.hash(newPassword, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedNewPassword,
        refreshToken: null, // Clear all sessions
      },
    });

    res.json({
      success: true,
      message: 'Password changed successfully. Please login again.',
    });
  } catch (error: unknown) {
    safeLog.error('Change password error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error while changing password',
    });
  }
};

// Forgot password
const forgotPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (!user) {
      // Don't reveal if email exists or not
      res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
      return;
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpires: resetTokenExpires,
      },
    });

    // TODO: Send email with reset link
    // For now, we'll just return the token (remove this in production)
    safeLog.debug(`Password reset token for ${email}: ${resetToken}`);

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
      // Remove this in production:
      resetToken: process.env['NODE_ENV'] === 'development' ? resetToken : undefined,
    });
  } catch (error: unknown) {
    safeLog.error('Forgot password error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error while processing password reset request',
    });
  }
};

// Reset password
const resetPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, password } = req.body;

    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          gt: new Date(),
        },
      },
    });
    if (!user) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
      });
      return;
    }

    // Hash new password and update user
    const hashedPassword = await bcryptjs.hash(password, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        refreshToken: null, // Clear all sessions
      },
    });

    res.json({
      success: true,
      message: 'Password reset successfully. Please login with your new password.',
    });
  } catch (error: unknown) {
    safeLog.error('Reset password error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error while resetting password',
    });
  }
};

// Verify email (placeholder for future implementation)
const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.params;

    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token || null,
      },
    });
    if (!user) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
      });
      return;
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
      },
    });

    res.json({
      success: true,
      message: 'Email verified successfully',
    });
  } catch (error: unknown) {
    safeLog.error('Email verification error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error during email verification',
    });
  }
};

// Resend verification email (placeholder for future implementation)
const resendVerification = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    if (user.emailVerified) {
      res.status(400).json({
        success: false,
        message: 'Email is already verified',
      });
      return;
    }

    // Generate new verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    await prisma.user.update({
      where: { id: user.id },
      data: { emailVerificationToken: verificationToken },
    });

    // TODO: Send verification email
    safeLog.debug(`Verification token for ${user.email}: ${verificationToken}`);

    res.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error: unknown) {
    safeLog.error('Resend verification error:', toError(error));
    res.status(500).json({
      success: false,
      message: 'Internal server error while sending verification email',
    });
  }
};

export {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification,
};

export default {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification,
};
