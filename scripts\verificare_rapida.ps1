# Script de verificare rapida pentru aplicatia FinanceApp
# Verifica componentele esentiale si identifica problemele critice

function Write-Status {
    param([string]$Message, [string]$Status)
    if ($Status -eq "OK") {
        Write-Host "[OK] $Message" -ForegroundColor Green
    } elseif ($Status -eq "WARNING") {
        Write-Host "[WARNING] $Message" -ForegroundColor Yellow
    } else {
        Write-Host "[ERROR] $Message" -ForegroundColor Red
    }
}

Write-Host "VERIFICARE RAPIDA APLICATIE FINANCEAPP" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# 1. Verifica fisierele .env
Write-Host "`nVerifica configuratiile..." -ForegroundColor Yellow

if (Test-Path "backend\.env") {
    Write-Status "backend/.env exista" "OK"
} else {
    Write-Status "backend/.env lipseste" "ERROR"
}

if (Test-Path "frontend\.env") {
    Write-Status "frontend/.env exista" "OK"
} else {
    Write-Status "frontend/.env lipseste" "ERROR"
}

# 2. Verifica dependentele
Write-Host "`nVerifica dependentele..." -ForegroundColor Yellow

if (Test-Path "backend\node_modules") {
    Write-Status "Dependente backend instalate" "OK"
} else {
    Write-Status "Dependente backend lipsa" "ERROR"
}

if (Test-Path "frontend\node_modules") {
    Write-Status "Dependente frontend instalate" "OK"
} else {
    Write-Status "Dependente frontend lipsa" "ERROR"
}

# 3. Verifica build-urile
Write-Host "`nVerifica build-urile..." -ForegroundColor Yellow

if (Test-Path "backend\dist") {
    Write-Status "Build backend disponibil" "OK"
} else {
    Write-Status "Build backend lipseste" "WARNING"
}

if (Test-Path "frontend\dist") {
    Write-Status "Build frontend disponibil" "OK"
} else {
    Write-Status "Build frontend lipseste" "WARNING"
}

# 4. Verifica structura proiectului
Write-Host "`nVerifica structura proiectului..." -ForegroundColor Yellow

$criticalFiles = @(
    "backend\package.json",
    "frontend\package.json",
    "backend\src\app.ts",
    "frontend\src\main.tsx",
    "backend\prisma\schema.prisma"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Status "$file exista" "OK"
    } else {
        Write-Status "$file lipseste" "ERROR"
    }
}

# 5. Sumar si recomandari
Write-Host "`nSUMAR VERIFICARE" -ForegroundColor Green
Write-Host "================" -ForegroundColor Green

$issues = @()

if (-not (Test-Path "backend\.env")) { $issues += "Configurare backend" }
if (-not (Test-Path "frontend\.env")) { $issues += "Configurare frontend" }
if (-not (Test-Path "backend\node_modules")) { $issues += "Dependente backend" }
if (-not (Test-Path "frontend\node_modules")) { $issues += "Dependente frontend" }

if ($issues.Count -eq 0) {
    Write-Host "`nAPLICATIA PARE CONFIGURATA CORECT!" -ForegroundColor Green
    Write-Host "`nURMATORII PASI:" -ForegroundColor Cyan
    Write-Host "1. cd backend && npm run dev" -ForegroundColor White
    Write-Host "2. cd frontend && npm run dev" -ForegroundColor White
    Write-Host "3. Deschide http://localhost:5173" -ForegroundColor White
} else {
    Write-Host "`nPROBLEME IDENTIFICATE:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "- $issue" -ForegroundColor Red
    }
    
    Write-Host "`nACTIUNI RECOMANDATE:" -ForegroundColor Cyan
    if ($issues -contains "Configurare backend") {
        Write-Host "- Creaza backend/.env cu configuratiile necesare" -ForegroundColor White
    }
    if ($issues -contains "Configurare frontend") {
        Write-Host "- Creaza frontend/.env cu configuratiile necesare" -ForegroundColor White
    }
    if ($issues -contains "Dependente backend") {
        Write-Host "- Ruleaza 'cd backend && npm install'" -ForegroundColor White
    }
    if ($issues -contains "Dependente frontend") {
        Write-Host "- Ruleaza 'cd frontend && npm install'" -ForegroundColor White
    }
}

Write-Host "`nVERIFICARE COMPLETA!" -ForegroundColor Green