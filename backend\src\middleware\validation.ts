import { Request, Response, NextFunction } from 'express';
import * as <PERSON><PERSON> from 'joi';
import { validateDual, DualSchemaBuilder } from './dualValidation';
import { CaseConverter } from '../utils/caseConverter';
import { safeLog } from '../utils/safeLogger';

// Validation middleware with dual case support (snake_case și camelCase)
export const validate = (schema: Joi.ObjectSchema) => {
  // Folosește middleware-ul de validare duală pentru a accepta ambele formate
  return validateDual(schema, {
    preferredFormat: 'snake', // Formatul preferat pentru backend
    allowBothFormats: true,   // Acceptă ambele formate în timpul tranziției
    transformToPreferred: true, // Transformă automat la formatul preferat
    logValidations: process.env['NODE_ENV'] === 'development' // Logging doar în development
  });
};

// Versiunea originală a middleware-ului de validare (păstrată pentru compatibilitate)
export const validateOriginal = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body, { abortEarly: false });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
      return;
    }

    next();
  };
};

// Parameter validation middleware
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.params, { abortEarly: false });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      res.status(400).json({
        success: false,
        message: 'Invalid parameter',
        errors
      });
      return;
    }

    next();
  };
};

// Query validation middleware with dual case support
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Încearcă validarea cu schema originală (camelCase)
      const { error: camelError, value: camelValue } = schema.validate(req.query, { abortEarly: false });

      if (!camelError) {
        req.query = camelValue;
        return next();
      }

      // Dacă validarea camelCase eșuează, încearcă cu snake_case
      const snakeQuery = CaseConverter.toSnake(req.query);
      const snakeSchema = DualSchemaBuilder.toCamelCase(schema);
      const { error: snakeError, value: snakeValue } = snakeSchema.validate(snakeQuery, { abortEarly: false });

      if (!snakeError) {
        // Transformă înapoi la camelCase pentru consistență
        req.query = CaseConverter.toCamel(snakeValue);
        return next();
      }

      // Ambele validări au eșuat - returnează eroarea mai detaliată
      const finalError = camelError.details.length <= snakeError.details.length ? camelError : snakeError;

      const errors = finalError.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors,
        hint: 'Please use consistent naming convention (either snake_case or camelCase)'
      });

    } catch (error) {
      safeLog.error('Error in query validation middleware:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during validation'
      });
    }
  };
};

// User validation schemas (camelCase format)
export const userSchemas = {
  register: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': 'Password must be at least 6 characters long',
      'any.required': 'Password is required'
    }),
    firstName: Joi.string().min(2).max(50).required().messages({
      'string.min': 'First name must be at least 2 characters long',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
    lastName: Joi.string().min(2).max(50).required().messages({
      'string.min': 'Last name must be at least 2 characters long',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),
    currency: Joi.string().length(3).optional().default('USD').messages({
      'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
    }),
    timezone: Joi.string().optional().default('UTC').messages({
      'string.base': 'Timezone must be a string'
    })
  }),

  login: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().required().messages({
      'any.required': 'Password is required'
    })
  }),

  updateProfile: Joi.object({
    firstName: Joi.string().min(2).max(50).optional().messages({
      'string.min': 'First name must be at least 2 characters long',
      'string.max': 'First name cannot exceed 50 characters'
    }),
    lastName: Joi.string().min(2).max(50).optional().messages({
      'string.min': 'Last name must be at least 2 characters long',
      'string.max': 'Last name cannot exceed 50 characters'
    }),
    currency: Joi.string().length(3).optional().messages({
      'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
    }),
    timezone: Joi.string().optional().messages({
      'string.base': 'Timezone must be a string'
    }),
    avatar: Joi.string().uri().optional().allow('').messages({
      'string.uri': 'Avatar must be a valid URL'
    })
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required'
    }),
    newPassword: Joi.string().min(6).required().messages({
      'string.min': 'New password must be at least 6 characters long',
      'any.required': 'New password is required'
    })
  }),

  forgotPassword: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
  }),

  resetPassword: Joi.object({
    token: Joi.string().required().messages({
      'any.required': 'Reset token is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': 'Password must be at least 6 characters long',
      'any.required': 'Password is required'
    })
  })
};

// Subscription validation schemas
export const subscriptionSchemas = {
  create: Joi.object({
    planId: Joi.string().required().messages({
      'any.required': 'Plan ID is required'
    }),
    paymentMethodId: Joi.string().required().messages({
      'any.required': 'Payment method ID is required'
    }),
    billingCycle: Joi.string().valid('monthly', 'yearly').required().messages({
      'any.only': 'Billing cycle must be monthly or yearly',
      'any.required': 'Billing cycle is required'
    }),
    couponCode: Joi.string().max(50).optional().messages({
      'string.max': 'Coupon code cannot exceed 50 characters'
    })
  }),

  update: Joi.object({
    planId: Joi.string().optional(),
    billingCycle: Joi.string().valid('monthly', 'yearly').optional().messages({
      'any.only': 'Billing cycle must be monthly or yearly'
    }),
    status: Joi.string().valid('active', 'cancelled', 'past_due', 'unpaid').optional().messages({
      'any.only': 'Status must be one of: active, cancelled, past_due, unpaid'
    })
  }),

  cancel: Joi.object({
    reason: Joi.string().max(500).optional().messages({
      'string.max': 'Cancellation reason cannot exceed 500 characters'
    }),
    cancelAtPeriodEnd: Joi.boolean().optional().default(true).messages({
      'boolean.base': 'Cancel at period end must be a boolean'
    })
  })
};

// Plan validation schemas
export const planSchemas = {
  create: Joi.object({
    name: Joi.string().min(1).max(100).required().messages({
      'string.min': 'Plan name is required',
      'string.max': 'Plan name cannot exceed 100 characters',
      'any.required': 'Plan name is required'
    }),
    description: Joi.string().max(500).optional().messages({
      'string.max': 'Description cannot exceed 500 characters'
    }),
    price: Joi.number().positive().precision(2).required().messages({
      'number.base': 'Price must be a number',
      'number.positive': 'Price must be positive',
      'number.precision': 'Price can have at most 2 decimal places',
      'any.required': 'Price is required'
    }),
    currency: Joi.string().length(3).uppercase().required().messages({
      'string.length': 'Currency must be 3 characters',
      'any.required': 'Currency is required'
    }),
    interval: Joi.string().valid('month', 'year').required().messages({
      'any.only': 'Interval must be month or year',
      'any.required': 'Interval is required'
    }),
    features: Joi.array().items(Joi.string().max(200)).max(20).required().messages({
      'array.base': 'Features must be an array',
      'string.max': 'Each feature cannot exceed 200 characters',
      'array.max': 'Maximum 20 features allowed',
      'any.required': 'Features are required'
    }),
    maxExpenses: Joi.number().integer().positive().optional().messages({
      'number.base': 'Max expenses must be a number',
      'number.integer': 'Max expenses must be an integer',
      'number.positive': 'Max expenses must be positive'
    }),
    maxCategories: Joi.number().integer().positive().optional().messages({
      'number.base': 'Max categories must be a number',
      'number.integer': 'Max categories must be an integer',
      'number.positive': 'Max categories must be positive'
    }),
    isActive: Joi.boolean().optional().default(true).messages({
      'boolean.base': 'Is active must be a boolean'
    }),
    sortOrder: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    })
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(100).optional().messages({
      'string.min': 'Plan name cannot be empty',
      'string.max': 'Plan name cannot exceed 100 characters'
    }),
    description: Joi.string().max(500).optional().allow('').messages({
      'string.max': 'Description cannot exceed 500 characters'
    }),
    price: Joi.number().positive().precision(2).optional().messages({
      'number.base': 'Price must be a number',
      'number.positive': 'Price must be positive',
      'number.precision': 'Price can have at most 2 decimal places'
    }),
    features: Joi.array().items(Joi.string().max(200)).max(20).optional().messages({
      'array.base': 'Features must be an array',
      'string.max': 'Each feature cannot exceed 200 characters',
      'array.max': 'Maximum 20 features allowed'
    }),
    maxExpenses: Joi.number().integer().positive().optional().messages({
      'number.base': 'Max expenses must be a number',
      'number.integer': 'Max expenses must be an integer',
      'number.positive': 'Max expenses must be positive'
    }),
    maxCategories: Joi.number().integer().positive().optional().messages({
      'number.base': 'Max categories must be a number',
      'number.integer': 'Max categories must be an integer',
      'number.positive': 'Max categories must be positive'
    }),
    isActive: Joi.boolean().optional().messages({
      'boolean.base': 'Is active must be a boolean'
    }),
    sortOrder: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    })
  })
};

// Admin validation schemas
export const adminSchemas = {
  userUpdate: Joi.object({
    email: Joi.string().email().optional().messages({
      'string.email': 'Invalid email format'
    }),
    firstName: Joi.string().min(1).max(50).optional().messages({
      'string.min': 'First name cannot be empty',
      'string.max': 'First name cannot exceed 50 characters'
    }),
    lastName: Joi.string().min(1).max(50).optional().messages({
      'string.min': 'Last name cannot be empty',
      'string.max': 'Last name cannot exceed 50 characters'
    }),
    isActive: Joi.boolean().optional().messages({
      'boolean.base': 'Is active must be a boolean'
    }),
    role: Joi.string().valid('user', 'admin').optional().messages({
      'any.only': 'Role must be user or admin'
    }),
    subscriptionStatus: Joi.string().valid('active', 'cancelled', 'past_due', 'unpaid').optional().messages({
      'any.only': 'Subscription status must be one of: active, cancelled, past_due, unpaid'
    })
  }),

  systemAlert: Joi.object({
    title: Joi.string().min(1).max(200).required().messages({
      'string.min': 'Alert title is required',
      'string.max': 'Alert title cannot exceed 200 characters',
      'any.required': 'Alert title is required'
    }),
    message: Joi.string().min(1).max(1000).required().messages({
      'string.min': 'Alert message is required',
      'string.max': 'Alert message cannot exceed 1000 characters',
      'any.required': 'Alert message is required'
    }),
    type: Joi.string().valid('info', 'warning', 'error', 'success').required().messages({
      'any.only': 'Alert type must be one of: info, warning, error, success',
      'any.required': 'Alert type is required'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional().default('medium').messages({
      'any.only': 'Priority must be one of: low, medium, high, critical'
    }),
    expiresAt: Joi.date().iso().optional().messages({
      'date.base': 'Invalid expiration date format',
      'date.format': 'Expiration date must be in ISO format'
    })
  }),

  statsQuery: Joi.object({
    startDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid start date format',
      'date.format': 'Start date must be in ISO format'
    }),
    endDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid end date format',
      'date.format': 'End date must be in ISO format'
    }),
    groupBy: Joi.string().valid('day', 'week', 'month', 'year').optional().default('month').messages({
      'any.only': 'Group by must be one of: day, week, month, year'
    }),
    includeInactive: Joi.boolean().optional().default(false).messages({
      'boolean.base': 'Include inactive must be a boolean'
    })
  })
};

// Webhook validation schemas
export const webhookSchemas = {
  stripe: Joi.object({
    id: Joi.string().required(),
    object: Joi.string().required(),
    apiVersion: Joi.string().optional(),
    data: Joi.object().required(),
    created: Joi.number().required(),
    livemode: Joi.boolean().required(),
    pendingWebhooks: Joi.number().required(),
    request: Joi.object().optional()
  }),

  paypal: Joi.object({
    id: Joi.string().required(),
    eventType: Joi.string().required(),
    resourceType: Joi.string().required(),
    resource: Joi.object().required(),
    createTime: Joi.string().required(),
    eventVersion: Joi.string().optional()
  })
};

// Query validation schemas
export const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(20).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
    sortBy: Joi.string().optional().messages({
      'string.base': 'Sort by must be a string'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc').messages({
      'any.only': 'Sort order must be either asc or desc'
    })
  }),

  dateRange: Joi.object({
    startDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid start date format',
      'date.format': 'Start date must be in ISO format'
    }),
    endDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid end date format',
      'date.format': 'End date must be in ISO format'
    })
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate && value.startDate > value.endDate) {
      return helpers.error('any.invalid', { message: 'Start date must be before end date' });
    }
    return value;
  })
};

// Category validation schemas (camelCase format)
export const categorySchemas = {
  create: Joi.object({
    name: Joi.string().min(1).max(50).required().messages({
      'string.min': 'Category name is required',
      'string.max': 'Category name cannot exceed 50 characters',
      'any.required': 'Category name is required'
    }),
    description: Joi.string().max(200).optional().messages({
      'string.max': 'Description cannot exceed 200 characters'
    }),
    color: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
      'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
    }),
    icon: Joi.string().max(50).optional().messages({
      'string.max': 'Icon name cannot exceed 50 characters'
    }),
    sortOrder: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    }),
    budgetLimit: Joi.number().positive().precision(2).optional().messages({
      'number.base': 'Budget limit must be a number',
      'number.positive': 'Budget limit must be positive',
      'number.precision': 'Budget limit can have at most 2 decimal places'
    }),
    budgetPeriod: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly').optional().default('monthly').messages({
      'any.only': 'Budget period must be one of: daily, weekly, monthly, yearly'
    }),
    isDefault: Joi.boolean().optional().default(false).messages({
      'boolean.base': 'Is default must be a boolean'
    })
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(50).optional().messages({
      'string.min': 'Category name cannot be empty',
      'string.max': 'Category name cannot exceed 50 characters'
    }),
    description: Joi.string().max(200).optional().allow('').messages({
      'string.max': 'Description cannot exceed 200 characters'
    }),
    color: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
      'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
    }),
    icon: Joi.string().max(50).optional().messages({
      'string.max': 'Icon name cannot exceed 50 characters'
    }),
    sortOrder: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    }),
    budgetLimit: Joi.number().positive().precision(2).optional().messages({
      'number.base': 'Budget limit must be a number',
      'number.positive': 'Budget limit must be positive',
      'number.precision': 'Budget limit can have at most 2 decimal places'
    }),
    budgetPeriod: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly').optional().messages({
      'any.only': 'Budget period must be one of: daily, weekly, monthly, yearly'
    }),
    isActive: Joi.boolean().optional().messages({
      'boolean.base': 'Is active must be a boolean'
    }),
    isDefault: Joi.boolean().optional().messages({
      'boolean.base': 'Is default must be a boolean'
    })
  })
};

// Parameter validation schemas (CUID format)
export const paramSchemas = {
  id: Joi.object({
    id: Joi.string().required().messages({
      'string.base': 'ID must be a string',
      'any.required': 'ID is required'
    })
  }),

  cuid: Joi.object({
    id: Joi.string().pattern(/^c[a-z0-9]{24}$/).required().messages({
      'string.pattern.base': 'Invalid CUID format',
      'any.required': 'CUID is required'
    })
  })
};

// Expense validation schemas (camelCase format)
export const expenseSchemas = {
  create: Joi.object({
    amount: Joi.number().positive().precision(2).required().messages({
      'number.base': 'Amount must be a number',
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount can have at most 2 decimal places',
      'any.required': 'Amount is required'
    }),
    description: Joi.string().min(1).max(255).required().messages({
      'string.min': 'Description is required',
      'string.max': 'Description cannot exceed 255 characters',
      'any.required': 'Description is required'
    }),
    categoryId: Joi.string().required().messages({
      'any.required': 'Category is required'
    }),
    date: Joi.date().iso().required().messages({
      'date.base': 'Invalid date format',
      'date.format': 'Date must be in ISO format',
      'any.required': 'Date is required'
    }),
    paymentMethod: Joi.string().valid('cash', 'card', 'bank_transfer', 'digital_wallet', 'other').required().messages({
      'any.only': 'Payment method must be one of: cash, card, bank_transfer, digital_wallet, other',
      'any.required': 'Payment method is required'
    }),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
      'array.base': 'Tags must be an array',
      'string.max': 'Each tag cannot exceed 50 characters',
      'array.max': 'Maximum 10 tags allowed'
    }),
    notes: Joi.string().max(1000).optional().allow('').messages({
      'string.max': 'Notes cannot exceed 1000 characters'
    }),
    receiptUrl: Joi.string().uri().optional().allow('').messages({
      'string.uri': 'Receipt URL must be a valid URL'
    }),
    location: Joi.string().max(255).optional().allow('').messages({
      'string.max': 'Location cannot exceed 255 characters'
    }),
    isRecurring: Joi.boolean().optional().default(false).messages({
      'boolean.base': 'Is recurring must be a boolean'
    }),
    recurringFrequency: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly').optional().messages({
      'any.only': 'Recurring frequency must be one of: daily, weekly, monthly, yearly'
    }),
    recurringEndDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid recurring end date format',
      'date.format': 'Recurring end date must be in ISO format'
    })
  }),

  update: Joi.object({
    amount: Joi.number().positive().precision(2).optional().messages({
      'number.base': 'Amount must be a number',
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount can have at most 2 decimal places'
    }),
    description: Joi.string().min(1).max(255).optional().messages({
      'string.min': 'Description cannot be empty',
      'string.max': 'Description cannot exceed 255 characters'
    }),
    categoryId: Joi.string().optional().messages({
      'string.base': 'Category ID must be a string'
    }),
    date: Joi.date().iso().optional().messages({
      'date.base': 'Invalid date format',
      'date.format': 'Date must be in ISO format'
    }),
    paymentMethod: Joi.string().valid('cash', 'card', 'bank_transfer', 'digital_wallet', 'other').optional().messages({
      'any.only': 'Payment method must be one of: cash, card, bank_transfer, digital_wallet, other'
    }),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
      'array.base': 'Tags must be an array',
      'string.max': 'Each tag cannot exceed 50 characters',
      'array.max': 'Maximum 10 tags allowed'
    }),
    notes: Joi.string().max(1000).optional().allow('').messages({
      'string.max': 'Notes cannot exceed 1000 characters'
    }),
    receiptUrl: Joi.string().uri().optional().allow('').messages({
      'string.uri': 'Receipt URL must be a valid URL'
    }),
    location: Joi.string().max(255).optional().allow('').messages({
      'string.max': 'Location cannot exceed 255 characters'
    }),
    isRecurring: Joi.boolean().optional().messages({
      'boolean.base': 'Is recurring must be a boolean'
    }),
    recurringFrequency: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly').optional().messages({
      'any.only': 'Recurring frequency must be one of: daily, weekly, monthly, yearly'
    }),
    recurringEndDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid recurring end date format',
      'date.format': 'Recurring end date must be in ISO format'
    })
  }),

  query: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(20).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
    categoryId: Joi.string().optional().messages({
      'string.base': 'Category ID must be a string'
    }),
    startDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid start date format',
      'date.format': 'Start date must be in ISO format'
    }),
    endDate: Joi.date().iso().optional().messages({
      'date.base': 'Invalid end date format',
      'date.format': 'End date must be in ISO format'
    }),
    minAmount: Joi.number().positive().optional().messages({
      'number.base': 'Minimum amount must be a number',
      'number.positive': 'Minimum amount must be positive'
    }),
    maxAmount: Joi.number().positive().optional().messages({
      'number.base': 'Maximum amount must be a number',
      'number.positive': 'Maximum amount must be positive'
    }),
    tags: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).optional().messages({
      'alternatives.match': 'Tags must be a string or array of strings'
    }),
    search: Joi.string().max(255).optional().messages({
      'string.max': 'Search term cannot exceed 255 characters'
    }),
    sortBy: Joi.string().valid('date', 'amount', 'description', 'createdAt').optional().default('date').messages({
      'any.only': 'Sort by must be one of: date, amount, description, createdAt'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc').messages({
      'any.only': 'Sort order must be either asc or desc'
    }),
    paymentMethod: Joi.string().valid('cash', 'card', 'bank_transfer', 'digital_wallet', 'other').optional().messages({
      'any.only': 'Payment method must be one of: cash, card, bank_transfer, digital_wallet, other'
    }),
    isRecurring: Joi.boolean().optional().messages({
      'boolean.base': 'Is recurring must be a boolean'
    })
  })
};