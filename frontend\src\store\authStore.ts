import toast from 'react-hot-toast';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

import AUTH_CONFIG from '../config/auth';
import { authService } from '../services/authService';
import { safeLog } from '../utils/safeLogger';
import type {
  User,
  LoginCredentials,
  RegisterData,
  UpdateProfileData,
  ChangePasswordData,
  // ApiResponse, // Not used currently
} from '../types';

// Interfețe pentru tipuri
interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastActivity: number | null;
}

interface AuthActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setTokens: (accessToken: string | null, refreshToken: string | null) => void;
  clearAuth: () => void;
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; message?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<{ success: boolean; message?: string }>;
  updateProfile: (
    profileData: UpdateProfileData,
  ) => Promise<{ success: boolean; message?: string }>;
  changePassword: (
    passwordData: ChangePasswordData,
  ) => Promise<{ success: boolean; message?: string }>;
  forgotPassword: (email: string) => Promise<{ success: boolean; message?: string }>;
  resetPassword: (
    token: string,
    newPassword: string,
  ) => Promise<{ success: boolean; message?: string }>;
  initializeAuth: () => Promise<void>;
  checkUserActivity: () => void;
  updateActivity: () => void;
}

type AuthStore = AuthState & AuthActions;

// Tipuri de acțiuni pentru reducer
// const _AUTH_ACTIONS = { // Not used currently
//   SET_LOADING: 'SET_LOADING',
//   SET_USER: 'SET_USER',
//   SET_TOKENS: 'SET_TOKENS',
//   CLEAR_AUTH: 'CLEAR_AUTH',
//   SET_ERROR: 'SET_ERROR',
//   CLEAR_ERROR: 'CLEAR_ERROR',
// } as const;

// Starea inițială
const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  lastActivity: null,
};

// Store pentru autentificare
export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Starea inițială
      ...initialState,

      // Acțiuni
      setLoading: loading => {
        set({ isLoading: loading });
      },

      setError: error => {
        set({ error });
        if (error) {
          toast.error(error);
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: user => {
        set({
          user,
          isAuthenticated: !!user,
          lastActivity: Date.now(),
        });
      },

      setTokens: (accessToken, refreshToken) => {
        set({
          accessToken,
          refreshToken,
          lastActivity: Date.now(),
        });

        // Setează token-ul în authService pentru cererile viitoare
        if (accessToken) {
          authService.setAuthToken(accessToken);
        }
      },

      clearAuth: () => {
        safeLog.debug('🧹 Clearing auth state...');

        // Curăță token-urile din authService și localStorage
        authService.clearAuthToken();

        // Resetează starea de autentificare
        set(initialState);

        // Șterge și din localStorage
        localStorage.removeItem('auth-storage');

        safeLog.debug('✅ Auth state cleared successfully');
      },

      // Funcții pentru autentificare
      login: async credentials => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.login(credentials);

          if (response.success) {
            const { user, tokens } = response.data as {
              user: User;
              tokens: {
                accessToken: string;
                refreshToken: string;
              };
            };
            const { accessToken, refreshToken } = tokens;

            set({
              user,
              accessToken,
              refreshToken,
              isAuthenticated: true,
              isLoading: false,
              lastActivity: Date.now(),
            });

            authService.setAuthToken(accessToken);
            toast.success('Autentificare reușită!');

            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la autentificare', isLoading: false });
            return { success: false, message: response.message || 'Eroare necunoscută' };
          }
        } catch (error: unknown) {
          const errorMessage = (error as any).response?.data?.message || 'Eroare la autentificare';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      register: async userData => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.register(userData);

          if (response.success) {
            set({ isLoading: false });
            toast.success('Cont creat cu succes! Te poți autentifica acum.');
            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la înregistrare', isLoading: false });
            return { success: false, message: response.message || 'Eroare la înregistrare' };
          }
        } catch (error: unknown) {
          const errorMessage = (error as any).response?.data?.message || 'Eroare la înregistrare';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      logout: async () => {
        try {
          const { refreshToken } = get();

          if (refreshToken) {
            await authService.logout(refreshToken);
          }

          get().clearAuth();
          toast.success('Deconectare reușită!');
        } catch (error: unknown) {
          // Chiar dacă logout-ul de pe server eșuează, curățăm starea locală
          get().clearAuth();
          safeLog.error('Eroare la logout:', error as Error);
        }
      },

      refreshAccessToken: async () => {
        try {
          const { refreshToken } = get();

          safeLog.debug('🔄 Attempting to refresh access token...');

          if (!refreshToken) {
            safeLog.debug('❌ No refresh token available');
            get().clearAuth();
            return { success: false, message: 'Nu există refresh token' };
          }

          const response = await authService.refreshToken(refreshToken);

          if (response.success) {
            const { accessToken: newAccessToken, refreshToken: newRefreshToken } =
              response.data as {
                accessToken: string;
                refreshToken: string;
              };

            safeLog.debug('✅ Token refresh successful');

            // Actualizează token-urile în store
            set({
              accessToken: newAccessToken,
              refreshToken: newRefreshToken,
              lastActivity: Date.now(),
            });

            // Actualizează token-ul în authService
            authService.setAuthToken(newAccessToken);

            return { success: true };
          } else {
            safeLog.debug('❌ Token refresh failed:');
            // Refresh token invalid sau expirat
            get().clearAuth();
            return { success: false, message: response.message || 'Eroare la refresh token' };
          }
        } catch (error: unknown) {
          safeLog.error('❌ Error during token refresh:', error as Error);
          get().clearAuth();
          return { success: false, message: 'Eroare la reîmprospătarea token-ului' };
        }
      },

      updateProfile: async profileData => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.updateProfile(profileData);

          if (response.success) {
            const currentUser = get().user;
            if (currentUser) {
              set({
                user: { ...currentUser, ...(response.data as Partial<User>) },
                isLoading: false,
              });
            }

            toast.success('Profil actualizat cu succes!');
            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la actualizare profil', isLoading: false });
            return { success: false, message: response.message || 'Eroare la actualizare profil' };
          }
        } catch (error: unknown) {
          const errorMessage =
            (error as any).response?.data?.message || 'Eroare la actualizarea profilului';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      changePassword: async passwordData => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.changePassword(passwordData);

          if (response.success) {
            set({ isLoading: false });
            toast.success('Parola a fost schimbată cu succes!');
            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la schimbarea parolei', isLoading: false });
            return { success: false, message: response.message || 'Eroare la schimbarea parolei' };
          }
        } catch (error: unknown) {
          const errorMessage =
            (error as any).response?.data?.message || 'Eroare la schimbarea parolei';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      forgotPassword: async email => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.forgotPassword(email);

          if (response.success) {
            set({ isLoading: false });
            toast.success('Link-ul de resetare a fost trimis pe email!');
            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la trimiterea email-ului', isLoading: false });
            return {
              success: false,
              message: response.message || 'Eroare la trimiterea email-ului',
            };
          }
        } catch (error: unknown) {
          const errorMessage =
            (error as any).response?.data?.message || 'Eroare la trimiterea email-ului';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      resetPassword: async (token, newPassword) => {
        try {
          set({ isLoading: true, error: null });

          const response = await authService.resetPassword(token, newPassword);

          if (response.success) {
            set({ isLoading: false });
            toast.success('Parola a fost resetată cu succes!');
            return { success: true };
          } else {
            set({ error: response.message || 'Eroare la resetarea parolei', isLoading: false });
            return { success: false, message: response.message || 'Eroare la resetarea parolei' };
          }
        } catch (error: unknown) {
          const errorMessage =
            (error as any).response?.data?.message || 'Eroare la resetarea parolei';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      // Inițializează autentificarea la pornirea aplicației
      initializeAuth: async () => {
        try {
          set({ isLoading: true });

          const { accessToken, refreshToken, user } = get();

          safeLog.debug('🚀 Initializing auth...');

          if (accessToken && refreshToken && user) {
            // Verifică dacă token-ul este expirat
            if (authService.isTokenExpired(accessToken)) {
              safeLog.debug('🔄 Access token expired, attempting refresh...');

              // Token expirat, încearcă să-l reîmprospătezi
              const refreshResult = await get().refreshAccessToken();
              if (refreshResult.success) {
                safeLog.debug('✅ Token refreshed successfully during init');
                set({
                  isAuthenticated: true,
                  isLoading: false,
                  lastActivity: Date.now(),
                });
              } else {
                safeLog.debug('❌ Token refresh failed during init');
                get().clearAuth();
                set({ isLoading: false });
              }
            } else {
              // Token-ul pare valid, setează-l și verifică cu serverul
              authService.setAuthToken(accessToken);

              try {
                // Verifică validitatea token-ului cu serverul
                const profileResponse = await authService.getProfile();

                if (profileResponse.success && profileResponse.data) {
                  safeLog.debug('✅ Auth initialized successfully with valid token');
                  set({
                    user: profileResponse.data as User,
                    isAuthenticated: true,
                    isLoading: false,
                    lastActivity: Date.now(),
                  });
                } else {
                  safeLog.debug('❌ Profile fetch failed, token might be invalid');
                  // Token invalid pe server, încearcă să-l reîmprospătezi
                  const refreshResult = await get().refreshAccessToken();
                  if (!refreshResult.success) {
                    get().clearAuth();
                  }
                  set({ isLoading: false });
                }
              } catch (error: unknown) {
                safeLog.debug('❌ Error during profile fetch:');
                // Eroare la verificarea profilului, încearcă refresh
                if (refreshToken) {
                  const refreshResult = await get().refreshAccessToken();
                  if (!refreshResult.success) {
                    get().clearAuth();
                  }
                } else {
                  get().clearAuth();
                }
                set({ isLoading: false });
              }
            }
          } else {
            safeLog.debug('❌ Missing auth data, clearing auth state');
            get().clearAuth();
            set({ isLoading: false });
          }
        } catch (error: unknown) {
          safeLog.error('❌ Error during auth initialization:', error as Error);
          get().clearAuth();
          set({ isLoading: false });
        }
      },

      // Verifică activitatea utilizatorului
      checkUserActivity: () => {
        const { lastActivity, isAuthenticated } = get();

        if (isAuthenticated && lastActivity) {
          const now = Date.now();
          const timeSinceLastActivity = now - lastActivity;

          safeLog.debug('⏰ Checking user activity...');

          // Deconectează utilizatorul după perioada de inactivitate configurată
          if (timeSinceLastActivity > AUTH_CONFIG.INACTIVITY_TIMEOUT) {
            safeLog.debug('⏰ Session expired due to inactivity');
            get().logout();
            toast.error(`${AUTH_CONFIG.ERROR_MESSAGES.SESSION_EXPIRED} din cauza inactivității`);
          }
        }
      },

      // Actualizează ultima activitate
      updateActivity: () => {
        const { isAuthenticated } = get();
        if (isAuthenticated) {
          set({ lastActivity: Date.now() });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
        lastActivity: state.lastActivity,
      }),
      onRehydrateStorage: () => state => {
        if (state?.accessToken) {
          authService.setAuthToken(state.accessToken);
        }
      },
    },
  ),
);

// Interfețe pentru hook-uri
interface AuthHookReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActionsHookReturn {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; message?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<{ success: boolean; message?: string }>;
  updateProfile: (
    profileData: UpdateProfileData,
  ) => Promise<{ success: boolean; message?: string }>;
  changePassword: (
    passwordData: ChangePasswordData,
  ) => Promise<{ success: boolean; message?: string }>;
  forgotPassword: (email: string) => Promise<{ success: boolean; message?: string }>;
  resetPassword: (
    token: string,
    newPassword: string,
  ) => Promise<{ success: boolean; message?: string }>;
  clearError: () => void;
  updateActivity: () => void;
}

interface PermissionsHookReturn {
  isAdmin: boolean;
  canManageUsers: boolean;
  canExportData: boolean;
  canImportData: boolean;
}

// Selector hooks pentru performanță optimă
export const useAuth = (): AuthHookReturn => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
  };
};

export const useAuthActions = (): AuthActionsHookReturn => {
  const store = useAuthStore();
  return {
    login: store.login,
    register: store.register,
    logout: store.logout,
    refreshAccessToken: store.refreshAccessToken,
    updateProfile: store.updateProfile,
    changePassword: store.changePassword,
    forgotPassword: store.forgotPassword,
    resetPassword: store.resetPassword,
    clearError: store.clearError,
    updateActivity: store.updateActivity,
  };
};

// Hook pentru verificarea permisiunilor
export const usePermissions = (): PermissionsHookReturn => {
  const { user } = useAuth();

  return {
    isAdmin: user?.role === 'admin',
    canManageUsers: user?.role === 'admin',
    canExportData: true, // Toți utilizatorii pot exporta propriile date
    canImportData: true, // Toți utilizatorii pot importa date
  };
};
