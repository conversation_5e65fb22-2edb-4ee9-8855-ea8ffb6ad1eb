import {
  EyeIcon,
  EyeSlashIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import React, { useState, forwardRef } from 'react';

import { cn } from '../../utils/helpers';

type InputSize = 'sm' | 'md' | 'lg';
type InputType =
  | 'text'
  | 'email'
  | 'password'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'date'
  | 'time'
  | 'datetime-local'
  | 'month'
  | 'week'
  | 'color'
  | 'file'
  | 'range'
  | 'hidden'
  | 'checkbox'
  | 'radio';

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'type'> {
  label?: string;
  type?: InputType;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string | undefined;
  success?: string;
  hint?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  size?: InputSize;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  id?: string;
  name?: string;
}

/**
 * Componenta Input reutilizabilă pentru formulare
 */
const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      type = 'text',
      placeholder,
      value,
      onChange,
      onBlur,
      onFocus,
      error,
      success,
      hint,
      required = false,
      disabled = false,
      readOnly = false,
      size = 'md',
      leftIcon,
      rightIcon,
      className = '',
      inputClassName = '',
      labelClassName = '',
      id,
      name,
      ...rest
    },
    ref,
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [, setFocused] = useState(false);

    // Generează un ID unic dacă nu este furnizat
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    // Determină tipul real al input-ului
    const inputType = type === 'password' && showPassword ? 'text' : type;

    // Clase pentru mărimi
    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2.5 text-sm',
      lg: 'px-4 py-3 text-base',
    };

    // Clase pentru iconițe în funcție de mărime
    const iconSizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
    };

    // Determină starea input-ului
    const hasError = Boolean(error);
    const hasSuccess = Boolean(success) && !hasError;
    // const hasIcon = Boolean(leftIcon || rightIcon || type === 'password'); // Not used currently

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      onBlur?.(e);
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={cn('w-full', className)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium mb-2',
              hasError ? 'text-red-700' : 'text-gray-700',
              disabled && 'text-gray-400',
              labelClassName,
            )}
          >
            {label}
            {required && (
              <span className="text-red-500 ml-1" aria-label="obligatoriu">
                *
              </span>
            )}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span
                className={cn(iconSizeClasses[size], hasError ? 'text-red-400' : 'text-gray-400')}
              >
                {leftIcon}
              </span>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            id={inputId}
            name={name}
            type={inputType}
            value={value}
            onChange={onChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            readOnly={readOnly}
            className={cn(
              // Clase de bază
              'block w-full border rounded-lg transition-all duration-200',
              'focus:outline-none focus:ring-2 focus:ring-offset-0',
              'placeholder:text-gray-400',

              // Mărime
              sizeClasses[size],

              // Padding pentru iconițe
              leftIcon && 'pl-10',
              (rightIcon || type === 'password') && 'pr-10',

              // Stări
              !disabled &&
                !readOnly && [
                  hasError
                    ? ['border-red-300 text-red-900', 'focus:border-red-500 focus:ring-red-500']
                    : hasSuccess
                      ? [
                          'border-green-300 text-green-900',
                          'focus:border-green-500 focus:ring-green-500',
                        ]
                      : [
                          'border-gray-300 text-gray-900',
                          'focus:border-primary-500 focus:ring-primary-500',
                          'hover:border-gray-400',
                        ],
                ],

              disabled && ['bg-gray-50 border-gray-200 text-gray-500', 'cursor-not-allowed'],

              readOnly && ['bg-gray-50 border-gray-200', 'cursor-default'],

              inputClassName,
            )}
            {...rest}
          />

          {/* Right Icon / Password Toggle / Status Icon */}
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {type === 'password' ? (
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className={cn(
                  'text-gray-400 hover:text-gray-600 focus:outline-none',
                  iconSizeClasses[size],
                )}
                aria-label={showPassword ? 'Ascunde parola' : 'Arată parola'}
              >
                {showPassword ? (
                  <EyeSlashIcon className={iconSizeClasses[size]} />
                ) : (
                  <EyeIcon className={iconSizeClasses[size]} />
                )}
              </button>
            ) : hasError ? (
              <ExclamationCircleIcon className={cn(iconSizeClasses[size], 'text-red-400')} />
            ) : hasSuccess ? (
              <CheckCircleIcon className={cn(iconSizeClasses[size], 'text-green-400')} />
            ) : rightIcon ? (
              <span className={cn(iconSizeClasses[size], 'text-gray-400')}>{rightIcon}</span>
            ) : null}
          </div>
        </div>

        {/* Help Text / Error / Success Messages */}
        {(error || success || hint) && (
          <div className="mt-2 flex items-start space-x-1">
            {(error || success) && (
              <span className="flex-shrink-0 mt-0.5">
                {error ? (
                  <ExclamationCircleIcon className="h-4 w-4 text-red-400" />
                ) : (
                  <CheckCircleIcon className="h-4 w-4 text-green-400" />
                )}
              </span>
            )}

            {hint && !error && !success && (
              <InformationCircleIcon className="h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5" />
            )}

            <p
              className={cn(
                'text-sm',
                error ? 'text-red-600' : success ? 'text-green-600' : 'text-gray-600',
              )}
            >
              {error || success || hint}
            </p>
          </div>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';

/**
 * Input pentru email cu validare
 */
export const EmailInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>((props, ref) => (
  <Input ref={ref} type="email" placeholder="<EMAIL>" {...props} />
));

EmailInput.displayName = 'EmailInput';

/**
 * Input pentru parolă
 */
export const PasswordInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="password" placeholder="••••••••" {...props} />,
);

PasswordInput.displayName = 'PasswordInput';

interface NumberInputProps extends Omit<InputProps, 'type'> {
  min?: number;
  max?: number;
  step?: number;
}

/**
 * Input pentru numere
 */
export const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
  ({ min, max, step = 1, ...props }, ref) => (
    <Input ref={ref} type="number" min={min} max={max} step={step} {...props} />
  ),
);

NumberInput.displayName = 'NumberInput';

interface SearchInputProps extends Omit<InputProps, 'type'> {
  onSearch?: (value: string) => void;
}

/**
 * Input pentru căutare
 */
export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, ...props }, ref) => {
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && onSearch) {
        onSearch(e.currentTarget.value);
      }
    };

    return (
      <Input ref={ref} type="search" placeholder="Caută..." onKeyDown={handleKeyDown} {...props} />
    );
  },
);

SearchInput.displayName = 'SearchInput';

/**
 * Input pentru telefon
 */
export const PhoneInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>((props, ref) => (
  <Input ref={ref} type="tel" placeholder="+40 123 456 789" {...props} />
));

PhoneInput.displayName = 'PhoneInput';

/**
 * Input pentru URL
 */
export const UrlInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>((props, ref) => (
  <Input ref={ref} type="url" placeholder="https://exemplu.com" {...props} />
));

UrlInput.displayName = 'UrlInput';

/**
 * Input pentru dată
 */
export const DateInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>((props, ref) => (
  <Input ref={ref} type="date" {...props} />
));

DateInput.displayName = 'DateInput';

/**
 * Input pentru timp
 */
export const TimeInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>((props, ref) => (
  <Input ref={ref} type="time" {...props} />
));

TimeInput.displayName = 'TimeInput';

/**
 * Input pentru dată și timp
 */
export const DateTimeInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => <Input ref={ref} type="datetime-local" {...props} />,
);

DateTimeInput.displayName = 'DateTimeInput';

export default Input;
