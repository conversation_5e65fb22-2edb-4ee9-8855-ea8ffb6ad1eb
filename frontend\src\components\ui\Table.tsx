import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from '@heroicons/react/24/outline';
import React, { useState, useMemo } from 'react';

import { cn } from '../../utils/helpers';

import Button from './Button';
import LoadingSpinner from './LoadingSpinner';

// Tipuri pentru Table
type TableAlign = 'left' | 'center' | 'right';
type SortDirection = 'asc' | 'desc' | null;

interface TableProps extends React.TableHTMLAttributes<HTMLTableElement> {
  children: React.ReactNode;
  className?: string;
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  compact?: boolean;
}

interface TableHeaderProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
  className?: string;
}

interface TableBodyProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
  className?: string;
  striped?: boolean;
  hover?: boolean;
}

interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode;
  className?: string;
  clickable?: boolean;
  onClick?: (
    e: React.MouseEvent<HTMLTableRowElement> | React.KeyboardEvent<HTMLTableRowElement>,
  ) => void;
  selected?: boolean;
}

interface TableHeaderCellProps extends React.ThHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode;
  className?: string;
  sortable?: boolean;
  sortDirection?: SortDirection;
  onSort?: (direction: 'asc' | 'desc') => void;
  align?: TableAlign;
  width?: string | number | undefined;
}

interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode;
  className?: string;
  align?: TableAlign;
  width?: string | number | undefined;
  truncate?: boolean | undefined;
}

interface TableColumn {
  key: string;
  title: string;
  sortable?: boolean;
  align?: TableAlign;
  width?: string | number;
  truncate?: boolean;
  render?: (value: unknown, row: unknown, index: number) => React.ReactNode;
}

interface DataTableProps extends Omit<TableProps, 'children'> {
  data?: unknown[];
  columns?: TableColumn[];
  loading?: boolean;
  emptyMessage?: string;
  sortable?: boolean;
  paginated?: boolean;
  pageSize?: number;
  currentPage?: number;
  totalItems?: number;
  onPageChange?: (page: number) => void;
  onSort?: (columnKey: string, direction: 'asc' | 'desc') => void;
  onRowClick?: (row: unknown, index: number) => void;
  selectedRows?: unknown[];
  onRowSelect?: (rows: unknown[], action?: string | number) => void;
  selectable?: boolean;
}

interface TablePaginationProps {
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  totalItems?: number;
  pageSize?: number;
  showInfo?: boolean;
  showPageNumbers?: boolean;
  maxPageNumbers?: number;
  className?: string;
}

interface SortConfig {
  key: string | null;
  direction: SortDirection;
}

/**
 * Componenta Table de bază
 */
const Table: React.FC<TableProps> = ({
  children,
  className = '',
  striped = false,
  bordered = false,
  hover: _hover = false,
  compact: _compact = false,
  ...rest
}) => {
  return (
    <div className="overflow-x-auto">
      <table
        className={cn(
          'min-w-full divide-y divide-gray-200',
          striped && 'divide-y-0',
          bordered && 'border border-gray-200',
          className,
        )}
        {...rest}
      >
        {children}
      </table>
    </div>
  );
};

/**
 * Header pentru tabel
 */
export const TableHeader: React.FC<TableHeaderProps> = ({ children, className = '', ...rest }) => {
  return (
    <thead className={cn('bg-gray-50', className)} {...rest}>
      {children}
    </thead>
  );
};

/**
 * Body pentru tabel
 */
export const TableBody: React.FC<TableBodyProps> = ({
  children,
  className = '',
  striped = false,
  hover = false,
  ...rest
}) => {
  return (
    <tbody
      className={cn(
        'bg-white divide-y divide-gray-200',
        striped && '[&>tr:nth-child(even)]:bg-gray-50',
        hover && '[&>tr]:hover:bg-gray-50 [&>tr]:transition-colors',
        className,
      )}
      {...rest}
    >
      {children}
    </tbody>
  );
};

/**
 * Rând pentru tabel
 */
export const TableRow: React.FC<TableRowProps> = ({
  children,
  className = '',
  clickable = false,
  onClick,
  selected = false,
  ...rest
}) => {
  const isClickable = clickable || Boolean(onClick);

  return (
    <tr
      className={cn(
        isClickable && [
          'cursor-pointer',
          'hover:bg-gray-50',
          'focus:bg-gray-50 focus:outline-none',
        ],
        selected && 'bg-primary-50',
        className,
      )}
      onClick={onClick}
      role={isClickable ? 'button' : undefined}
      tabIndex={isClickable ? 0 : undefined}
      onKeyDown={
        isClickable
          ? (e: React.KeyboardEvent<HTMLTableRowElement>) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClick?.(e);
              }
            }
          : undefined
      }
      {...rest}
    >
      {children}
    </tr>
  );
};

/**
 * Celulă header pentru tabel
 */
export const TableHeaderCell: React.FC<TableHeaderCellProps> = ({
  children,
  className = '',
  sortable = false,
  sortDirection = null,
  onSort,
  align = 'left',
  width,
  ...rest
}) => {
  const alignClasses: Record<TableAlign, string> = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  const handleSort = () => {
    if (sortable && onSort) {
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      onSort(newDirection);
    }
  };

  return (
    <th
      className={cn(
        'px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider',
        alignClasses[align],
        sortable && 'cursor-pointer hover:bg-gray-100 select-none',
        className,
      )}
      style={{ width }}
      onClick={handleSort}
      {...rest}
    >
      <div
        className={cn(
          'flex items-center gap-1',
          align === 'center' && 'justify-center',
          align === 'right' && 'justify-end',
        )}
      >
        <span>{children}</span>
        {sortable && (
          <span className="flex flex-col">
            <ChevronUpIcon
              className={cn(
                'w-3 h-3 -mb-1',
                sortDirection === 'asc' ? 'text-gray-900' : 'text-gray-400',
              )}
            />
            <ChevronDownIcon
              className={cn(
                'w-3 h-3',
                sortDirection === 'desc' ? 'text-gray-900' : 'text-gray-400',
              )}
            />
          </span>
        )}
      </div>
    </th>
  );
};

/**
 * Celulă pentru tabel
 */
export const TableCell: React.FC<TableCellProps> = ({
  children,
  className = '',
  align = 'left',
  width,
  truncate = false,
  ...rest
}) => {
  const alignClasses: Record<TableAlign, string> = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <td
      className={cn(
        'px-6 py-4 whitespace-nowrap text-sm text-gray-900',
        alignClasses[align],
        truncate && 'truncate max-w-0',
        className,
      )}
      style={{ width }}
      {...rest}
    >
      {children}
    </td>
  );
};

/**
 * Tabel cu funcționalități avansate
 */
export const DataTable: React.FC<DataTableProps> = ({
  data = [],
  columns = [],
  loading = false,
  emptyMessage = 'Nu există date de afișat',
  sortable = true,
  paginated = true,
  pageSize = 10,
  currentPage = 1,
  totalItems,
  onPageChange,
  onSort,
  onRowClick,
  selectedRows = [],
  onRowSelect,
  selectable = false,
  className = '',
  ...rest
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null });
  const [internalPage, setInternalPage] = useState<number>(currentPage);

  // Sortare locală dacă nu este controlată extern
  const sortedData = useMemo(() => {
    if (!sortable || !sortConfig.key || onSort) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = sortConfig.key ? (a as any)[sortConfig.key] : null;
      const bValue = sortConfig.key ? (b as any)[sortConfig.key] : null;

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'desc' ? -comparison : comparison;
    });
  }, [data, sortConfig, sortable, onSort]);

  // Paginare locală dacă nu este controlată extern
  const paginatedData = useMemo(() => {
    if (!paginated || onPageChange) {
      return sortedData;
    }

    const startIndex = (internalPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, paginated, internalPage, pageSize, onPageChange]);

  const handleSort = (columnKey: string, direction: 'asc' | 'desc') => {
    if (onSort) {
      onSort(columnKey, direction);
    } else {
      setSortConfig({ key: columnKey, direction });
    }
  };

  const handlePageChange = (page: number) => {
    if (onPageChange) {
      onPageChange(page);
    } else {
      setInternalPage(page);
    }
  };

  const handleRowSelect = (rowData: unknown, index: number) => {
    if (onRowSelect) {
      onRowSelect([rowData], index.toString());
    }
  };

  const handleSelectAll = () => {
    if (onRowSelect) {
      const allSelected = selectedRows.length === paginatedData.length;
      if (allSelected) {
        onRowSelect([], 'clear');
      } else {
        onRowSelect(paginatedData, 'all');
      }
    }
  };

  const isRowSelected = (rowData: unknown): boolean => {
    return selectedRows.some(row => JSON.stringify(row) === JSON.stringify(rowData));
  };

  const totalPages = Math.ceil((totalItems || sortedData.length) / pageSize);
  const currentPageNumber = onPageChange ? currentPage : internalPage;

  return (
    <div className={cn('space-y-4', className)}>
      <Table striped hover {...rest}>
        <TableHeader>
          <TableRow>
            {selectable && (
              <TableHeaderCell width="50px">
                <input
                  type="checkbox"
                  checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </TableHeaderCell>
            )}
            {columns.map(column => (
              <TableHeaderCell
                key={column.key}
                sortable={sortable && column.sortable !== false}
                sortDirection={sortConfig.key === column.key ? sortConfig.direction : null}
                onSort={direction => handleSort(column.key, direction)}
                align={column.align || 'left'}
                width={column.width}
              >
                {column.title}
              </TableHeaderCell>
            ))}
          </TableRow>
        </TableHeader>

        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell
                colSpan={columns.length + (selectable ? 1 : 0)}
                className="text-center py-8"
              >
                <LoadingSpinner size="md" className="mx-auto" />
              </TableCell>
            </TableRow>
          ) : paginatedData.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={columns.length + (selectable ? 1 : 0)}
                className="text-center py-8 text-gray-500"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          ) : (
            paginatedData.map((row, index) => (
              <TableRow
                key={index}
                clickable={Boolean(onRowClick)}
                onClick={() => onRowClick?.(row, index)}
                selected={isRowSelected(row)}
              >
                {selectable && (
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={isRowSelected(row)}
                      onChange={() => handleRowSelect(row, index)}
                      onClick={e => e.stopPropagation()}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </TableCell>
                )}
                {columns.map(column => (
                  <TableCell
                    key={column.key}
                    align={column.align || 'left'}
                    width={column.width}
                    truncate={column.truncate || undefined}
                  >
                    {column.render
                      ? column.render((row as any)[column.key], row, index)
                      : (row as any)[column.key]}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {paginated && totalPages > 1 && (
        <TablePagination
          currentPage={currentPageNumber}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          totalItems={totalItems || sortedData.length}
          pageSize={pageSize}
        />
      )}
    </div>
  );
};

/**
 * Componenta pentru paginare
 */
export const TablePagination: React.FC<TablePaginationProps> = ({
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  totalItems = 0,
  pageSize = 10,
  showInfo = true,
  showPageNumbers = true,
  maxPageNumbers = 5,
  className = '',
}) => {
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  const getPageNumbers = (): number[] => {
    const pages: number[] = [];
    const halfRange = Math.floor(maxPageNumbers / 2);

    let startPage = Math.max(1, currentPage - halfRange);
    const endPage = Math.min(totalPages, startPage + maxPageNumbers - 1);

    if (endPage - startPage + 1 < maxPageNumbers) {
      startPage = Math.max(1, endPage - maxPageNumbers + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div
      className={cn(
        'flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200',
        className,
      )}
    >
      {showInfo && (
        <div className="flex-1 flex justify-between sm:hidden">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Următor
          </Button>
        </div>
      )}

      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        {showInfo && (
          <div>
            <p className="text-sm text-gray-700">
              Afișează <span className="font-medium">{startItem}</span> -{' '}
              <span className="font-medium">{endItem}</span> din{' '}
              <span className="font-medium">{totalItems}</span> rezultate
            </p>
          </div>
        )}

        <div className="flex items-center space-x-2">
          {/* Prima pagină */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(1)}
            disabled={currentPage <= 1}
            className="p-2"
          >
            <ChevronDoubleLeftIcon className="w-4 h-4" />
          </Button>

          {/* Pagina anterioară */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage <= 1}
            className="p-2"
          >
            <ChevronLeftIcon className="w-4 h-4" />
          </Button>

          {/* Numerele paginilor */}
          {showPageNumbers &&
            pageNumbers.map(page => (
              <Button
                key={page}
                variant={page === currentPage ? 'primary' : 'outline'}
                size="sm"
                onClick={() => onPageChange?.(page)}
                className="min-w-[2.5rem]"
              >
                {page}
              </Button>
            ))}

          {/* Pagina următoare */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="p-2"
          >
            <ChevronRightIcon className="w-4 h-4" />
          </Button>

          {/* Ultima pagină */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(totalPages)}
            disabled={currentPage >= totalPages}
            className="p-2"
          >
            <ChevronDoubleRightIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Table;
