import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import React, { useState, useEffect, createContext, useContext, type ReactNode } from 'react';
import { createPortal } from 'react-dom';

import { cn } from '../../utils/helpers';

// Tipuri pentru componentele Toast
export type ToastType = 'success' | 'error' | 'warning' | 'info';
export type ToastPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right';

export interface ToastOptions {
  type?: ToastType;
  title?: string;
  message?: string;
  duration?: number;
  dismissible?: boolean;
  action?: ReactNode;
}

export interface Toast extends ToastOptions {
  id: number;
  type: ToastType;
  duration: number;
  dismissible: boolean;
}

export interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: ToastOptions) => number;
  removeToast: (id: number) => void;
  clearToasts: () => void;
  success: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => number;
  error: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => number;
  warning: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => number;
  info: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => number;
}

export interface ToastProviderProps {
  children: ReactNode;
  position?: ToastPosition;
  maxToasts?: number;
}

export interface ToastContainerProps {
  position: ToastPosition;
  toasts: Toast[];
  onRemove: (id: number) => void;
}

export interface ToastProps {
  id?: number;
  type?: ToastType;
  title: string | undefined;
  message: string | undefined;
  duration?: number;
  dismissible?: boolean;
  action?: ReactNode;
  onRemove: (() => void) | undefined;
  className?: string;
}

export interface SimpleToastProps {
  isOpen: boolean;
  onClose?: () => void;
  type?: ToastType;
  title?: string;
  message?: string;
  duration?: number;
  position?: ToastPosition;
  className?: string;
}

export interface SimpleToastState {
  id: number;
  isOpen: boolean;
  type?: ToastType;
  title?: string;
  message?: string;
  duration?: number;
  position?: ToastPosition;
  className?: string;
}

export interface UseSimpleToastReturn {
  showToast: (options: Omit<SimpleToastState, 'id' | 'isOpen'>) => void;
  hideToast: () => void;
  success: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => void;
  error: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => void;
  warning: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => void;
  info: (message: string, options?: Omit<ToastOptions, 'type' | 'message'>) => void;
  ToastComponent: ReactNode;
}

interface TypeConfig {
  icon: React.ComponentType<any>;
  bgColor: string;
  borderColor: string;
  iconColor: string;
  titleColor: string;
  messageColor: string;
}

/**
 * Context pentru gestionarea toast-urilor
 */
const ToastContext = createContext<ToastContextType | undefined>(undefined);

/**
 * Hook pentru utilizarea toast-urilor
 */
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

/**
 * Provider pentru toast-uri
 */
export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  position = 'top-right',
  maxToasts = 5,
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = (toast: ToastOptions): number => {
    const id = Date.now() + Math.random();
    const newToast: Toast = {
      id,
      type: 'info' as ToastType,
      duration: 5000,
      dismissible: true,
      ...toast,
    };

    setToasts((prev: Toast[]) => {
      const updated = [newToast, ...prev];
      return updated.slice(0, maxToasts);
    });

    // Auto-dismiss dacă este specificată durata
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  };

  const removeToast = (id: number): void => {
    setToasts((prev: Toast[]) => prev.filter(toast => toast.id !== id));
  };

  const clearToasts = (): void => {
    setToasts([]);
  };

  // Funcții helper pentru diferite tipuri de toast-uri
  const success = (
    message: string,
    options: Omit<ToastOptions, 'type' | 'message'> = {},
  ): number => {
    return addToast({ ...options, type: 'success', message });
  };

  const error = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): number => {
    return addToast({ ...options, type: 'error', message, duration: 0 });
  };

  const warning = (
    message: string,
    options: Omit<ToastOptions, 'type' | 'message'> = {},
  ): number => {
    return addToast({ ...options, type: 'warning', message });
  };

  const info = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): number => {
    return addToast({ ...options, type: 'info', message });
  };

  const value = {
    toasts,
    addToast,
    removeToast,
    clearToasts,
    success,
    error,
    warning,
    info,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer position={position} toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  );
};

/**
 * Container pentru afișarea toast-urilor
 */
const ToastContainer: React.FC<ToastContainerProps> = ({ position, toasts, onRemove }) => {
  if (toasts.length === 0) return null;

  const positionClasses: Record<ToastPosition, string> = {
    'top-left': 'top-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
    'bottom-right': 'bottom-4 right-4',
  };

  const container = (
    <div
      className={cn(
        'fixed z-50 flex flex-col gap-2 pointer-events-none',
        'max-w-sm w-full',
        positionClasses[position],
      )}
    >
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          {...toast}
          title={toast.title || undefined}
          message={toast.message || undefined}
          onRemove={() => onRemove(toast.id)}
        />
      ))}
    </div>
  );

  return createPortal(container, document.body);
};

/**
 * Componenta Toast individuală
 */
const Toast: React.FC<ToastProps> = ({
  id: _id,
  type = 'info',
  title,
  message,
  duration = 5000,
  dismissible = true,
  action,
  onRemove,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Animație de intrare
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleRemove = (): void => {
    setIsLeaving(true);
    setTimeout(() => {
      onRemove?.();
    }, 300);
  };

  const typeConfig: Record<ToastType, TypeConfig> = {
    success: {
      icon: CheckCircleIcon,
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      iconColor: 'text-green-400',
      titleColor: 'text-green-800',
      messageColor: 'text-green-700',
    },
    error: {
      icon: ExclamationCircleIcon,
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      iconColor: 'text-red-400',
      titleColor: 'text-red-800',
      messageColor: 'text-red-700',
    },
    warning: {
      icon: ExclamationTriangleIcon,
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      iconColor: 'text-yellow-400',
      titleColor: 'text-yellow-800',
      messageColor: 'text-yellow-700',
    },
    info: {
      icon: InformationCircleIcon,
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      iconColor: 'text-blue-400',
      titleColor: 'text-blue-800',
      messageColor: 'text-blue-700',
    },
  };

  const config = typeConfig[type];
  const Icon = config.icon;

  return (
    <div
      className={cn(
        'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg shadow-lg ring-1 ring-black ring-opacity-5',
        'transform transition-all duration-300 ease-out',
        isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',
        config.bgColor,
        config.borderColor,
        'border',
        className,
      )}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={cn('h-6 w-6', config.iconColor)} />
          </div>

          <div className="ml-3 w-0 flex-1">
            {title && <p className={cn('text-sm font-medium', config.titleColor)}>{title}</p>}
            {message && (
              <p className={cn('text-sm', title ? 'mt-1' : '', config.messageColor)}>{message}</p>
            )}

            {action && <div className="mt-3">{action}</div>}
          </div>

          {dismissible && (
            <div className="ml-4 flex-shrink-0 flex">
              <button
                className={cn(
                  'inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
                  config.iconColor,
                  'hover:opacity-75',
                )}
                onClick={handleRemove}
                aria-label="Închide notificarea"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Bara de progres pentru auto-dismiss */}
      {duration > 0 && (
        <div className="h-1 bg-black bg-opacity-10">
          <div
            className={cn(
              'h-full transition-all ease-linear',
              type === 'success' && 'bg-green-500',
              type === 'error' && 'bg-red-500',
              type === 'warning' && 'bg-yellow-500',
              type === 'info' && 'bg-blue-500',
            )}
            style={{
              animation: `toast-progress ${duration}ms linear forwards`,
            }}
          />
        </div>
      )}
    </div>
  );
};

/**
 * Componenta Toast simplă pentru utilizare directă
 */
export const SimpleToast: React.FC<SimpleToastProps> = ({
  isOpen,
  onClose,
  type = 'info',
  title,
  message,
  duration = 5000,
  position = 'top-right',
  className = '',
}) => {
  useEffect(() => {
    if (isOpen && duration > 0) {
      const timer = setTimeout(() => {
        onClose?.();
      }, duration);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [isOpen, duration, onClose]);

  if (!isOpen) return null;

  const positionClasses: Record<ToastPosition, string> = {
    'top-left': 'top-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
    'bottom-right': 'bottom-4 right-4',
  };

  const toast = (
    <div className={cn('fixed z-50 max-w-sm w-full', positionClasses[position])}>
      <Toast
        type={type}
        title={title}
        message={message}
        duration={0} // Controlat extern
        onRemove={onClose}
        className={className}
      />
    </div>
  );

  return createPortal(toast, document.body);
};

/**
 * Hook pentru toast-uri simple fără provider
 */
export const useSimpleToast = (): UseSimpleToastReturn => {
  const [toast, setToast] = useState<SimpleToastState | null>(null);

  const showToast = (options: Omit<SimpleToastState, 'id' | 'isOpen'>): void => {
    setToast({
      id: Date.now(),
      isOpen: true,
      ...options,
    });
  };

  const hideToast = (): void => {
    setToast((prev: SimpleToastState | null) => (prev ? { ...prev, isOpen: false } : null));
  };

  const success = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): void => {
    showToast({ ...options, type: 'success', message });
  };

  const error = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): void => {
    showToast({ ...options, type: 'error', message, duration: 0 });
  };

  const warning = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): void => {
    showToast({ ...options, type: 'warning', message });
  };

  const info = (message: string, options: Omit<ToastOptions, 'type' | 'message'> = {}): void => {
    showToast({ ...options, type: 'info', message });
  };

  const ToastComponent: ReactNode = toast ? <SimpleToast {...toast} onClose={hideToast} /> : null;

  return {
    showToast,
    hideToast,
    success,
    error,
    warning,
    info,
    ToastComponent,
  };
};

// Stiluri CSS pentru animația barei de progres
const toastStyles = `
@keyframes toast-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
`;

// Injectează stilurile în document
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = toastStyles;
  document.head.appendChild(styleElement);
}

export default Toast;
