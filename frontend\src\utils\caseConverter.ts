/**
 * Utilitare pentru conversie între snake_case și camelCase în frontend
 * Sincronizat cu backend-ul pentru consistență
 */

// Tipuri pentru conversie
export type CaseConversionType = 'snakeToCamel' | 'camelToSnake';

/**
 * Convertește un string de la snake_case la camelCase
 */
export function snakeToCamel(str: string): string {
  if (!str || typeof str !== 'string') return str;

  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Convertește un string de la camelCase la snake_case
 */
export function camelToSnake(str: string): string {
  if (!str || typeof str !== 'string') return str;

  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * Mapping specific pentru câmpurile aplicației
 * Sincronizat cu backend-ul
 */
export const FIELD_MAPPING = {
  // Conversii speciale snake_case -> camelCase
  snakeToCamel: {
    created_at: 'createdAt',
    updated_at: 'updatedAt',
    email_verified: 'emailVerified',
    is_active: 'isActive',
    is_default: 'isDefault',
    is_recurring: 'isRecurring',
    category_id: 'categoryId',
    user_id: 'userId',
    expense_id: 'expenseId',
    plan_id: 'planId',
    subscription_id: 'subscriptionId',
    expense_date: 'date',
    payment_method: 'paymentMethod',
    receipt_url: 'receiptUrl',
    last_login: 'lastLogin',
    login_count: 'loginCount',
    password_reset_token: 'passwordResetToken',
    password_reset_expires: 'passwordResetExpires',
    email_verification_token: 'emailVerificationToken',
    subscription_status: 'subscriptionStatus',
    subscription_current_period_start: 'subscriptionCurrentPeriodStart',
    subscription_current_period_end: 'subscriptionCurrentPeriodEnd',
    stripe_customer_id: 'stripeCustomerId',
    plan_type: 'planType',
    monthly_expense_count: 'monthlyExpenseCount',
    monthly_expense_limit: 'monthlyExpenseLimit',
    last_usage_reset: 'lastUsageReset',
    trial_ends_at: 'trialEndsAt',
    refresh_token: 'refreshToken',
    first_name: 'firstName',
    last_name: 'lastName',
    budget_limit: 'budgetLimit',
    budget_period: 'budgetPeriod',
    sort_order: 'sortOrder',
    recurring_frequency: 'recurringFrequency',
    recurring_end_date: 'recurringEndDate',
    original_expense_id: 'originalExpenseId',
    stripe_price_id: 'stripePriceId',
    current_period_start: 'currentPeriodStart',
    current_period_end: 'currentPeriodEnd',
    trial_start: 'trialStart',
    trial_end: 'trialEnd',
    canceled_at: 'canceledAt',
    ended_at: 'endedAt',
    processed_at: 'processedAt',
    retry_count: 'retryCount',
    stripe_id: 'stripeId',
  },

  // Conversii inverse camelCase -> snake_case
  camelToSnake: {} as Record<string, string>,
};

// Generează mapping-ul invers automat
for (const [snake, camel] of Object.entries(FIELD_MAPPING.snakeToCamel)) {
  FIELD_MAPPING.camelToSnake[camel] = snake;
}

/**
 * Convertește un obiect folosind mapping-ul specificat
 */
function convertWithMapping<T = any>(obj: unknown, type: CaseConversionType): T {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertWithMapping(item, type)) as T;
  }

  if (typeof obj === 'object' && obj.constructor === Object) {
    const result: unknown = {};
    const mapping = FIELD_MAPPING[type] as Record<string, string>;

    for (const [key, value] of Object.entries(obj)) {
      // Folosește mapping-ul specific dacă există
      const newKey =
        mapping?.[key] || (type === 'snakeToCamel' ? snakeToCamel(key) : camelToSnake(key));

      result[newKey] = convertWithMapping(value, type);
    }

    return result;
  }

  return obj;
}

/**
 * Funcții de conveniență pentru conversii rapide
 */
export const CaseConverter = {
  /**
   * Convertește de la snake_case la camelCase
   */
  toCamel: <T = any>(obj: unknown): T => convertWithMapping<T>(obj, 'snakeToCamel'),

  /**
   * Convertește de la camelCase la snake_case
   */
  toSnake: <T = any>(obj: unknown): T => convertWithMapping<T>(obj, 'camelToSnake'),

  /**
   * Convertește un string individual
   */
  stringToCamel: snakeToCamel,
  stringToSnake: camelToSnake,

  /**
   * Verifică dacă un string este în snake_case
   */
  isSnakeCase: (str: string): boolean => {
    return /^[a-z]+(_[a-z]+)*$/.test(str);
  },

  /**
   * Verifică dacă un string este în camelCase
   */
  isCamelCase: (str: string): boolean => {
    return /^[a-z]+([A-Z][a-z]*)*$/.test(str);
  },

  /**
   * Convertește parametrii de query pentru API
   */
  convertQueryParams: (params: Record<string, any>): Record<string, any> => {
    return CaseConverter.toSnake(params);
  },

  /**
   * Convertește response-ul de la API
   */
  convertApiResponse: <T = any>(response: unknown): T => {
    return CaseConverter.toCamel<T>(response);
  },
};

/**
 * Interceptor pentru axios pentru transformarea automată
 */
export const createCaseInterceptors = () => {
  return {
    // Request interceptor - convertește la snake_case
    request: (config: unknown) => {
      if (config.data && typeof config.data === 'object') {
        config.data = CaseConverter.toSnake(config.data);
      }

      if (config.params && typeof config.params === 'object') {
        config.params = CaseConverter.toSnake(config.params);
      }

      return config;
    },

    // Response interceptor - convertește la camelCase
    response: (response: unknown) => {
      if (response.data && typeof response.data === 'object') {
        response.data = CaseConverter.toCamel(response.data);
      }

      return response;
    },

    // Error interceptor - convertește erorile la camelCase
    error: (error: unknown) => {
      if (error.response?.data && typeof error.response.data === 'object') {
        error.response.data = CaseConverter.toCamel(error.response.data);
      }

      return Promise.reject(error);
    },
  };
};

/**
 * Hook pentru transformarea automată în React Query
 */
export const useApiTransform = () => {
  return {
    // Transformă datele înainte de trimitere
    transformRequest: <T>(data: T): unknown => {
      return CaseConverter.toSnake(data);
    },

    // Transformă response-ul după primire
    transformResponse: <T>(data: unknown): T => {
      return CaseConverter.toCamel<T>(data);
    },
  };
};

export default CaseConverter;
