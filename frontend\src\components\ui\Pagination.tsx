import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  EllipsisHorizontalIcon,
} from '@heroicons/react/24/outline';
import React from 'react';

import { cn } from '../../utils/helpers';

import Button from './Button';

// Types
type PaginationSize = 'sm' | 'md' | 'lg';
type PaginationVariant = 'default' | 'outline' | 'ghost';

interface PaginationProps {
  currentPage?: number;
  totalPages?: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  showPageNumbers?: boolean;
  maxVisiblePages?: number;
  size?: PaginationSize;
  variant?: PaginationVariant;
  disabled?: boolean;
  className?: string;
  [key: string]: unknown;
}

interface SimplePaginationProps {
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  size?: PaginationSize;
  disabled?: boolean;
  className?: string;
  showPageInfo?: boolean;
  [key: string]: unknown;
}

interface DetailedPaginationProps {
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  itemsPerPageOptions?: number[];
  showItemsPerPage?: boolean;
  showItemsInfo?: boolean;
  className?: string;
  [key: string]: unknown;
}

interface CompactPaginationProps {
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  disabled?: boolean;
  className?: string;
  [key: string]: unknown;
}

interface PaginationButtonProps {
  children: React.ReactNode;
  page: number;
  isActive?: boolean;
  isDisabled?: boolean;
  'aria-label'?: string;
  [key: string]: unknown;
}

interface UsePaginationOptions {
  totalItems?: number;
  itemsPerPage?: number;
  initialPage?: number;
}

interface PageInfo {
  startItem: number;
  endItem: number;
  totalItems: number;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface UsePaginationReturn {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  getPageItems: <T>(items: T[]) => T[];
  getPageInfo: () => PageInfo;
}

interface VariantConfig {
  button: string;
  active: string;
  disabled: string;
}

// Componente externe pentru a evita nested components
const PaginationButton: React.FC<
  PaginationButtonProps & {
    size: PaginationSize;
    variant: PaginationVariant;
    onPageChange: (page: number) => void;
  }
> = ({
  children,
  page,
  isActive = false,
  isDisabled = false,
  size,
  variant,
  onPageChange,
  'aria-label': ariaLabel,
  ...buttonProps
}) => {
  const sizeClasses: Record<PaginationSize, string> = {
    sm: 'px-2 py-1 text-xs min-w-[28px] h-7',
    md: 'px-3 py-2 text-sm min-w-[32px] h-8',
    lg: 'px-4 py-2 text-base min-w-[36px] h-10',
  };

  const variantConfigs: Record<PaginationVariant, VariantConfig> = {
    default: {
      button: 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50',
      active: 'bg-primary-600 border-primary-600 text-white hover:bg-primary-700',
      disabled: 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed',
    },
    outline: {
      button: 'bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50',
      active: 'bg-primary-50 border-primary-600 text-primary-600 hover:bg-primary-100',
      disabled: 'bg-transparent border-gray-200 text-gray-400 cursor-not-allowed',
    },
    ghost: {
      button: 'bg-transparent border-transparent text-gray-700 hover:bg-gray-100',
      active: 'bg-primary-100 border-transparent text-primary-600 hover:bg-primary-200',
      disabled: 'bg-transparent border-transparent text-gray-400 cursor-not-allowed',
    },
  };

  const config = variantConfigs[variant];

  return (
    <button
      type="button"
      onClick={() => !isDisabled && onPageChange(page)}
      disabled={isDisabled}
      aria-label={ariaLabel}
      className={cn(
        'inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1',
        sizeClasses[size],
        isActive ? config.active : isDisabled ? config.disabled : config.button,
      )}
      {...buttonProps}
    >
      {children}
    </button>
  );
};

const PaginationEllipsis: React.FC<{ size: PaginationSize }> = ({ size }) => {
  const sizeClasses: Record<PaginationSize, string> = {
    sm: 'px-2 py-1 text-xs min-w-[28px] h-7',
    md: 'px-3 py-2 text-sm min-w-[32px] h-8',
    lg: 'px-4 py-2 text-base min-w-[36px] h-10',
  };

  return (
    <span
      className={cn('inline-flex items-center justify-center text-gray-500', sizeClasses[size])}
    >
      <EllipsisHorizontalIcon className="w-5 h-5" />
    </span>
  );
};

/**
 * Componenta Pagination principală
 */
const Pagination: React.FC<PaginationProps> = ({
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  showPageNumbers = true,
  maxVisiblePages = 5,
  size = 'md',
  variant = 'default',
  disabled = false,
  className = '',
  ...rest
}) => {
  // Validare props
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
  const validTotalPages = Math.max(1, totalPages);

  const handlePageChange = (page: number) => {
    if (disabled || page === validCurrentPage || page < 1 || page > validTotalPages) {
      return;
    }
    onPageChange?.(page);
  };

  // Calculează paginile vizibile
  const getVisiblePages = () => {
    if (validTotalPages <= maxVisiblePages) {
      return Array.from({ length: validTotalPages }, (_, i) => i + 1);
    }

    const half = Math.floor(maxVisiblePages / 2);
    let start = validCurrentPage - half;
    let end = validCurrentPage + half;

    if (start < 1) {
      start = 1;
      end = maxVisiblePages;
    }

    if (end > validTotalPages) {
      end = validTotalPages;
      start = validTotalPages - maxVisiblePages + 1;
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const visiblePages = getVisiblePages();
  const showStartEllipsis = visiblePages.length > 0 && visiblePages[0]! > 2;
  const showEndEllipsis =
    visiblePages.length > 0 && visiblePages[visiblePages.length - 1]! < validTotalPages - 1;

  const sizeClasses: Record<PaginationSize, string> = {
    sm: 'h-8 min-w-[2rem] text-sm',
    md: 'h-10 min-w-[2.5rem] text-sm',
    lg: 'h-12 min-w-[3rem] text-base',
  };

  const variantClasses: Record<PaginationVariant, VariantConfig> = {
    default: {
      button: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
      active: 'border-primary-500 bg-primary-500 text-white hover:bg-primary-600',
      disabled: 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed',
    },
    outline: {
      button: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50',
      active: 'border-primary-500 bg-primary-50 text-primary-600 hover:bg-primary-100',
      disabled: 'border-gray-200 bg-transparent text-gray-400 cursor-not-allowed',
    },
    ghost: {
      button: 'border-0 bg-transparent text-gray-700 hover:bg-gray-100',
      active: 'border-0 bg-primary-100 text-primary-600 hover:bg-primary-200',
      disabled: 'border-0 bg-transparent text-gray-400 cursor-not-allowed',
    },
  };

  return (
    <nav
      className={cn('flex items-center gap-1', className)}
      role="navigation"
      aria-label="Navigare pagini"
      {...rest}
    >
      {/* First page */}
      {showFirstLast && validTotalPages > 1 && (
        <PaginationButton
          page={1}
          isDisabled={validCurrentPage === 1}
          size={size}
          variant={variant}
          onPageChange={handlePageChange}
          aria-label="Prima pagină"
        >
          <ChevronDoubleLeftIcon className="w-4 h-4" />
        </PaginationButton>
      )}

      {/* Previous page */}
      {showPrevNext && (
        <PaginationButton
          page={validCurrentPage - 1}
          isDisabled={validCurrentPage === 1}
          size={size}
          variant={variant}
          onPageChange={handlePageChange}
          aria-label="Pagina anterioară"
        >
          <ChevronLeftIcon className="w-4 h-4" />
        </PaginationButton>
      )}

      {showPageNumbers && (
        <>
          {/* First page number */}
          {showStartEllipsis && (
            <>
              <PaginationButton
                page={1}
                size={size}
                variant={variant}
                onPageChange={handlePageChange}
                aria-label="Pagina 1"
              >
                1
              </PaginationButton>
              <PaginationEllipsis size={size} />
            </>
          )}

          {/* Visible page numbers */}
          {visiblePages.map(page => (
            <PaginationButton
              key={page}
              page={page}
              isActive={page === validCurrentPage}
              size={size}
              variant={variant}
              onPageChange={handlePageChange}
              aria-label={`Pagina ${page}`}
            >
              {page}
            </PaginationButton>
          ))}

          {/* Last page number */}
          {showEndEllipsis && (
            <>
              <PaginationEllipsis size={size} />
              <PaginationButton
                page={validTotalPages}
                size={size}
                variant={variant}
                onPageChange={handlePageChange}
                aria-label={`Pagina ${validTotalPages}`}
              >
                {validTotalPages}
              </PaginationButton>
            </>
          )}
        </>
      )}

      {/* Next page */}
      {showPrevNext && (
        <PaginationButton
          page={validCurrentPage + 1}
          isDisabled={validCurrentPage === validTotalPages}
          size={size}
          variant={variant}
          onPageChange={handlePageChange}
          aria-label="Pagina următoare"
        >
          <ChevronRightIcon className="w-4 h-4" />
        </PaginationButton>
      )}

      {/* Last page */}
      {showFirstLast && validTotalPages > 1 && (
        <PaginationButton
          page={validTotalPages}
          isDisabled={validCurrentPage === validTotalPages}
          size={size}
          variant={variant}
          onPageChange={handlePageChange}
          aria-label="Ultima pagină"
        >
          <ChevronDoubleRightIcon className="w-4 h-4" />
        </PaginationButton>
      )}
    </nav>
  );
};

/**
 * Pagination simplu cu doar prev/next
 */
export const SimplePagination: React.FC<SimplePaginationProps> = ({
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  size = 'md',
  disabled = false,
  className = '',
  showPageInfo = true,
  ...rest
}) => {
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
  const validTotalPages = Math.max(1, totalPages);

  const handlePrevious = () => {
    if (validCurrentPage > 1) {
      onPageChange?.(validCurrentPage - 1);
    }
  };

  const handleNext = () => {
    if (validCurrentPage < validTotalPages) {
      onPageChange?.(validCurrentPage + 1);
    }
  };

  return (
    <div className={cn('flex items-center justify-between', className)} {...rest}>
      <Button
        variant="outline"
        size={size}
        onClick={handlePrevious}
        disabled={disabled || validCurrentPage === 1}
        leftIcon={<ChevronLeftIcon className="w-4 h-4" />}
      >
        Anterior
      </Button>

      {showPageInfo && (
        <span className="text-sm text-gray-700">
          Pagina {validCurrentPage} din {validTotalPages}
        </span>
      )}

      <Button
        variant="outline"
        size={size}
        onClick={handleNext}
        disabled={disabled || validCurrentPage === validTotalPages}
        rightIcon={<ChevronRightIcon className="w-4 h-4" />}
      >
        Următorul
      </Button>
    </div>
  );
};

/**
 * Pagination cu informații detaliate
 */
export const DetailedPagination: React.FC<DetailedPaginationProps> = ({
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 25, 50, 100],
  showItemsPerPage = true,
  showItemsInfo = true,
  className = '',
  ...rest
}) => {
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
  const validTotalPages = Math.max(1, totalPages);

  const startItem = (validCurrentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(validCurrentPage * itemsPerPage, totalItems);

  return (
    <div className={cn('flex flex-col sm:flex-row items-center justify-between gap-4', className)}>
      <div className="flex items-center gap-4">
        {showItemsInfo && totalItems > 0 && (
          <span className="text-sm text-gray-700">
            Afișează {startItem}-{endItem} din {totalItems} rezultate
          </span>
        )}

        {showItemsPerPage && onItemsPerPageChange && (
          <div className="flex items-center gap-2">
            <label htmlFor="items-per-page" className="text-sm text-gray-700">
              Elemente per pagină:
            </label>
            <select
              id="items-per-page"
              value={itemsPerPage}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                onItemsPerPageChange(Number(e.target.value))
              }
              className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {itemsPerPageOptions.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      <Pagination
        currentPage={validCurrentPage}
        totalPages={validTotalPages}
        onPageChange={onPageChange}
        {...rest}
      />
    </div>
  );
};

/**
 * Pagination compact pentru spații mici
 */
export const CompactPagination: React.FC<CompactPaginationProps> = ({
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  disabled = false,
  className = '',
  ...rest
}) => {
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));
  const validTotalPages = Math.max(1, totalPages);

  const handlePageChange = (page: number) => {
    if (disabled || page === validCurrentPage || page < 1 || page > validTotalPages) {
      return;
    }
    onPageChange?.(page);
  };

  return (
    <div className={cn('flex items-center gap-2', className)} {...rest}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => handlePageChange(validCurrentPage - 1)}
        disabled={disabled || validCurrentPage === 1}
        aria-label="Pagina anterioară"
      >
        <ChevronLeftIcon className="w-4 h-4" />
      </Button>

      <div className="flex items-center gap-1">
        <input
          type="number"
          min={1}
          max={validTotalPages}
          value={validCurrentPage}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const page = parseInt(e.target.value, 10);
            if (!isNaN(page)) {
              handlePageChange(page);
            }
          }}
          className="w-16 px-2 py-1 text-center border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          disabled={disabled}
        />
        <span className="text-sm text-gray-500">din {validTotalPages}</span>
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={() => handlePageChange(validCurrentPage + 1)}
        disabled={disabled || validCurrentPage === validTotalPages}
        aria-label="Pagina următoare"
      >
        <ChevronRightIcon className="w-4 h-4" />
      </Button>
    </div>
  );
};

/**
 * Hook pentru gestionarea paginării
 */
export const usePagination = ({
  totalItems = 0,
  itemsPerPage = 10,
  initialPage = 1,
}: UsePaginationOptions = {}): UsePaginationReturn => {
  const [currentPage, setCurrentPage] = React.useState(initialPage);
  const [itemsPerPageState, setItemsPerPageState] = React.useState(itemsPerPage);

  const totalPages = Math.ceil(totalItems / itemsPerPageState);
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));

  // Ajustează pagina curentă dacă totalPages se schimbă
  React.useEffect(() => {
    if (validCurrentPage !== currentPage) {
      setCurrentPage(validCurrentPage);
    }
  }, [totalPages, validCurrentPage, currentPage]);

  const goToPage = (page: number) => {
    const newPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(newPage);
  };

  const goToNextPage = () => {
    goToPage(currentPage + 1);
  };

  const goToPreviousPage = () => {
    goToPage(currentPage - 1);
  };

  const goToFirstPage = () => {
    goToPage(1);
  };

  const goToLastPage = () => {
    goToPage(totalPages);
  };

  const setItemsPerPage = (newItemsPerPage: number) => {
    setItemsPerPageState(newItemsPerPage);
    // Recalculează pagina curentă pentru a menține aproximativ aceleași elemente vizibile
    const currentFirstItem = (currentPage - 1) * itemsPerPageState + 1;
    const newPage = Math.ceil(currentFirstItem / newItemsPerPage);
    setCurrentPage(Math.max(1, newPage));
  };

  const getPageItems = <T,>(items: T[] = []): T[] => {
    const startIndex = (validCurrentPage - 1) * itemsPerPageState;
    const endIndex = startIndex + itemsPerPageState;
    return items.slice(startIndex, endIndex);
  };

  const getPageInfo = (): PageInfo => {
    const startItem = (validCurrentPage - 1) * itemsPerPageState + 1;
    const endItem = Math.min(validCurrentPage * itemsPerPageState, totalItems);

    return {
      startItem,
      endItem,
      totalItems,
      currentPage: validCurrentPage,
      totalPages,
      itemsPerPage: itemsPerPageState,
      hasNextPage: validCurrentPage < totalPages,
      hasPreviousPage: validCurrentPage > 1,
    };
  };

  return {
    currentPage: validCurrentPage,
    totalPages,
    itemsPerPage: itemsPerPageState,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
    setItemsPerPage,
    getPageItems,
    getPageInfo,
  };
};

export default Pagination;
