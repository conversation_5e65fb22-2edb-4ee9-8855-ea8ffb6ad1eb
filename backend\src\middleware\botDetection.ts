import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
}

/**
 * Middleware pentru detectarea bot-urilor
 */
export const botDetection = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  const userAgent = req.get('User-Agent') || '';
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python-requests/i,
    /postman/i
  ];

  const isBot = botPatterns.some(pattern => pattern.test(userAgent));
  
  if (isBot) {
    logger.info('Bot detected', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      userAgent,
      userId: req.user?.id
    });
    
    // Permite doar anumite rute pentru bot-uri
    const allowedBotRoutes = ['/health', '/api/docs', '/robots.txt'];
    if (!allowedBotRoutes.some(route => req.path.startsWith(route))) {
      res.status(403).json({
        success: false,
        error: {
          message: 'Accesul pentru bot-uri nu este permis pe această rută'
        }
      });
      return;
    }
  }

  next();
};

export default botDetection;