import {
  UsersIcon,
  CreditCardIcon,
  ChartBarIcon,
  BanknotesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import React from 'react';

import Badge from '../../components/ui/Badge';
import Card from '../../components/ui/Card';
import { useSubscriptionStats, usePlanStats, useUsageStats } from '../../hooks/useAdminData';
import { formatCurrency } from '../../utils/helpers';
import type { DashboardStats } from '../../types';

// Tipuri locale pentru statistici
interface SubscriptionStats {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
  monthlyRevenue: number;
  yearlyRevenue: number;
  churnRate: number;
  userGrowth: number;
  revenueGrowth: number;
}

interface Plan {
  id: string;
  name: string;
  userCount?: number;
}

interface PlanStats {
  plans?: Plan[];
}

interface UsageStats {
  totalActions: number;
  dailyActiveUsers: number;
  usageGrowth: number;
}

interface AdminStatsProps {
  dashboardStats?: DashboardStats | undefined;
}

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ComponentType<any>;
  trend?: 'up' | 'down';
  trendValue?: number;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'indigo';
  subtitle?: string;
  type?: 'number' | 'currency';
  trendLabel?: string;
  iconColor?: string;
}

// Componentă externă pentru a evita nested components
const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  trendValue,
  color = 'blue',
  subtitle,
  type = 'number',
  trendLabel,
  iconColor,
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600',
    orange: 'bg-orange-50 text-orange-600',
    red: 'bg-red-50 text-red-600',
    indigo: 'bg-indigo-50 text-indigo-600',
  };

  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    if (type === 'currency') return formatCurrency(val);
    return val.toLocaleString();
  };

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-lg ${iconColor || colorClasses[color]}`}>
              <Icon className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{formatValue(value)}</p>
              {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
            </div>
          </div>
        </div>

        {trend && trendValue !== undefined && (
          <div className="flex items-center space-x-1">
            {trend === 'up' ? (
              <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />
            ) : (
              <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />
            )}
            <Badge variant={trend === 'up' ? 'success' : 'error'} size="sm" className="text-xs">
              {trendValue > 0 ? '+' : ''}
              {trendValue}%
            </Badge>
            {trendLabel && <span className="text-xs text-gray-500 ml-1">{trendLabel}</span>}
          </div>
        )}
      </div>
    </Card>
  );
};

const AdminStats: React.FC<AdminStatsProps> = ({ dashboardStats: _dashboardStats }) => {
  const { data: subscriptionStats } = useSubscriptionStats() as {
    data: SubscriptionStats | undefined;
  };
  const { data: planStats } = usePlanStats() as { data: PlanStats | undefined };
  const { data: usageStats } = useUsageStats() as { data: UsageStats | undefined };
  // Calculează metrici derivate
  const totalUsers = subscriptionStats?.totalUsers || 0;
  const activeUsers = subscriptionStats?.activeUsers || 0;
  const premiumUsers = subscriptionStats?.premiumUsers || 0;
  const freeUsers = totalUsers - premiumUsers;

  const monthlyRevenue = subscriptionStats?.monthlyRevenue || 0;
  const yearlyRevenue = subscriptionStats?.yearlyRevenue || 0;
  const averageRevenuePerUser = premiumUsers > 0 ? monthlyRevenue / premiumUsers : 0;

  const conversionRate = totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0;
  const churnRate = subscriptionStats?.churnRate || 0;

  const totalActions = usageStats?.totalActions || 0;
  const dailyActiveUsers = usageStats?.dailyActiveUsers || 0;

  // Calculează tendințele (mock data pentru demonstrație)
  const userGrowth = subscriptionStats?.userGrowth || 5.2;
  const revenueGrowth = subscriptionStats?.revenueGrowth || 12.8;
  const usageGrowth = usageStats?.usageGrowth || 8.4;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total utilizatori */}
      <StatCard
        title="Total utilizatori"
        value={totalUsers}
        subtitle={`${activeUsers} activi astăzi`}
        icon={UsersIcon}
        trend={userGrowth > 0 ? 'up' : 'down'}
        trendValue={userGrowth}
        color="blue"
      />

      {/* Venituri lunare */}
      <StatCard
        title="Venituri lunare"
        value={formatCurrency(monthlyRevenue)}
        subtitle={`ARPU: ${formatCurrency(averageRevenuePerUser)}`}
        icon={BanknotesIcon}
        trend={revenueGrowth > 0 ? 'up' : 'down'}
        trendValue={revenueGrowth}
        color="green"
      />

      {/* Utilizatori Premium */}
      <StatCard
        title="Utilizatori Premium"
        value={premiumUsers}
        subtitle={`${conversionRate.toFixed(1)}% conversion rate`}
        icon={CreditCardIcon}
        trend={conversionRate > 0 ? 'up' : 'down'}
        trendValue={conversionRate}
        color="purple"
      />

      {/* Activitate zilnică */}
      <StatCard
        title="Acțiuni zilnice"
        value={totalActions}
        subtitle={`${dailyActiveUsers} utilizatori activi`}
        icon={ChartBarIcon}
        trend={usageGrowth > 0 ? 'up' : 'down'}
        trendValue={usageGrowth}
        color="indigo"
      />

      {/* Distribuția planurilor */}
      <Card className="p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuția planurilor</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-gray-400 rounded-full mr-3" />
              <span className="text-sm font-medium text-gray-700">Gratuit</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">{freeUsers}</span>
              <Badge variant="secondary">
                {totalUsers > 0 ? ((freeUsers / totalUsers) * 100).toFixed(1) : 0}%
              </Badge>
            </div>
          </div>

          {planStats?.plans?.map((plan, index) => {
            const planUsers = plan.userCount || 0;
            const planPercentage = totalUsers > 0 ? (planUsers / totalUsers) * 100 : 0;
            const colors = ['bg-blue-500', 'bg-purple-500', 'bg-green-500'];

            return (
              <div key={plan.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 ${colors[index % colors.length]} rounded-full mr-3`} />
                  <span className="text-sm font-medium text-gray-700">{plan.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{planUsers}</span>
                  <Badge variant="primary">{planPercentage.toFixed(1)}%</Badge>
                </div>
              </div>
            );
          }) || (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded-full mr-3" />
                <span className="text-sm font-medium text-gray-700">Premium</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{premiumUsers}</span>
                <Badge variant="primary">{conversionRate.toFixed(1)}%</Badge>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Metrici cheie */}
      <Card className="p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Metrici cheie</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(yearlyRevenue)}</p>
            <p className="text-sm text-gray-600">ARR (Annual Recurring Revenue)</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{churnRate.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">Churn Rate</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(averageRevenuePerUser * 12)}
            </p>
            <p className="text-sm text-gray-600">LTV (Customer Lifetime Value)</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {dailyActiveUsers > 0 ? ((dailyActiveUsers / totalUsers) * 100).toFixed(1) : 0}%
            </p>
            <p className="text-sm text-gray-600">Daily Active Users</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminStats;
