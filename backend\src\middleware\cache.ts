import redis, { RedisClientType } from 'redis';
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import crypto from 'crypto';

// Interfaces
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
}

interface CacheOptions {
  ttl?: number;
  prefix?: string;
  skipCache?: boolean;
  varyBy?: string[];
  condition?: (req: AuthenticatedRequest) => boolean;
}

interface CacheData {
  data: unknown;
  statusCode: number;
  timestamp: string;
}

type PatternFunction = (req: AuthenticatedRequest, res?: Response) => string;
type Pattern = string | PatternFunction;

type WarmupFunction = (req: AuthenticatedRequest) => Promise<void>;

// Configurare client Redis
let redisClient: RedisClientType | null = null;
let isRedisConnected: boolean = false;

/**
 * Inițializează conexiunea Redis
 */
const initializeRedis = async (): Promise<void> => {
  try {
    if (process.env.REDIS_URL) {
      redisClient = redis.createClient({
        url: process.env.REDIS_URL,
        socket: {
          reconnectStrategy: (retries: number) => {
            if (retries > 10) {
              logger.error('Redis max retry attempts reached');
              return new Error('Max retry attempts reached');
            }
            return Math.min(retries * 100, 3000);
          }
        }
      });

      redisClient.on('error', (err: Error) => {
        logger.error('Redis Client Error:', err);
        isRedisConnected = false;
      });

      redisClient.on('connect', () => {
        logger.info('Redis Client Connected');
        isRedisConnected = true;
      });

      redisClient.on('disconnect', () => {
        logger.warn('Redis Client Disconnected');
        isRedisConnected = false;
      });

      await redisClient.connect();
    } else {
      logger.warn('REDIS_URL not configured, caching will be disabled');
    }
  } catch (error) {
    logger.error('Failed to initialize Redis:', error);
    isRedisConnected = false;
  }
};

/**
 * Generează o cheie de cache unică
 */
const generateCacheKey = (
  prefix: string,
  req: AuthenticatedRequest,
  additionalParams: Record<string, any> = {}
): string => {
  const baseKey = {
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id || 'anonymous',
    query: req.query,
    params: req.params,
    ...additionalParams
  };
  
  const keyString = JSON.stringify(baseKey);
  const hash = crypto.createHash('md5').update(keyString).digest('hex');
  
  return `${prefix}:${hash}`;
};

/**
 * Middleware de cache generic
 */
const cache = (options: CacheOptions = {}) => {
  const {
    ttl = 300, // 5 minute default
    prefix = 'cache',
    skipCache = false,
    varyBy = [],
    condition = () => true
  } = options;

  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    // Skip cache în mediul de test sau dacă Redis nu este conectat
    if (process.env['NODE_ENV'] === 'test' || !isRedisConnected || skipCache || !condition(req)) {
      return next();
    }

    try {
      // Generează cheia de cache
      const additionalParams: Record<string, any> = {};
      varyBy.forEach(param => {
        if ((req as any)[param]) {
          additionalParams[param] = (req as any)[param];
        }
      });
      
      const cacheKey = generateCacheKey(prefix, req, additionalParams);
      
      // Încearcă să obțină din cache
      const cachedData = await redisClient!.get(cacheKey);
      
      if (cachedData) {
        const parsed: CacheData = JSON.parse(cachedData);
        
        // Adaugă header-e de cache
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey,
          'X-Cache-TTL': ttl.toString()
        });
        
        logger.logPerformance('cacheHit', 0, {
          cacheKey,
          url: req.originalUrl,
          userId: req.user?.id
        });
        
        res.status(parsed.statusCode || 200).json(parsed.data);
        return;
      }
      
      // Cache miss - interceptează răspunsul
      const originalJson = res.json;
      const originalSend = res.send;
      
      res.json = function(data: unknown) {
        // Salvează în cache doar răspunsurile de succes
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const cacheData: CacheData = {
            data,
            statusCode: res.statusCode,
            timestamp: new Date().toISOString()
          };
          
          redisClient!.setEx(cacheKey, ttl, JSON.stringify(cacheData))
            .catch(err => logger.error('Cache save error:', err));
          
          logger.logPerformance('cacheMiss', 0, {
            cacheKey,
            url: req.originalUrl,
            userId: req.user?.id,
            ttl
          });
        }
        
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey,
          'X-Cache-TTL': ttl.toString()
        });
        
        return originalJson.call(this, data);
      };
      
      res.send = function(data: unknown) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const cacheData: CacheData = {
            data,
            statusCode: res.statusCode,
            timestamp: new Date().toISOString()
          };
          
          redisClient!.setEx(cacheKey, ttl, JSON.stringify(cacheData))
            .catch(err => logger.error('Cache save error:', err));
        }
        
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey,
          'X-Cache-TTL': ttl.toString()
        });
        
        return originalSend.call(this, data);
      };
      
      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
};

/**
 * Cache pentru date utilizator (profil, setări)
 */
const userCache = cache({
  ttl: 900, // 15 minute
  prefix: 'user',
  condition: (req) => req.method === 'GET'
});

/**
 * Cache pentru categorii
 */
const categoryCache = cache({
  ttl: 1800, // 30 minute
  prefix: 'categories',
  condition: (req) => req.method === 'GET'
});

/**
 * Cache pentru cheltuieli (cu TTL scurt)
 */
const expenseCache = cache({
  ttl: 300, // 5 minute
  prefix: 'expenses',
  condition: (req) => req.method === 'GET' && !req.query['realtime']
});

/**
 * Cache pentru rapoarte și statistici
 */
const reportCache = cache({
  ttl: 3600, // 1 oră
  prefix: 'reports',
  condition: (req) => req.method === 'GET'
});

/**
 * Cache pentru date publice (nu depind de utilizator)
 */
const publicCache = cache({
  ttl: 7200, // 2 ore
  prefix: 'public',
  condition: (req) => req.method === 'GET'
});

/**
 * Invalidează cache-ul pentru un pattern specific
 */
const invalidateCache = async (pattern: string): Promise<void> => {
  if (!isRedisConnected || !redisClient) return;
  
  try {
    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      await redisClient.del(keys);
      logger.logPerformance('cacheInvalidated', 0, {
        pattern,
        keysCount: keys.length
      });
    }
  } catch (error) {
    logger.error('Cache invalidation error:', error);
  }
};

/**
 * Middleware pentru invalidarea cache-ului după modificări
 */
const cacheInvalidator = (patterns: Pattern[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const originalJson = res.json;
    const originalSend = res.send;
    
    res.json = function(data: unknown) {
      // Invalidează cache-ul doar pentru operațiuni de succes
      if (res.statusCode >= 200 && res.statusCode < 300) {
        patterns.forEach(pattern => {
          const resolvedPattern = typeof pattern === 'function' 
            ? pattern(req, res) 
            : pattern;
          invalidateCache(resolvedPattern);
        });
      }
      return originalJson.call(this, data);
    };
    
    res.send = function(data: unknown) {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        patterns.forEach(pattern => {
          const resolvedPattern = typeof pattern === 'function' 
            ? pattern(req, res) 
            : pattern;
          invalidateCache(resolvedPattern);
        });
      }
      return originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * Invalidatori specifici pentru diferite resurse
 */
const invalidators = {
  // Invalidează cache-ul utilizatorului curent
  user: cacheInvalidator([
    (req: AuthenticatedRequest) => `user:*${req.user?.id}*`,
    (req: AuthenticatedRequest) => `expenses:*${req.user?.id}*`,
    (req: AuthenticatedRequest) => `reports:*${req.user?.id}*`
  ]),
  
  // Invalidează cache-ul categoriilor
  categories: cacheInvalidator([
    (req: AuthenticatedRequest) => `categories:*${req.user?.id}*`,
    (req: AuthenticatedRequest) => `expenses:*${req.user?.id}*`,
    (req: AuthenticatedRequest) => `reports:*${req.user?.id}*`
  ]),
  
  // Invalidează cache-ul cheltuielilor
  expenses: cacheInvalidator([
    (req: AuthenticatedRequest) => `expenses:*${req.user?.id}*`,
    (req: AuthenticatedRequest) => `reports:*${req.user?.id}*`
  ]),
  
  // Invalidează toate cache-urile utilizatorului
  all: cacheInvalidator([
    (req: AuthenticatedRequest) => `*:*${req.user?.id}*`
  ])
};

/**
 * Middleware pentru cache warming (preîncărcarea cache-ului)
 */
const cacheWarmer = (warmupFunctions: WarmupFunction[]) => {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction): Promise<void> => {
    // Rulează warming-ul în background
    setImmediate(async () => {
      for (const warmupFn of warmupFunctions) {
        try {
          await warmupFn(req);
        } catch (error) {
          logger.error('Cache warming error:', error);
        }
      }
    });
    
    next();
  };
};

/**
 * Funcții de warming pentru date frecvent accesate
 */
const warmupFunctions = {
  userCategories: async (req: AuthenticatedRequest): Promise<void> => {
    if (!req.user?.id || !redisClient) return;
    
    const cacheKey = generateCacheKey('categories', {
      originalUrl: `/api/categories`,
      method: 'GET',
      user: { id: req.user.id },
      query: {},
      params: {}
    } as AuthenticatedRequest);
    
    const exists = await redisClient.exists(cacheKey);
    if (!exists) {
      // Aici ar trebui să faci query-ul real pentru categorii
      // și să salvezi rezultatul în cache
      logger.logPerformance('cacheWarmup', 0, {
        type: 'userCategories',
        userId: req.user.id,
        cacheKey
      });
    }
  }
};

/**
 * Statistici cache
 */
const getCacheStats = async (): Promise<any> => {
  if (!isRedisConnected || !redisClient) {
    return { error: 'Redis not connected' };
  }
  
  try {
    const info = await redisClient.info('memory');
    const keyspace = await redisClient.info('keyspace');
    
    return {
      connected: isRedisConnected,
      memory: info,
      keyspace: keyspace
    };
  } catch (error) {
    logger.error('Error getting cache stats:', error);
    return { error: (error as Error).message };
  }
};

/**
 * Curăță cache-ul expirat
 */
const cleanupExpiredCache = async (): Promise<void> => {
  if (!isRedisConnected || !redisClient) return;
  
  try {
    // Redis se ocupă automat de expirare, dar putem face cleanup manual
    const keys = await redisClient.keys('*');
    let expiredCount = 0;
    
    for (const key of keys) {
      const ttl = await redisClient.ttl(key);
      if (ttl === -1) { // Chei fără expirare
        await redisClient.expire(key, 3600); // Setează expirare de 1 oră
        expiredCount++;
      }
    }
    
    logger.logPerformance('cacheCleanup', 0, {
      totalKeys: keys.length,
      keysWithoutTTL: expiredCount
    });
  } catch (error) {
    logger.error('Cache cleanup error:', error);
  }
};

// Funcție pentru închiderea conexiunii Redis
const closeCache = async (): Promise<void> => {
  try {
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
      logger.info('Redis connection closed successfully');
    }
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
    throw error;
  }
};

// Export named pentru invalidatori
export const invalidateUserCache = invalidators.user;
export const invalidateCategoryCache = invalidators.categories;
export const invalidateExpenseCache = invalidators.expenses;

export {
  initializeRedis,
  cache,
  userCache,
  categoryCache,
  expenseCache,
  reportCache,
  publicCache,
  invalidateCache,
  invalidators,
  cacheWarmer,
  warmupFunctions,
  getCacheStats,
  cleanupExpiredCache,
  generateCacheKey,
  closeCache
};