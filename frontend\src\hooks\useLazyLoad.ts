import { useEffect, useRef, useState, useCallback } from 'react';
import { safeLog } from '../utils/safeLogger';

interface UseLazyLoadOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  fallbackDelay?: number;
}

interface UseLazyLoadReturn {
  ref: React.RefObject<HTMLElement>;
  isVisible: boolean;
  hasBeenVisible: boolean;
}

/**
 * Hook pentru lazy loading cu Intersection Observer
 */
export const useLazyLoad = (options: UseLazyLoadOptions = {}): UseLazyLoadReturn => {
  const { threshold = 0.1, rootMargin = '50px', triggerOnce = true, fallbackDelay = 300 } = options;

  const ref = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Fallback pentru browsere care nu suportă Intersection Observer
    if (!window.IntersectionObserver) {
      const timer = setTimeout(() => {
        setIsVisible(true);
        setHasBeenVisible(true);
      }, fallbackDelay);

      return () => clearTimeout(timer);
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isIntersecting = entry?.isIntersecting;

        setIsVisible(isIntersecting || false);

        if (isIntersecting && !hasBeenVisible) {
          setHasBeenVisible(true);
        }

        // Oprește observarea după prima vizibilitate dacă triggerOnce este true
        if (isIntersecting && triggerOnce) {
          observer.unobserve(element);
        }
      },
      {
        threshold,
        rootMargin,
      },
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce, fallbackDelay, hasBeenVisible]);

  return { ref, isVisible, hasBeenVisible };
};

/**
 * Hook pentru lazy loading de imagini
 */
export const useLazyImage = (src: string, options: UseLazyLoadOptions = {}) => {
  const { ref, isVisible, hasBeenVisible } = useLazyLoad(options);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (!hasBeenVisible) return;

    const img = new Image();

    img.onload = () => {
      setImageSrc(src);
      setIsLoaded(true);
      setIsError(false);
    };

    img.onerror = () => {
      setIsError(true);
      setIsLoaded(false);
    };

    img.src = src;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src, hasBeenVisible]);

  return {
    ref,
    imageSrc,
    isLoaded,
    isError,
    isVisible,
    hasBeenVisible,
  };
};

/**
 * Hook pentru lazy loading de componente React
 */
export const useLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: UseLazyLoadOptions = {},
) => {
  const { ref, isVisible, hasBeenVisible } = useLazyLoad(options);
  const [Component, setComponent] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!hasBeenVisible || Component) return;

    setIsLoading(true);
    setError(null);

    importFunc()
      .then(module => {
        setComponent(() => module.default);
        setIsLoading(false);
      })
      .catch(err => {
        setError(err);
        setIsLoading(false);
      });
  }, [hasBeenVisible, Component, importFunc]);

  return {
    ref,
    Component,
    isLoading,
    error,
    isVisible,
    hasBeenVisible,
  };
};

/**
 * Hook pentru preloading de resurse
 */
export const usePreload = () => {
  const preloadedResources = useRef(new Set<string>());

  const preloadImage = useCallback((src: string): Promise<void> => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };

      img.onerror = reject;
      img.src = src;
    });
  }, []);

  const preloadScript = useCallback((src: string): Promise<void> => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');

      script.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };

      script.onerror = reject;
      script.src = src;

      // Nu adăugăm script-ul în DOM, doar îl preîncărcăm
      document.head.appendChild(script);
      document.head.removeChild(script);
    });
  }, []);

  const preloadStylesheet = useCallback((href: string): Promise<void> => {
    if (preloadedResources.current.has(href)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');

      link.onload = () => {
        preloadedResources.current.add(href);
        resolve();
      };

      link.onerror = reject;
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;

      document.head.appendChild(link);
    });
  }, []);

  const preloadRoute = useCallback(async (routePath: string) => {
    try {
      // Preîncarcă componenta pentru rută
      const routeModule = await import(`../pages${routePath}`);

      // Preîncarcă și alte resurse asociate rutei
      if (routeModule.preloadResources) {
        await routeModule.preloadResources();
      }

      preloadedResources.current.add(routePath);
    } catch (error) {
      safeLog.warn(`Failed to preload route: ${routePath}`, error as Error);
    }
  }, []);

  return {
    preloadImage,
    preloadScript,
    preloadStylesheet,
    preloadRoute,
    isPreloaded: (resource: string) => preloadedResources.current.has(resource),
  };
};

/**
 * Hook pentru optimizarea performanței cu requestIdleCallback
 */
export const useIdleCallback = (
  callback: () => void,
  deps: React.DependencyList,
  options: { timeout?: number } = {},
) => {
  const { timeout = 5000 } = options;

  useEffect(() => {
    if (!window.requestIdleCallback) {
      // Fallback pentru browsere care nu suportă requestIdleCallback
      const timer = setTimeout(callback, 0);
      return () => clearTimeout(timer);
    }

    const handle = window.requestIdleCallback(callback, { timeout });

    return () => window.cancelIdleCallback(handle);
  }, deps);
};

/**
 * Hook pentru debouncing cu cleanup automat
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook pentru throttling
 */
export const useThrottle = <T extends (...args: unknown[]) => any>(
  callback: T,
  delay: number,
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay],
  );
};

/**
 * Hook pentru detectarea conexiunii lente
 */
export const useSlowConnection = (): boolean => {
  const [isSlowConnection, setIsSlowConnection] = useState(false);

  useEffect(() => {
    // Type assertion pentru Network Information API
    const nav = navigator as any;
    if (!nav.connection) {
      return;
    }

    const connection = nav.connection;

    const checkConnection = () => {
      const slowConnections = ['slow-2g', '2g', '3g'];
      setIsSlowConnection(
        slowConnections.includes(connection.effectiveType) || connection.downlink < 1.5,
      );
    };

    checkConnection();
    connection.addEventListener('change', checkConnection);

    return () => {
      connection.removeEventListener('change', checkConnection);
    };
  }, []);

  return isSlowConnection;
};
