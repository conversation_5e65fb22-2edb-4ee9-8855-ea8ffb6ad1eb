import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Link, useNavigate } from 'react-router-dom';
import { z } from 'zod';

import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Logo from '../../components/ui/Logo';
import { useAuthStore } from '../../store/authStore';

// Schema de validare pentru formularul de înregistrare
const registerSchema = z
  .object({
    firstName: z
      .string()
      .min(1, 'Prenumele este obligatoriu')
      .min(2, 'Prenumele trebuie să aibă cel puțin 2 caractere'),
    lastName: z
      .string()
      .min(1, 'Numele este obligatoriu')
      .min(2, 'Numele trebuie să aibă cel puțin 2 caractere'),
    email: z.string().min(1, 'Email-ul este obligatoriu').email('Email-ul nu este valid'),
    password: z
      .string()
      .min(1, 'Parola este obligatorie')
      .min(8, 'Parola trebuie să aibă cel puțin 8 caractere')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră',
      ),
    confirmPassword: z.string().min(1, 'Confirmarea parolei este obligatorie'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Parolele nu se potrivesc',
    path: ['confirmPassword'],
  });

type RegisterFormData = z.infer<typeof registerSchema>;

const Register: React.FC = () => {
  const { register: registerUser, isLoading } = useAuthStore();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterFormData) => {
    try {
      const result = await registerUser({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
        currency: 'USD',
        timezone: 'UTC',
      });
      if (result.success) {
        // Toast-ul de succes este deja afișat în store
        navigate('/login');
      } else {
        toast.error(result.message || 'Eroare la crearea contului');
      }
    } catch (error: unknown) {
      toast.error((error as any).message || 'Eroare la crearea contului');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Header cu logo și buton de întoarcere */}
      <div className="absolute top-0 left-0 right-0 z-10">
        <div className="flex items-center justify-between p-6">
          <Link
            to="/"
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors group"
          >
            <ArrowLeftIcon className="h-5 w-5 group-hover:-translate-x-1 transition-transform" />
            <span className="font-medium">Înapoi la pagina principală</span>
          </Link>
          <Logo size="sm" showText={true} />
        </div>
      </div>

      {/* Conținutul principal */}
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {/* Card principal */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
            <div className="text-center mb-8">
              <div className="mb-6">
                <Logo size="lg" showText={true} className="mx-auto" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Creează un cont nou</h2>
              <p className="text-gray-600">
                Alătură-te comunității FinanceNinja și începe să-ți gestionezi finanțele inteligent
              </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-5">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Input
                      {...register('firstName')}
                      type="text"
                      label="Prenume"
                      placeholder="Introdu prenumele"
                      error={errors.firstName?.message}
                      autoComplete="given-name"
                      className="rounded-xl"
                    />
                  </div>

                  <div>
                    <Input
                      {...register('lastName')}
                      type="text"
                      label="Nume"
                      placeholder="Introdu numele"
                      error={errors.lastName?.message}
                      autoComplete="family-name"
                      className="rounded-xl"
                    />
                  </div>
                </div>

                <div>
                  <Input
                    {...register('email')}
                    type="email"
                    label="Adresa de email"
                    placeholder="<EMAIL>"
                    error={errors.email?.message}
                    autoComplete="email"
                    className="rounded-xl"
                  />
                </div>

                <div>
                  <Input
                    {...register('password')}
                    type="password"
                    label="Parola"
                    placeholder="Introdu parola"
                    error={errors.password?.message}
                    autoComplete="new-password"
                    className="rounded-xl"
                  />
                </div>

                <div>
                  <Input
                    {...register('confirmPassword')}
                    type="password"
                    label="Confirmă parola"
                    placeholder="Confirmă parola"
                    error={errors.confirmPassword?.message}
                    autoComplete="new-password"
                    className="rounded-xl"
                  />
                </div>
              </div>

              <div className="flex items-start">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors mt-1"
                  required
                />
                <label htmlFor="terms" className="ml-3 block text-sm text-gray-700 leading-relaxed">
                  Accept{' '}
                  <Link
                    to="/terms"
                    className="text-primary-600 hover:text-primary-700 transition-colors font-medium"
                  >
                    termenii și condițiile
                  </Link>{' '}
                  și{' '}
                  <Link
                    to="/privacy"
                    className="text-primary-600 hover:text-primary-700 transition-colors font-medium"
                  >
                    politica de confidențialitate
                  </Link>
                </label>
              </div>

              <div className="space-y-4">
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? 'Se creează contul...' : 'Creează contul'}
                </Button>

                {/* Separator */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500">sau</span>
                  </div>
                </div>

                {/* Link către login */}
                <div className="text-center">
                  <p className="text-gray-600">
                    Ai deja un cont?{' '}
                    <Link
                      to="/login"
                      className="font-semibold text-primary-600 hover:text-primary-700 transition-colors"
                    >
                      Conectează-te aici
                    </Link>
                  </p>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
