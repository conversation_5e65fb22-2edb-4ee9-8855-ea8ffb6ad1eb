import express from 'express';
import { expenseController } from '../controllers/expenseController';
import { authenticateToken } from '../middleware/auth';
import { validate, validateParams, expenseSchemas, paramSchemas } from '../middleware/validation';
import { usageMiddleware, requirePremium } from '../middleware/usageLimits';

const router = express.Router();

/**
 * @route   GET /api/expenses
 * @desc    Get all expenses for the authenticated user with filtering and pagination
 * @access  Private
 */
router.get('/', authenticateToken, validate(expenseSchemas.query), expenseController.getExpenses);

/**
 * @route   GET /api/expenses/stats
 * @desc    Get expense statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, expenseController.getExpenseStats);

/**
 * @route   GET /api/expenses/trends
 * @desc    Get monthly expense trends
 * @access  Private
 */
router.get('/trends', authenticateToken, expenseController.getMonthlyTrends);

/**
 * @route   GET /api/expenses/tags
 * @desc    Get popular tags
 * @access  Private
 */
router.get('/tags', authenticateToken, expenseController.getPopularTags);

/**
 * @route   GET /api/expenses/:id
 * @desc    Get a single expense by ID
 * @access  Private
 */
router.get('/:id', authenticateToken, validateParams(paramSchemas.id), expenseController.getExpense);

/**
 * @route   POST /api/expenses
 * @desc    Create a new expense
 * @access  Private
 */
router.post(
  '/',
  authenticateToken,
  ...usageMiddleware('createExpense'),
  validate(expenseSchemas.create),
  expenseController.createExpense,
);

/**
 * @route   PUT /api/expenses/:id
 * @desc    Update an expense
 * @access  Private
 */
router.put(
  '/:id',
  authenticateToken,
  validateParams(paramSchemas.id),
  validate(expenseSchemas.update),
  expenseController.updateExpense,
);

/**
 * @route   DELETE /api/expenses/:id
 * @desc    Delete an expense
 * @access  Private
 */
router.delete('/:id', authenticateToken, validateParams(paramSchemas.id), expenseController.deleteExpense);

/**
 * @route   POST /api/expenses/:id/tags
 * @desc    Add tag to expense
 * @access  Private
 */
router.post('/:id/tags', authenticateToken, validateParams(paramSchemas.id), expenseController.addTag);

/**
 * @route   DELETE /api/expenses/:id/tags
 * @desc    Remove tag from expense
 * @access  Private
 */
router.delete('/:id/tags', authenticateToken, validateParams(paramSchemas.id), expenseController.removeTag);

/**
 * @route   DELETE /api/expenses/bulk
 * @desc    Bulk delete expenses
 * @access  Private
 */
router.delete('/bulk', authenticateToken, expenseController.bulkDeleteExpenses);

export default router;
