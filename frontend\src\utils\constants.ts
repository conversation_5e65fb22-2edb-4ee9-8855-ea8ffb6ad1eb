// Tipuri pentru configurări
export type Environment = 'development' | 'production' | 'test';

export interface ApiEndpoints {
  AUTH: {
    REGISTER: string;
    LOGIN: string;
    LOGOUT: string;
    REFRESH: string;
    PROFILE: string;
    CHANGE_PASSWORD: string;
    FORGOT_PASSWORD: string;
    RESET_PASSWORD: string;
    VERIFY_EMAIL: string;
    RESEND_VERIFICATION: string;
  };
  CATEGORIES: {
    BASE: string;
    BY_ID: (id: string) => string;
    STATS: string;
    REORDER: string;
    SET_DEFAULT: string;
  };
  EXPENSES: {
    BASE: string;
    BY_ID: (id: string) => string;
    STATS: string;
    TRENDS: string;
    TAGS: string;
    BULK_DELETE: string;
    ADD_TAG: (id: string) => string;
    REMOVE_TAG: (id: string, tag: string) => string;
  };
}

// Configurări de mediu
export const ENV = {
  DEVELOPMENT: 'development' as const,
  PRODUCTION: 'production' as const,
  TEST: 'test' as const,
};

// URL-ul de bază pentru API
export const API_BASE_URL: string = process.env['REACT_APP_API_URL'] || 'http://localhost:3001/api';
export const API_SERVER_URL: string =
  process.env['REACT_APP_API_BASE_URL'] || 'http://localhost:3001';

// Versiunea API
export const API_VERSION: string = 'v1';

// Endpoint-urile API
export const API_ENDPOINTS: ApiEndpoints = {
  // Autentificare
  AUTH: {
    REGISTER: '/auth/register',
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
    RESEND_VERIFICATION: '/auth/resend-verification',
  },

  // Categorii
  CATEGORIES: {
    BASE: '/categories',
    BY_ID: (id: string) => `/categories/${id}`,
    STATS: '/categories/stats',
    REORDER: '/categories/reorder',
    SET_DEFAULT: '/categories/set-default',
  },

  // Cheltuieli
  EXPENSES: {
    BASE: '/expenses',
    BY_ID: (id: string) => `/expenses/${id}`,
    STATS: '/expenses/stats',
    TRENDS: '/expenses/trends',
    TAGS: '/expenses/tags',
    BULK_DELETE: '/expenses/bulk-delete',
    ADD_TAG: (id: string) => `/expenses/${id}/tags`,
    REMOVE_TAG: (id: string, tag: string) => `/expenses/${id}/tags/${tag}`,
  },
};

// Tipuri pentru configurări
export interface PaginationConfig {
  DEFAULT_PAGE: number;
  DEFAULT_LIMIT: number;
  MAX_LIMIT: number;
  LIMITS: number[];
}

export interface ValidationConfig {
  PASSWORD: {
    MIN_LENGTH: number;
    MAX_LENGTH: number;
    REQUIRE_UPPERCASE: boolean;
    REQUIRE_LOWERCASE: boolean;
    REQUIRE_NUMBERS: boolean;
    REQUIRE_SPECIAL_CHARS: boolean;
  };
  EMAIL: {
    MAX_LENGTH: number;
  };
  NAME: {
    MIN_LENGTH: number;
    MAX_LENGTH: number;
  };
  EXPENSE: {
    MIN_AMOUNT: number;
    MAX_AMOUNT: number;
    MAX_DESCRIPTION_LENGTH: number;
    MAX_NOTES_LENGTH: number;
    MAX_LOCATION_LENGTH: number;
  };
  CATEGORY: {
    MIN_NAME_LENGTH: number;
    MAX_NAME_LENGTH: number;
    MAX_DESCRIPTION_LENGTH: number;
  };
}

// Configurări pentru paginare
export const PAGINATION: PaginationConfig = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
  LIMITS: [10, 20, 50, 100],
};

// Configurări pentru validare
export const VALIDATION: ValidationConfig = {
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
  },
  EMAIL: {
    MAX_LENGTH: 255,
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
  },
  EXPENSE: {
    MIN_AMOUNT: 0.01,
    MAX_AMOUNT: 999999.99,
    MAX_DESCRIPTION_LENGTH: 500,
    MAX_NOTES_LENGTH: 1000,
    MAX_LOCATION_LENGTH: 200,
  },
  CATEGORY: {
    MIN_NAME_LENGTH: 2,
    MAX_NAME_LENGTH: 50,
    MAX_DESCRIPTION_LENGTH: 200,
  },
};

// Tipuri pentru metode de plată
export type PaymentMethod =
  | 'cash'
  | 'card'
  | 'bank_transfer'
  | 'digital_wallet'
  | 'check'
  | 'other';

export interface PaymentMethodConfig {
  CASH: PaymentMethod;
  CARD: PaymentMethod;
  BANK_TRANSFER: PaymentMethod;
  DIGITAL_WALLET: PaymentMethod;
  CHECK: PaymentMethod;
  OTHER: PaymentMethod;
}

// Tipuri de plată
export const PAYMENT_METHODS: PaymentMethodConfig = {
  CASH: 'cash',
  CARD: 'card',
  BANK_TRANSFER: 'bank_transfer',
  DIGITAL_WALLET: 'digital_wallet',
  CHECK: 'check',
  OTHER: 'other',
} as const;

// Etichete pentru tipurile de plată
export const PAYMENT_METHOD_LABELS = {
  [PAYMENT_METHODS.CASH]: 'Numerar',
  [PAYMENT_METHODS.CARD]: 'Card',
  [PAYMENT_METHODS.BANK_TRANSFER]: 'Transfer bancar',
  [PAYMENT_METHODS.DIGITAL_WALLET]: 'Portofel digital',
  [PAYMENT_METHODS.CHECK]: 'Cec',
  [PAYMENT_METHODS.OTHER]: 'Altele',
} as const;

// Icoane pentru tipurile de plată
export const PAYMENT_METHOD_ICONS = {
  [PAYMENT_METHODS.CASH]: '💵',
  [PAYMENT_METHODS.CARD]: '💳',
  [PAYMENT_METHODS.BANK_TRANSFER]: '🏦',
  [PAYMENT_METHODS.DIGITAL_WALLET]: '📱',
  [PAYMENT_METHODS.CHECK]: '📝',
  [PAYMENT_METHODS.OTHER]: '💰',
} as const;

// Tipuri pentru frecvențe recurente
export type RecurringFrequency = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

export interface RecurringFrequencyConfig {
  DAILY: RecurringFrequency;
  WEEKLY: RecurringFrequency;
  MONTHLY: RecurringFrequency;
  QUARTERLY: RecurringFrequency;
  YEARLY: RecurringFrequency;
}

// Tipuri pentru valute
export type Currency = 'RON' | 'EUR' | 'USD' | 'GBP';

export interface CurrencyConfig {
  RON: Currency;
  EUR: Currency;
  USD: Currency;
  GBP: Currency;
}

// Frecvențe pentru cheltuielile recurente
export const RECURRING_FREQUENCIES: RecurringFrequencyConfig = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly',
} as const;

// Etichete pentru frecvențe
export const RECURRING_FREQUENCY_LABELS = {
  [RECURRING_FREQUENCIES.DAILY]: 'Zilnic',
  [RECURRING_FREQUENCIES.WEEKLY]: 'Săptămânal',
  [RECURRING_FREQUENCIES.MONTHLY]: 'Lunar',
  [RECURRING_FREQUENCIES.QUARTERLY]: 'Trimestrial',
  [RECURRING_FREQUENCIES.YEARLY]: 'Anual',
} as const;

// Valute suportate
export const CURRENCIES: CurrencyConfig = {
  RON: 'RON',
  EUR: 'EUR',
  USD: 'USD',
  GBP: 'GBP',
} as const;

// Simboluri pentru valute
export const CURRENCY_SYMBOLS = {
  [CURRENCIES.RON]: 'lei',
  [CURRENCIES.EUR]: '€',
  [CURRENCIES.USD]: '$',
  [CURRENCIES.GBP]: '£',
} as const;

// Tipuri pentru limbi
export type Language = 'ro' | 'en';

export interface LanguageConfig {
  RO: Language;
  EN: Language;
}

// Limbi suportate
export const LANGUAGES: LanguageConfig = {
  RO: 'ro',
  EN: 'en',
} as const;

// Etichete pentru limbi
export const LANGUAGE_LABELS = {
  [LANGUAGES.RO]: 'Română',
  [LANGUAGES.EN]: 'English',
} as const;

// Fusuri orare
export const TIMEZONES = {
  BUCHAREST: 'Europe/Bucharest',
  LONDON: 'Europe/London',
  NEW_YORK: 'America/New_York',
  LOS_ANGELES: 'America/Los_Angeles',
};

// Etichete pentru fusuri orare
export const TIMEZONE_LABELS = {
  [TIMEZONES.BUCHAREST]: 'București (UTC+2)',
  [TIMEZONES.LONDON]: 'Londra (UTC+0)',
  [TIMEZONES.NEW_YORK]: 'New York (UTC-5)',
  [TIMEZONES.LOS_ANGELES]: 'Los Angeles (UTC-8)',
};

// Tipuri de grafice pentru rapoarte
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  DOUGHNUT: 'doughnut',
  AREA: 'area',
};

// Perioade pentru rapoarte
export const REPORT_PERIODS = {
  LAST_7_DAYS: 'last_7_days',
  LAST_30_DAYS: 'last_30_days',
  LAST_3_MONTHS: 'last_3_months',
  LAST_6_MONTHS: 'last_6_months',
  LAST_YEAR: 'last_year',
  THIS_MONTH: 'this_month',
  THIS_YEAR: 'this_year',
  CUSTOM: 'custom',
};

// Etichete pentru perioade
export const REPORT_PERIOD_LABELS = {
  [REPORT_PERIODS.LAST_7_DAYS]: 'Ultimele 7 zile',
  [REPORT_PERIODS.LAST_30_DAYS]: 'Ultimele 30 zile',
  [REPORT_PERIODS.LAST_3_MONTHS]: 'Ultimele 3 luni',
  [REPORT_PERIODS.LAST_6_MONTHS]: 'Ultimele 6 luni',
  [REPORT_PERIODS.LAST_YEAR]: 'Ultimul an',
  [REPORT_PERIODS.THIS_MONTH]: 'Luna aceasta',
  [REPORT_PERIODS.THIS_YEAR]: 'Anul acesta',
  [REPORT_PERIODS.CUSTOM]: 'Perioadă personalizată',
};

// Tipuri de sortare
export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
};

// Câmpuri pentru sortarea cheltuielilor
export const EXPENSE_SORT_FIELDS = {
  DATE: 'date',
  AMOUNT: 'amount',
  DESCRIPTION: 'description',
  CATEGORY: 'category',
  PAYMENT_METHOD: 'paymentMethod',
  CREATED_AT: 'createdAt',
};

// Etichete pentru câmpurile de sortare
export const EXPENSE_SORT_FIELD_LABELS = {
  [EXPENSE_SORT_FIELDS.DATE]: 'Data',
  [EXPENSE_SORT_FIELDS.AMOUNT]: 'Suma',
  [EXPENSE_SORT_FIELDS.DESCRIPTION]: 'Descriere',
  [EXPENSE_SORT_FIELDS.CATEGORY]: 'Categorie',
  [EXPENSE_SORT_FIELDS.PAYMENT_METHOD]: 'Metoda de plată',
  [EXPENSE_SORT_FIELDS.CREATED_AT]: 'Data creării',
};

// Configurări pentru toast notifications
export const TOAST_CONFIG = {
  DURATION: {
    SHORT: 2000,
    MEDIUM: 4000,
    LONG: 6000,
  },
  POSITION: {
    TOP_LEFT: 'top-left',
    TOP_CENTER: 'top-center',
    TOP_RIGHT: 'top-right',
    BOTTOM_LEFT: 'bottom-left',
    BOTTOM_CENTER: 'bottom-center',
    BOTTOM_RIGHT: 'bottom-right',
  },
};

// Configurări pentru localStorage
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_PREFERENCES: 'userPreferences',
  THEME: 'theme',
  LANGUAGE: 'language',
  CURRENCY: 'currency',
  LAST_VISITED_PAGE: 'lastVisitedPage',
};

// Configurări pentru cache
export const CACHE_CONFIG = {
  STALE_TIME: {
    SHORT: 1000 * 60, // 1 minut
    MEDIUM: 1000 * 60 * 5, // 5 minute
    LONG: 1000 * 60 * 30, // 30 minute
  },
  CACHE_TIME: {
    SHORT: 1000 * 60 * 5, // 5 minute
    MEDIUM: 1000 * 60 * 10, // 10 minute
    LONG: 1000 * 60 * 60, // 1 oră
  },
};

// Configurări pentru debounce
export const DEBOUNCE_DELAYS = {
  SEARCH: 300,
  INPUT: 500,
  RESIZE: 250,
  SCROLL: 100,
};

// Configurări pentru animații
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 250,
  SLOW: 350,
  VERY_SLOW: 500,
};

// Breakpoint-uri pentru responsive design
export const BREAKPOINTS = {
  XS: 480,
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
};

// Configurări pentru fișiere
export const FILE_CONFIG = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: {
    IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    DOCUMENTS: ['application/pdf', 'text/csv', 'application/vnd.ms-excel'],
  },
  ALLOWED_EXTENSIONS: {
    IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    DOCUMENTS: ['.pdf', '.csv', '.xls', '.xlsx'],
  },
};

// Mesaje de eroare comune
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Eroare de rețea. Verifică conexiunea la internet.',
  SERVER_ERROR: 'Eroare de server. Te rugăm să încerci din nou.',
  UNAUTHORIZED: 'Nu ești autorizat să accesezi această resursă.',
  FORBIDDEN: 'Nu ai permisiunea să efectuezi această acțiune.',
  NOT_FOUND: 'Resursa solicitată nu a fost găsită.',
  VALIDATION_ERROR: 'Datele introduse nu sunt valide.',
  TIMEOUT_ERROR: 'Cererea a expirat. Te rugăm să încerci din nou.',
};

// Mesaje de succes comune
export const SUCCESS_MESSAGES = {
  CREATED: 'Creat cu succes!',
  UPDATED: 'Actualizat cu succes!',
  DELETED: 'Șters cu succes!',
  SAVED: 'Salvat cu succes!',
  SENT: 'Trimis cu succes!',
};

// Configurări pentru export/import
export const EXPORT_CONFIG = {
  FORMATS: {
    CSV: 'csv',
    JSON: 'json',
    PDF: 'pdf',
  },
  MAX_RECORDS: 10000,
};

// Configurări pentru notificări
export const NOTIFICATION_CONFIG = {
  TYPES: {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },
  AUTO_DISMISS: {
    INFO: 4000,
    SUCCESS: 3000,
    WARNING: 5000,
    ERROR: 6000,
  },
};
