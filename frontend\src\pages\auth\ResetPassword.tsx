import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { z } from 'zod';

import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';


// Schema de validare pentru formularul de reset password
const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(1, 'Parola este obligatorie')
    .min(8, 'Parola trebuie să aibă cel puțin 8 caractere')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră'),
  confirmPassword: z
    .string()
    .min(1, 'Confirmarea parolei este obligatorie'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Parolele nu se potrivesc',
  path: ['confirmPassword'],
});

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

const ResetPassword: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  useEffect(() => {
    // Verifică dacă token-ul este prezent
    if (!token) {
      toast.error('Token invalid sau lipsă');
      navigate('/forgot-password');
    }
  }, [token, navigate]);

  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true);
    try {
      // Aici ar trebui să faci request către API pentru resetarea parolei
      // await authService.resetPassword(token, data.password);

      // Simulare request
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success('Parola a fost resetată cu succes!');
      navigate('/login');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Eroare la resetarea parolei';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Link invalid
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Link-ul de resetare a parolei este invalid sau a expirat.
            </p>
          </div>

          <div className="mt-8">
            <Link
              to="/forgot-password"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Solicită un nou link
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Setează parola nouă
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Introdu noua parolă pentru contul tău.
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <Input
                {...register('password')}
                type="password"
                label="Parola nouă"
                placeholder="Introdu parola nouă"
                error={errors.password?.message as string}
                autoComplete="new-password"
              />
            </div>

            <div>
              <Input
                {...register('confirmPassword')}
                type="password"
                label="Confirmă parola nouă"
                placeholder="Confirmă parola nouă"
                error={errors.confirmPassword?.message as string}
                autoComplete="new-password"
              />
            </div>
          </div>

          <div>
            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              loading={isLoading}
              disabled={isLoading}
            >
              {isLoading ? 'Se resetează...' : 'Resetează parola'}
            </Button>
          </div>

          <div className="text-center">
            <Link
              to="/login"
              className="font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Înapoi la conectare
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResetPassword;
