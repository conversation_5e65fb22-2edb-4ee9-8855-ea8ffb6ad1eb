import express, { Request, Response } from 'express';
import { adaptiveResponseTransform, debugCaseFormat } from '../middleware/responseTransform';

const router = express.Router();

// Aplică middleware-ul de transformare pentru toate rutele API
if (process.env.NODE_ENV === 'development') {
  router.use(debugCaseFormat());
}
router.use(adaptiveResponseTransform());

// Import route modules
import authRoutes from './auth';
import categoryRoutes from './categories';
import expenseRoutes from './expenses';
import usageRoutes from './usage';
import subscriptionRoutes from './subscription';
import webhookRoutes from './webhooks';
import exportRoutes from './export';
import adminRoutes from './admin';
import userRoutes from './users';

// Health check endpoint
router.get('/health', (res: Response) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
  });
});

// API documentation endpoint
router.get('/docs', (res: Response) => {
  res.json({
    success: true,
    message: 'Expense Tracker API Documentation',
    version: process.env.API_VERSION || '1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/register': 'Register a new user',
        'POST /api/auth/login': 'Login user',
        'POST /api/auth/refresh': 'Refresh access token',
        'POST /api/auth/logout': 'Logout user',
        'GET /api/auth/profile': 'Get user profile',
        'PUT /api/auth/profile': 'Update user profile',
        'POST /api/auth/change-password': 'Change user password',
        'POST /api/auth/forgot-password': 'Request password reset',
        'POST /api/auth/reset-password': 'Reset password with token',
        'POST /api/auth/verify-email': 'Verify email address',
        'POST /api/auth/resend-verification': 'Resend email verification',
      },
      categories: {
        'GET /api/categories': 'Get all categories',
        'GET /api/categories/stats': 'Get categories with statistics',
        'GET /api/categories/:id': 'Get single category',
        'GET /api/categories/:id/stats': 'Get category statistics',
        'POST /api/categories': 'Create new category',
        'PUT /api/categories/:id': 'Update category',
        'DELETE /api/categories/:id': 'Delete category',
        'POST /api/categories/reorder': 'Reorder categories',
        'POST /api/categories/:id/set-default': 'Set default category',
      },
      expenses: {
        'GET /api/expenses': 'Get all expenses with filtering',
        'GET /api/expenses/stats': 'Get expense statistics',
        'GET /api/expenses/trends': 'Get monthly trends',
        'GET /api/expenses/tags': 'Get popular tags',
        'GET /api/expenses/:id': 'Get single expense',
        'POST /api/expenses': 'Create new expense',
        'PUT /api/expenses/:id': 'Update expense',
        'DELETE /api/expenses/:id': 'Delete expense',
        'POST /api/expenses/:id/tags': 'Add tag to expense',
        'DELETE /api/expenses/:id/tags': 'Remove tag from expense',
        'DELETE /api/expenses/bulk': 'Bulk delete expenses',
      },
      users: {
        'GET /api/users/profile': 'Get current user profile',
        'PUT /api/users/profile': 'Update user profile',
        'PUT /api/users/password': 'Change user password',
        'DELETE /api/users/account': 'Delete user account',
      },
      usage: {
        'GET /api/usage/current': 'Get current usage information',
        'GET /api/usage/stats': 'Get detailed usage statistics',
        'POST /api/usage/check-action': 'Check if user can perform action',
        'GET /api/usage/upgrade-recommendations': 'Get upgrade recommendations',
      },
      subscriptions: {
        'GET /api/subscriptions/plans': 'Get available subscription plans',
        'GET /api/subscriptions/current': 'Get current subscription',
        'POST /api/subscriptions/checkout': 'Create checkout session',
        'POST /api/subscriptions/portal': 'Create customer portal',
        'POST /api/subscriptions/cancel': 'Cancel subscription',
        'POST /api/subscriptions/reactivate': 'Reactivate subscription',
        'GET /api/subscriptions/checkout/:sessionId': 'Check checkout session status',
        'GET /api/subscriptions/usage': 'Get usage statistics',
        'GET /api/subscriptions/permission/:action': 'Check action permission',
      },
      webhooks: {
        'POST /api/webhooks/stripe': 'Stripe webhook endpoint',
        'GET /api/webhooks/health': 'Webhook health check',
      },
      export: {
        'GET /api/export/csv': 'Export expenses to CSV (Basic/Premium)',
        'GET /api/export/pdf': 'Export expenses to PDF (Premium)',
        'GET /api/export/excel': 'Export expenses to Excel (Premium)',
      },
      admin: {
        'GET /api/admin/dashboard': 'Get admin dashboard (redirects to stats)',
        'GET /api/admin/dashboard/stats': 'Get admin dashboard statistics',
        'GET /api/admin/alerts': 'Get system alerts',
        'POST /api/admin/alerts/:alertId/read': 'Mark alert as read',
        'GET /api/admin/users': 'Get users list with pagination',
        'GET /api/admin/users/:userId': 'Get user details',
        'GET /api/admin/subscriptions': 'Get subscriptions list',
        'GET /api/admin/subscriptions/stats': 'Get subscription statistics',
        'GET /api/admin/plans/stats': 'Get plan statistics',
        'GET /api/admin/usage/stats': 'Get usage statistics',
        'GET /api/admin/revenue/data': 'Get revenue data',
        'GET /api/admin/activity': 'Get user activity logs',
      },
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      note: 'Most endpoints require authentication except registration, login, and password reset',
    },
    responseFormat: {
      success: {
        success: true,
        message: 'Success message',
        data: 'Response data',
      },
      error: {
        success: false,
        message: 'Error message',
        errors: 'Validation errors (optional)',
      },
    },
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/categories', categoryRoutes);
router.use('/expenses', expenseRoutes);
router.use('/users', userRoutes);
router.use('/usage', usageRoutes);
router.use('/subscriptions', subscriptionRoutes);
router.use('/webhooks', webhookRoutes);
router.use('/export', exportRoutes);
router.use('/admin', adminRoutes);

// 404 handler for API routes
router.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: `API endpoint ${req.originalUrl} not found`,
    availableEndpoints: [
      'GET /api/health',
      'GET /api/docs',
      'POST /api/auth/register',
      'POST /api/auth/login',
      'GET /api/categories',
      'GET /api/expenses',
    ],
  });
});

export default router;