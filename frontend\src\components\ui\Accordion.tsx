import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import React, { useState, createContext, useContext, useRef, useEffect } from 'react';

import { cn } from '../../utils/helpers';

// Tipuri pentru accordion
type AccordionType = 'single' | 'multiple';
type AccordionVariant = 'default' | 'bordered' | 'ghost' | 'filled';
type AccordionSize = 'sm' | 'md' | 'lg';
type IconPosition = 'left' | 'right';
type AnimationType = 'slide' | 'fade' | 'scale';

// Interfețe pentru proprietăți
interface AccordionContextValue {
  value: string | string[] | undefined;
  onValueChange: (value: string) => void;
  isItemOpen: (value: string) => boolean;
  type: AccordionType;
  disabled: boolean;
  variant: AccordionVariant;
  size: AccordionSize;
}

interface AccordionProps {
  children: React.ReactNode;
  type?: AccordionType;
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[] | undefined) => void;
  collapsible?: boolean;
  disabled?: boolean;
  variant?: AccordionVariant;
  size?: AccordionSize;
  className?: string;
}

interface AccordionItemProps {
  children: React.ReactNode;
  value: string;
  disabled?: boolean;
  className?: string;
}

interface AccordionTriggerProps {
  children: React.ReactNode;
  value: string;
  disabled?: boolean;
  className?: string;
  icon?: React.ComponentType<{ className?: string }>;
  iconPosition?: IconPosition;
}

interface AccordionContentProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  forceMount?: boolean;
}

interface AnimatedAccordionContentProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  animation?: AnimationType;
}

interface LazyAccordionContentProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  loader?: React.ReactNode;
}

interface FAQItem {
  id?: string;
  question: string;
  answer: string | React.ReactNode;
}

interface FAQAccordionProps {
  items: FAQItem[];
}

interface NavigationItem {
  id?: string;
  title: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
}

interface NavigationAccordionProps {
  items: NavigationItem[];
}

/**
 * Context pentru gestionarea stării accordion-ului
 */
const AccordionContext = createContext<AccordionContextValue | undefined>(undefined);

/**
 * Hook pentru utilizarea contextului accordion-ului
 */
const useAccordion = (): AccordionContextValue => {
  const context = useContext(AccordionContext);
  if (!context) {
    throw new Error('useAccordion must be used within an Accordion component');
  }
  return context;
};

/**
 * Componenta Accordion principală
 */
const Accordion: React.FC<AccordionProps> = ({
  children,
  type = 'single',
  defaultValue,
  value: controlledValue,
  onValueChange,
  collapsible = false,
  disabled = false,
  variant = 'default',
  size = 'md',
  className = '',
  ...rest
}) => {
  const [internalValue, setInternalValue] = useState<string | string[] | undefined>(
    type === 'multiple' ? defaultValue || [] : defaultValue,
  );

  const value = controlledValue !== undefined ? controlledValue : internalValue;

  const handleValueChange = (itemValue: string) => {
    let newValue: string | string[] | undefined;

    if (type === 'multiple') {
      const currentArray = Array.isArray(value) ? value : [];
      if (currentArray.includes(itemValue)) {
        newValue = currentArray.filter(v => v !== itemValue);
      } else {
        newValue = [...currentArray, itemValue];
      }
    } else {
      // Pentru type === 'single'
      if (value === itemValue && collapsible) {
        newValue = undefined;
      } else {
        newValue = itemValue;
      }
    }

    if (onValueChange) {
      onValueChange(newValue);
    } else {
      setInternalValue(newValue);
    }
  };

  const isItemOpen = (itemValue: string): boolean => {
    if (type === 'multiple') {
      return Array.isArray(value) && value.includes(itemValue);
    }
    return value === itemValue;
  };

  const contextValue: AccordionContextValue = {
    value,
    onValueChange: handleValueChange,
    isItemOpen,
    type,
    disabled,
    variant,
    size,
  };

  const variantClasses: Record<AccordionVariant, string> = {
    default: 'border border-gray-200 rounded-lg',
    bordered: 'border border-gray-200',
    ghost: '',
    filled: 'bg-gray-50',
  };

  return (
    <AccordionContext.Provider value={contextValue}>
      <div className={cn('w-full', variantClasses[variant], className)} {...rest}>
        {children}
      </div>
    </AccordionContext.Provider>
  );
};

/**
 * Item individual din accordion
 */
export const AccordionItem: React.FC<AccordionItemProps> = ({
  children,
  value: itemValue,
  disabled: itemDisabled = false,
  className = '',
  ...rest
}) => {
  const { disabled: accordionDisabled, variant } = useAccordion();
  const isDisabled = accordionDisabled || itemDisabled;

  const variantClasses: Record<AccordionVariant, string> = {
    default: 'border-b border-gray-200 last:border-b-0',
    bordered: 'border-b border-gray-200 last:border-b-0',
    ghost: 'mb-2 last:mb-0',
    filled: 'mb-1 last:mb-0 bg-white rounded-lg border border-gray-200',
  };

  return (
    <div
      className={cn(variantClasses[variant], isDisabled && 'opacity-50', className)}
      data-value={itemValue}
      {...rest}
    >
      {children}
    </div>
  );
};

/**
 * Trigger pentru deschiderea/închiderea unui item
 */
export const AccordionTrigger: React.FC<AccordionTriggerProps> = ({
  children,
  value: itemValue,
  disabled: itemDisabled = false,
  className = '',
  icon,
  iconPosition = 'right',
  ...rest
}) => {
  const { onValueChange, isItemOpen, disabled: accordionDisabled, variant, size } = useAccordion();

  const isDisabled = accordionDisabled || itemDisabled;
  const isOpen = isItemOpen(itemValue);

  const handleClick = () => {
    if (!isDisabled && itemValue !== undefined) {
      onValueChange(itemValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  const sizeClasses: Record<AccordionSize, string> = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg',
  };

  const variantClasses: Record<AccordionVariant, string> = {
    default: 'hover:bg-gray-50',
    bordered: 'hover:bg-gray-50',
    ghost: 'hover:bg-gray-100 rounded-lg',
    filled: 'hover:bg-gray-100',
  };

  const DefaultIcon = variant === 'ghost' ? ChevronRightIcon : ChevronDownIcon;
  const IconComponent = icon || DefaultIcon;

  return (
    <button
      type="button"
      className={cn(
        'flex items-center justify-between w-full text-left',
        'font-medium transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        !isDisabled && 'cursor-pointer',
        isDisabled && 'cursor-not-allowed',
        sizeClasses[size],
        variantClasses[variant],
        className,
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={isDisabled}
      aria-expanded={isOpen}
      aria-controls={`accordion-content-${itemValue}`}
      id={`accordion-trigger-${itemValue}`}
      {...rest}
    >
      {iconPosition === 'left' && (
        <IconComponent
          className={cn(
            'w-5 h-5 transition-transform duration-200 flex-shrink-0 mr-3',
            isOpen && (variant === 'ghost' ? 'rotate-90' : 'rotate-180'),
          )}
        />
      )}

      <span className="flex-1">{children}</span>

      {iconPosition === 'right' && (
        <IconComponent
          className={cn(
            'w-5 h-5 transition-transform duration-200 flex-shrink-0 ml-3',
            isOpen && (variant === 'ghost' ? 'rotate-90' : 'rotate-180'),
          )}
        />
      )}
    </button>
  );
};

/**
 * Conținutul unui item din accordion
 */
export const AccordionContent: React.FC<AccordionContentProps> = ({
  children,
  value: itemValue,
  className = '',
  forceMount = false,
  ...rest
}) => {
  const { isItemOpen, size, variant } = useAccordion();
  const [height, setHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);

  const isOpen = isItemOpen(itemValue);

  useEffect(() => {
    if (contentRef.current) {
      if (isOpen) {
        setHeight(contentRef.current.scrollHeight);
      } else {
        setHeight(0);
      }
    }
  }, [isOpen, children]);

  const sizeClasses: Record<AccordionSize, string> = {
    sm: 'px-3 pb-2',
    md: 'px-4 pb-3',
    lg: 'px-6 pb-4',
  };

  const variantClasses: Record<AccordionVariant, string> = {
    default: '',
    bordered: '',
    ghost: 'px-0',
    filled: '',
  };

  if (!isOpen && !forceMount) {
    return null;
  }

  return (
    <div
      className="overflow-hidden transition-all duration-300 ease-in-out"
      style={{ height: forceMount ? 'auto' : height }}
    >
      <div
        ref={contentRef}
        className={cn(
          'text-gray-600',
          sizeClasses[size],
          variantClasses[variant],
          !isOpen && forceMount && 'hidden',
          className,
        )}
        role="region"
        aria-labelledby={`accordion-trigger-${itemValue}`}
        id={`accordion-content-${itemValue}`}
        {...rest}
      >
        {children}
      </div>
    </div>
  );
};

/**
 * Accordion cu animații avansate
 */
export const AnimatedAccordionContent: React.FC<AnimatedAccordionContentProps> = ({
  children,
  value: itemValue,
  className = '',
  animation = 'slide',
  ...rest
}) => {
  const { isItemOpen, size } = useAccordion();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [height, setHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);

  const isOpen = isItemOpen(itemValue);

  useEffect(() => {
    if (contentRef.current) {
      if (isOpen) {
        setIsVisible(true);
        setHeight(contentRef.current.scrollHeight);
      } else {
        setHeight(0);
        const timer = setTimeout(() => setIsVisible(false), 300);
        return () => clearTimeout(timer);
      }
    }
    return undefined;
  }, [isOpen, children]);

  const sizeClasses: Record<AccordionSize, string> = {
    sm: 'px-3 pb-2',
    md: 'px-4 pb-3',
    lg: 'px-6 pb-4',
  };

  const animationClasses: Record<AnimationType, string> = {
    slide: 'transition-all duration-300 ease-in-out',
    fade: 'transition-all duration-300 ease-in-out',
    scale: 'transition-all duration-300 ease-in-out origin-top',
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={cn('overflow-hidden', animationClasses[animation])}
      style={{
        height: animation === 'slide' ? height : 'auto',
        opacity: animation === 'fade' ? (isOpen ? 1 : 0) : 1,
        transform: animation === 'scale' ? (isOpen ? 'scaleY(1)' : 'scaleY(0)') : 'none',
      }}
    >
      <div
        ref={contentRef}
        className={cn('text-gray-600', sizeClasses[size], className)}
        role="region"
        aria-labelledby={`accordion-trigger-${itemValue}`}
        id={`accordion-content-${itemValue}`}
        {...rest}
      >
        {children}
      </div>
    </div>
  );
};

/**
 * Accordion cu lazy loading
 */
export const LazyAccordionContent: React.FC<LazyAccordionContentProps> = ({
  children,
  value: itemValue,
  className = '',
  loader = <div className="flex items-center justify-center py-4">Se încarcă...</div>,
  ...rest
}) => {
  const { isItemOpen } = useAccordion();
  const [hasLoaded, setHasLoaded] = useState<boolean>(false);
  const isOpen = isItemOpen(itemValue);

  useEffect(() => {
    if (isOpen && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isOpen, hasLoaded]);

  return (
    <AccordionContent value={itemValue} className={className} {...rest}>
      {hasLoaded ? children : isOpen ? loader : null}
    </AccordionContent>
  );
};

/**
 * Accordion simplu pentru FAQ
 */
export const FAQAccordion: React.FC<FAQAccordionProps> = ({ items = [], ...props }) => {
  return (
    <Accordion type="single" collapsible variant="bordered" {...props}>
      {items.map((item, index) => {
        const itemValue = item.id || index.toString();
        return (
          <AccordionItem key={itemValue} value={itemValue}>
            <AccordionTrigger value={itemValue}>{item.question}</AccordionTrigger>
            <AccordionContent value={itemValue}>
              {typeof item.answer === 'string' ? <p>{item.answer}</p> : item.answer}
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};

/**
 * Accordion pentru navigare
 */
export const NavigationAccordion: React.FC<NavigationAccordionProps> = ({
  items = [],
  ...props
}) => {
  return (
    <Accordion type="single" variant="ghost" {...props}>
      {items.map((item, index) => {
        const itemValue = item.id || index.toString();
        return (
          <AccordionItem key={itemValue} value={itemValue}>
            <AccordionTrigger value={itemValue} iconPosition="left">
              <div className="flex items-center gap-3">
                {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                <span>{item.title}</span>
                {item.badge && <span className="ml-auto">{item.badge}</span>}
              </div>
            </AccordionTrigger>
            <AccordionContent value={itemValue}>{item.content}</AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};

/**
 * Hook pentru controlul programatic al accordion-ului
 */
export const useAccordionControl = (type: AccordionType = 'single') => {
  const [value, setValue] = useState<string | string[] | undefined>(
    type === 'multiple' ? [] : undefined,
  );

  const open = (itemValue: string) => {
    if (type === 'multiple') {
      setValue(prev => {
        const currentArray = Array.isArray(prev) ? prev : [];
        if (!currentArray.includes(itemValue)) {
          return [...currentArray, itemValue];
        }
        return currentArray;
      });
    } else {
      setValue(itemValue);
    }
  };

  const close = (itemValue: string) => {
    if (type === 'multiple') {
      setValue(prev => {
        const currentArray = Array.isArray(prev) ? prev : [];
        return currentArray.filter(v => v !== itemValue);
      });
    } else {
      setValue(undefined);
    }
  };

  const toggle = (itemValue: string) => {
    if (type === 'multiple') {
      setValue(prev => {
        const currentArray = Array.isArray(prev) ? prev : [];
        if (currentArray.includes(itemValue)) {
          return currentArray.filter(v => v !== itemValue);
        }
        return [...currentArray, itemValue];
      });
    } else {
      setValue(prev => (prev === itemValue ? undefined : itemValue));
    }
  };

  const openAll = (itemValues: string[]) => {
    if (type === 'multiple') {
      setValue(itemValues);
    }
  };

  const closeAll = () => {
    setValue(type === 'multiple' ? [] : undefined);
  };

  const isOpen = (itemValue: string): boolean => {
    if (type === 'multiple') {
      return Array.isArray(value) && value.includes(itemValue);
    }
    return value === itemValue;
  };

  return {
    value,
    setValue,
    open,
    close,
    toggle,
    openAll,
    closeAll,
    isOpen,
  };
};

export default Accordion;
