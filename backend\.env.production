# Production Environment Variables

# Environment
NODE_ENV=production
PORT=3001

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/expense_tracker_prod"
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30000

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password
REDIS_TIMEOUT=5000

# JWT Secrets (use strong, unique secrets in production)
JWT_SECRET=your_super_secure_jwt_secret_key_here_min_32_chars
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here_min_32_chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_your_live_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PRICE_ID_PREMIUM=price_live_premium_plan_id

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_live_client_id
PAYPAL_CLIENT_SECRET=your_paypal_live_client_secret
PAYPAL_MODE=live

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Security
CORS_ORIGIN=https://financeapp.com,https://www.financeapp.com
SESSION_SECRET=your_super_secure_session_secret_min_32_chars
ENCRYPTION_KEY=your_32_char_encryption_key_here
HASH_SALT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# File Upload
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DEST=uploads/

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
NEW_RELIC_APP_NAME=FinanceApp-Backend

# Performance
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000
DB_QUERY_TIMEOUT=30000
API_TIMEOUT=30000

# Features
ENABLE_SWAGGER=false
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true
ENABLE_AUDIT_LOG=true

# SSL/TLS
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem
SSL_CA_PATH=/path/to/ssl/ca.pem

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=financeapp-backups
BACKUP_S3_REGION=us-east-1

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=financeapp-assets

# CDN
CDN_URL=https://cdn.financeapp.com
ASSETS_URL=https://assets.financeapp.com

# API Documentation
API_DOCS_URL=https://docs.financeapp.com
API_VERSION=v1

# Webhooks
WEBHOOK_TIMEOUT=10000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=1000