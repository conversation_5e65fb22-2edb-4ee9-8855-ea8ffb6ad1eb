# Production Environment Variables

# API Configuration
REACT_APP_API_URL=https://api.financeapp.com/api
REACT_APP_API_BASE_URL=https://api.financeapp.com
API_BASE_URL=https://api.financeapp.com/api

# Stripe Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_stripe_key_here

# Environment
NODE_ENV=production

# App Configuration
REACT_APP_APP_NAME=FinanceApp
REACT_APP_VERSION=1.0.0

# Debug (disabled in production)
REACT_APP_DEBUG=false

# Features
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_ANALYTICS=true

# Security (enabled in production)
REACT_APP_ENABLE_HTTPS=true
REACT_APP_SECURE_COOKIES=true

# Production settings
REACT_APP_MOCK_API=false
REACT_APP_LOG_LEVEL=error

# Performance
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_CACHE_STRATEGY=cache-first

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
REACT_APP_SENTRY_DSN=https://<EMAIL>/project-id

# CDN
REACT_APP_CDN_URL=https://cdn.financeapp.com
REACT_APP_ASSETS_URL=https://assets.financeapp.com

# API Rate Limiting
REACT_APP_API_RATE_LIMIT=1000
REACT_APP_API_TIMEOUT=30000

# Build optimization
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false