import { EllipsisVerticalIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import Badge from '../../components/ui/Badge';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Dropdown from '../../components/ui/Dropdown';

import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import Pagination from '../../components/ui/Pagination';
import { DataTable } from '../../components/ui/Table';
import {
  useSubscriptions,
  useSubscriptionDetails,
  useSuspendSubscription,
  useReactivateSubscription,
  useCancelSubscription,
  useSyncWithStripe,
} from '../../hooks/useAdminData';
import { formatCurrency } from '../../utils/helpers';
import { safeLog } from '../../utils/safeLogger';
import type {
  Subscription,
  User,
  Plan,
  UseSubscriptionsParams,
  // PaginatedResponse, // Not used currently
  SuspendSubscriptionParams,
  CancelSubscriptionParams,
} from '../../types';

// Tipuri pentru hook-uri

interface SubscriptionWithDetails extends Omit<Subscription, 'plan'> {
  user: User;
  plan: Plan;
  totalRevenue?: number;
  paymentsCount?: number;
  failedPayments?: number;
  billingCycle?: 'monthly' | 'yearly';
  nextBillingDate?: string;
  startDate?: string;
  endDate?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
}

// interface SubscriptionsData { // Not used currently
//   subscriptions: SubscriptionWithDetails[];
//   total: number;
//   stats?: {
//     active: number;
//     paused: number;
//     canceled: number;
//   };
// }

const SubscriptionManager: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [planFilter, setPlanFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithDetails | null>(
    null,
  );
  const [showDetailsModal, setShowDetailsModal] = useState<boolean>(false);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [actionType, setActionType] = useState<string>('');

  const itemsPerPage = 10;

  // Hook-uri pentru gestionarea abonamentelor
  const {
    data: subscriptionsData,
    isLoading,
    error,
  } = useSubscriptions({
    page: currentPage,
    search: searchTerm,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    plan: planFilter !== 'all' ? planFilter : undefined,
    limit: itemsPerPage,
  } as UseSubscriptionsParams);

  const { data: subscriptionDetails } = useSubscriptionDetails(selectedSubscription?.id);

  const suspendSubscription = useSuspendSubscription();
  const reactivateSubscription = useReactivateSubscription();
  const cancelSubscription = useCancelSubscription();
  const syncWithStripe = useSyncWithStripe();

  const handleSubscriptionAction = (
    subscription: SubscriptionWithDetails,
    action: string,
  ): void => {
    setActionType(action);
    setSelectedSubscription(subscription);
    setShowConfirmModal(true);
  };

  const confirmAction = async () => {
    if (!selectedSubscription || !actionType) return;

    try {
      switch (actionType) {
        case 'suspend':
          await suspendSubscription.mutateAsync({
            subscriptionId: selectedSubscription.id,
            reason: 'Admin action',
          } as SuspendSubscriptionParams);
          break;
        case 'reactivate':
          await reactivateSubscription.mutateAsync(selectedSubscription.id);
          break;
        case 'cancel':
          await cancelSubscription.mutateAsync({
            subscriptionId: selectedSubscription.id,
            reason: 'Admin action',
          } as CancelSubscriptionParams);
          break;
        case 'sync':
          await syncWithStripe.mutateAsync(selectedSubscription.id);
          break;
        default:
          break;
      }
      setShowConfirmModal(false);
      setSelectedSubscription(null);
    } catch (error) {
      safeLog.error('Action failed:', error as Error);
    }
  };

  const getStatusBadge = (status: string): JSX.Element => {
    const statusConfig: Record<string, { variant: unknown; label: string }> = {
      active: { variant: 'success', label: 'Activ' },
      unpaid: { variant: 'secondary', label: 'Neplătit' },
      canceled: { variant: 'error', label: 'Anulat' },
      paused: { variant: 'warning', label: 'Suspendat' },
      past_due: { variant: 'error', label: 'Întârziat' },
      trialing: { variant: 'info', label: 'Perioadă de probă' },
    };

    const config = statusConfig[status] || statusConfig['unpaid'];
    return <Badge variant={config?.variant as any}>{config?.label}</Badge>;
  };

  const getPlanBadge = (plan?: string): JSX.Element => {
    const planConfig: Record<string, { variant: unknown; label: string; color?: string }> = {
      basic: { variant: 'primary', label: 'Basic', color: 'bg-blue-100 text-blue-800' },
      premium: { variant: 'success', label: 'Premium', color: 'bg-green-100 text-green-800' },
      enterprise: {
        variant: 'warning',
        label: 'Enterprise',
        color: 'bg-yellow-100 text-yellow-800',
      },
    };

    const config = planConfig[plan?.toLowerCase() ?? ''] ?? {
      variant: 'secondary',
      label: plan ?? 'Unknown',
    };
    return <Badge variant={config.variant as any}>{config.label}</Badge>;
  };

  const columns: any[] = [
    {
      key: 'user',
      title: 'Utilizator',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <div className="flex items-center">
          <div className="ml-0">
            <div className="text-sm font-medium text-gray-900">
              {`${subscription.user.firstName || ''} ${subscription.user.lastName || ''}`.trim() ||
                'Utilizator necunoscut'}
            </div>
            <div className="text-sm text-gray-500">{subscription.user.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'plan',
      title: 'Plan',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <div>
          {getPlanBadge(subscription.plan?.name ?? 'N/A')}
          <div className="text-xs text-gray-500 mt-1">
            {formatCurrency(subscription.plan?.price ?? 0)}/lună
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (_value: unknown, subscription: SubscriptionWithDetails) =>
        getStatusBadge(subscription.status ?? 'unpaid'),
    },
    {
      key: 'billing',
      title: 'Facturare',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {subscription.billingCycle === 'monthly' ? 'Lunar' : 'Anual'}
          </div>
          <div className="text-gray-500">
            Următoarea:{' '}
            {subscription.nextBillingDate
              ? new Date(subscription.nextBillingDate).toLocaleDateString('ro-RO')
              : 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'revenue',
      title: 'Venituri',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {formatCurrency(subscription.totalRevenue || 0)}
          </div>
          <div className="text-gray-500">{subscription.paymentsCount || 0} plăți</div>
          {subscription.failedPayments && subscription.failedPayments > 0 && (
            <div className="text-red-500 text-xs">{subscription.failedPayments} eșuate</div>
          )}
        </div>
      ),
    },
    {
      key: 'dates',
      title: 'Perioada',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <div className="text-sm">
          <div className="text-gray-900">
            Început:{' '}
            {subscription.startDate
              ? new Date(subscription.startDate).toLocaleDateString('ro-RO')
              : 'N/A'}
          </div>
          {subscription.endDate && (
            <div className="text-gray-500">
              Sfârșit: {new Date(subscription.endDate).toLocaleDateString('ro-RO')}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Acțiuni',
      render: (_value: unknown, subscription: SubscriptionWithDetails) => (
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm">
              <EllipsisVerticalIcon className="h-4 w-4" />
            </Button>
          }
        >
          <div className="py-1">
            <button
              onClick={() => {
                setSelectedSubscription(subscription);
                setShowDetailsModal(true);
              }}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              Vezi detalii
            </button>

            {subscription.status === 'active' && (
              <button
                onClick={() => handleSubscriptionAction(subscription, 'suspend')}
                className="block w-full text-left px-4 py-2 text-sm text-yellow-700 hover:bg-yellow-50"
              >
                Suspendă
              </button>
            )}

            {(subscription.status === 'unpaid' || subscription.status === 'canceled') && (
              <button
                onClick={() => handleSubscriptionAction(subscription, 'reactivate')}
                className="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50"
              >
                Reactivează
              </button>
            )}

            {subscription.status !== 'canceled' && (
              <button
                onClick={() => handleSubscriptionAction(subscription, 'cancel')}
                className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
              >
                Anulează
              </button>
            )}

            <button
              onClick={() => handleSubscriptionAction(subscription, 'sync')}
              className="block w-full text-left px-4 py-2 text-sm text-blue-700 hover:bg-blue-50"
            >
              Sincronizează cu Stripe
            </button>
          </div>
        </Dropdown>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">Eroare la încărcarea abonamentelor</div>
      </Card>
    );
  }

  const subscriptions = subscriptionsData?.pages?.flatMap(page => page.data || []) || [];
  const totalItems = subscriptionsData?.pages?.[0]?.pagination?.total || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Gestionarea abonamentelor</h3>
          <div className="text-sm text-gray-500">{totalItems} abonamente</div>
        </div>

        {/* Filtre */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Caută utilizatori..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            <select
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate statusurile</option>
              <option value="active">Activ</option>
              <option value="paused">Suspendat</option>
              <option value="canceled">Anulat</option>
              <option value="past_due">Întârziat</option>
            </select>
            <select
              value={planFilter}
              onChange={e => setPlanFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate planurile</option>
              <option value="basic">Basic</option>
              <option value="premium">Premium</option>
              <option value="enterprise">Enterprise</option>
            </select>
          </div>

          {/* Statistici rapide */}
          <div className="flex gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {subscriptions.filter(s => s.status === 'active').length}
              </div>
              <div className="text-gray-500">Active</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-yellow-600">
                {subscriptions.filter(s => s.status === 'unpaid').length}
              </div>
              <div className="text-gray-500">Neplătite</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-red-600">
                {subscriptions.filter(s => s.status === 'canceled').length}
              </div>
              <div className="text-gray-500">Anulate</div>
            </div>
          </div>
        </div>

        {/* Tabel abonamente */}
        <DataTable
          columns={columns}
          data={subscriptions}
          emptyMessage="Nu au fost găsite abonamente"
          loading={isLoading}
        />

        {/* Paginare */}
        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </Card>

      {/* Modal detalii abonament */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="Detalii abonament"
        size="lg"
      >
        {selectedSubscription && (
          <div className="space-y-6">
            {/* Informații utilizator */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Informații Utilizator</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">Nume:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {subscriptionDetails?.user
                      ? `${subscriptionDetails.user.firstName} ${subscriptionDetails.user.lastName}`
                      : selectedSubscription?.user
                        ? `${selectedSubscription.user.firstName} ${selectedSubscription.user.lastName}`
                        : 'Utilizator necunoscut'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Email:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {subscriptionDetails?.user?.email || selectedSubscription?.user?.email}
                  </p>
                </div>
              </div>
            </div>

            {/* Informații plan */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Informații Plan</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">Plan:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {subscriptionDetails?.plan?.name || selectedSubscription?.plan?.name}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Preț:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {formatCurrency(
                      subscriptionDetails?.plan?.price || selectedSubscription?.plan?.price || 0,
                    )}
                    /lună
                  </p>
                </div>
              </div>
            </div>

            {/* Status și facturare */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Status și Facturare</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">Status:</span>
                  <div className="mt-1">
                    {getStatusBadge(
                      subscriptionDetails?.status || selectedSubscription?.status || '',
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Ciclu facturare:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {(subscriptionDetails?.billingCycle || selectedSubscription?.billingCycle) ===
                    'monthly'
                      ? 'Lunar'
                      : 'Anual'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Data începerii:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {subscriptionDetails?.startDate || selectedSubscription?.startDate
                      ? new Date(
                          (subscriptionDetails?.startDate || selectedSubscription.startDate)!,
                        ).toLocaleDateString('ro-RO')
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Următoarea facturare:</span>
                  <p className="text-sm font-medium text-gray-900">
                    {subscriptionDetails?.nextBillingDate || selectedSubscription?.nextBillingDate
                      ? new Date(
                          (subscriptionDetails?.nextBillingDate ||
                            selectedSubscription.nextBillingDate)!,
                        ).toLocaleDateString('ro-RO')
                      : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            {/* Statistici financiare */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Statistici Financiare</h4>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <span className="text-sm text-gray-500">Venituri totale:</span>
                  <p className="text-lg font-semibold text-green-600">
                    {formatCurrency(
                      subscriptionDetails?.totalRevenue || selectedSubscription?.totalRevenue || 0,
                    )}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Plăți procesate:</span>
                  <p className="text-lg font-semibold text-blue-600">
                    {subscriptionDetails?.paymentsCount || selectedSubscription?.paymentsCount || 0}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Plăți eșuate:</span>
                  <p className="text-lg font-semibold text-red-600">
                    {subscriptionDetails?.failedPayments ||
                      selectedSubscription?.failedPayments ||
                      0}
                  </p>
                </div>
              </div>
            </div>

            {/* Informații Stripe */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Informații Stripe</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">ID Abonament Stripe:</span>
                  <p className="text-sm font-mono text-gray-900">
                    {subscriptionDetails?.stripeSubscriptionId ||
                      selectedSubscription?.stripeSubscriptionId ||
                      'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">ID Client Stripe:</span>
                  <p className="text-sm font-mono text-gray-900">
                    {subscriptionDetails?.stripeCustomerId ||
                      selectedSubscription?.stripeCustomerId ||
                      'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Modal confirmare acțiune */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirmă acțiunea"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            <p className="text-sm text-gray-700">
              Ești sigur că vrei să{' '}
              {actionType === 'suspend'
                ? 'suspenzi'
                : actionType === 'reactivate'
                  ? 'reactivezi'
                  : actionType === 'cancel'
                    ? 'anulezi'
                    : actionType === 'sync'
                      ? 'sincronizezi'
                      : 'modifici'}{' '}
              acest abonament?
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="secondary" onClick={() => setShowConfirmModal(false)}>
              Anulează
            </Button>
            <Button
              variant={actionType === 'cancel' ? 'danger' : 'primary'}
              onClick={confirmAction}
              disabled={
                suspendSubscription.isPending ||
                reactivateSubscription.isPending ||
                cancelSubscription.isPending ||
                syncWithStripe.isPending
              }
            >
              {suspendSubscription.isPending ||
              reactivateSubscription.isPending ||
              cancelSubscription.isPending ||
              syncWithStripe.isPending
                ? 'Se procesează...'
                : 'Confirmă'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SubscriptionManager;
