import React, { type ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../ui/LoadingSpinner';

interface ProtectedRouteProps {
  children: ReactNode;
  roles?: string[];
  requireAdmin?: boolean;
  redirectTo?: string;
  fallback?: ReactNode;
}

/**
 * Componentă pentru protejarea rutelor care necesită autentificare
 * Redirecționează utilizatorii neautentificați către pagina de login
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles = [],
  requireAdmin = false,
  redirectTo = '/login',
  fallback = null,
}) => {
  const { isAuthenticated, isLoading, user } = useAuthStore();
  const location = useLocation();

  // Afișează loader în timpul verificării autentificării
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Verificare autentificare...</p>
          </div>
        </div>
      )
    );
  }

  // Redirecționează către login dacă utilizatorul nu este autentificat
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location.pathname }} replace />;
  }

  // Verifică dacă este necesar rol de admin
  if (requireAdmin && user) {
    const userRole = user.role || 'user';

    if (userRole !== 'admin') {
      // Redirecționează către dashboard dacă nu este admin
      return (
        <Navigate
          to="/app/dashboard"
          state={{
            error:
              'Nu ai permisiunea să accesezi această pagină. Doar administratorii pot accesa această secțiune.',
            from: location.pathname,
          }}
          replace
        />
      );
    }
  }

  // Verifică rolurile dacă sunt specificate
  if (roles.length > 0 && user) {
    const userRole = user.role || 'user';
    const hasRequiredRole = roles.includes(userRole);

    if (!hasRequiredRole) {
      // Redirecționează către dashboard-ul corespunzător rolului utilizatorului
      const dashboardUrl = userRole === 'admin' ? '/app/admin/dashboard' : '/app/dashboard';
      return (
        <Navigate
          to={dashboardUrl}
          state={{
            error: 'Nu ai permisiunea să accesezi această pagină.',
            from: location.pathname,
          }}
          replace
        />
      );
    }
  }

  // Utilizatorul este autentificat și are rolurile necesare
  return children;
};

interface PermissionsResult {
  isAuthenticated: boolean;
  userRole: string;
  hasRequiredRole: boolean;
  canAccess: boolean;
  isAdmin: boolean;
  isUser: boolean;
}

/**
 * Hook pentru verificarea permisiunilor utilizatorului
 */
export const usePermissions = (requiredRoles: string[] = []): PermissionsResult => {
  const { user, isAuthenticated } = useAuthStore();

  const userRole = user?.role || '';
  const hasRequiredRole = requiredRoles.length === 0 || requiredRoles.includes(userRole);
  const isAdmin = userRole === 'admin';
  const isUser = userRole === 'user';

  return {
    isAuthenticated,
    userRole,
    hasRequiredRole,
    canAccess: hasRequiredRole,
    isAdmin,
    isUser,
  };
};

interface PermissionGuardProps {
  children: ReactNode;
  roles?: string[];
  fallback?: ReactNode;
}

/**
 * Componentă pentru protejarea bazată pe permisiuni
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  roles = [],
  fallback = null,
}) => {
  const { canAccess } = usePermissions(roles);

  return canAccess ? children : fallback;
};

interface AdminRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Componentă specializată pentru rute de administrator
 */
export const AdminRoute: React.FC<AdminRouteProps> = ({ children, fallback: _fallback = null }) => {
  return <ProtectedRoute roles={['admin']}>{children}</ProtectedRoute>;
};

interface OwnershipGuardProps {
  children: ReactNode;
  resourceUserId: string | number;
  fallback?: ReactNode;
}

/**
 * Componentă pentru verificarea dacă utilizatorul este proprietarul unei resurse
 */
export const OwnershipGuard: React.FC<OwnershipGuardProps> = ({
  resourceUserId,
  children,
  fallback = null,
}) => {
  const { user } = useAuthStore();

  const isOwner = user && (user.id === resourceUserId || user.role === 'admin');

  return isOwner ? children : fallback;
};

/**
 * HOC pentru protejarea componentelor
 */
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  roles: string[] = [],
) => {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute roles={roles}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
};

interface AuthStatusResult {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: unknown;
  error: unknown;
  isGuest: boolean;
}

/**
 * Hook pentru verificarea stării de autentificare
 */
export const useAuthStatus = (): AuthStatusResult => {
  const { isAuthenticated, isLoading, user, error } = useAuthStore();

  return {
    isAuthenticated,
    isLoading,
    user,
    error,
    isGuest: !isAuthenticated && !isLoading,
  };
};

export default ProtectedRoute;
