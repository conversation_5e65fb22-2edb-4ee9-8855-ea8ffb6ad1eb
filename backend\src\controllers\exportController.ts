import { prisma } from '../config/prisma';
import { Request, Response } from 'express';
import { Prisma } from '@prisma/client';
import { safeLog } from '../utils/safeLogger';
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  userId?: string;
}

// Temporary logger replacement
const logger = {
  info: (message: string, _data?: unknown) => { safeLog.debug(message); },
  error: (_message: string, error?: unknown) => { safeLog.error('ERROR:', error as Error); }
};

// Temporary AppError replacement
class AppError extends Error {
  statusCode: number;
  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
  }
}

/**
 * Export cheltuieli în format CSV
 */
const exportCSV = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req;
    const { startDate, endDate, categoryId } = req.query;

    // Convert query parameters to proper types
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;

    // Construiește filtrul pentru cheltuieli
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
      ...(startDateStr && endDateStr && {
        date: {
          gte: new Date(startDateStr),
          lte: new Date(endDateStr)
        }
      }),
      ...(categoryIdStr && { categoryId: categoryIdStr })
    };

    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Generează CSV
    const csvHeader = 'Data,Suma,Descriere,Categorie\n';
    const csvRows = expenses.map(expense => {
      const date = new Date(expense.date).toLocaleDateString('ro-RO');
      const amount = expense.amount.toString();
      const description = `"${expense.description.replace(/"/g, '""')}"`; // Escape quotes
      const category = expense.category?.name || 'N/A';
      return `${date},${amount},${description},${category}`;
    }).join('\n');

    const csvContent = csvHeader + csvRows;

    // Set headers pentru download
    const filename = `cheltuieli_${new Date().toISOString().split('T')[0]}.csv`;
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));

    // Adaugă BOM pentru UTF-8 (pentru Excel)
    res.write('\uFEFF');
    res.end(csvContent);

    logger.info(`CSV export generat pentru utilizatorul ${userId}`, {
      userId,
      expenseCount: expenses.length,
      filters: { startDate, endDate, categoryId }
    });

  } catch (error) {
    logger.error('Eroare la exportul CSV:', (error as Error));
    throw new AppError('Eroare la generarea exportului CSV', 500);
  }
};

/**
 * Export cheltuieli în format PDF
 */
const exportPDF = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req;
    const { startDate, endDate, categoryId } = req.query;

    // Convert query parameters to proper types
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;

    // Construiește filtrul pentru cheltuieli
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
      ...(startDateStr && endDateStr && {
        date: {
          gte: new Date(startDateStr),
          lte: new Date(endDateStr)
        }
      }),
      ...(categoryIdStr && { categoryId: categoryIdStr })
    };

    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Obține informații utilizator
    const user = await prisma.user.findUnique({
      where: { id: String(userId) },
      select: { email: true, firstName: true, lastName: true }
    });

    // Creează PDF
    const doc = new PDFDocument({ margin: 50 });

    // Set headers pentru download
    const filename = `cheltuieli_${new Date().toISOString().split('T')[0]}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe PDF la response
    doc.pipe(res);

    // Header PDF
    doc.fontSize(20).text('Raport Cheltuieli', { align: 'center' });
    doc.moveDown();

    // Informații utilizator
    if (user) {
      doc.fontSize(12).text(`Utilizator: ${user.firstName} ${user.lastName}`, { align: 'left' });
      doc.text(`Email: ${user.email}`, { align: 'left' });
    }

    // Perioada și filtre
    if (startDateStr && endDateStr) {
      doc.text(`Perioada: ${new Date(startDateStr).toLocaleDateString('ro-RO')} - ${new Date(endDateStr).toLocaleDateString('ro-RO')}`, { align: 'left' });
    }

    doc.text(`Data generării: ${new Date().toLocaleDateString('ro-RO')}`, { align: 'left' });
    doc.text(`Total cheltuieli: ${expenses.length}`, { align: 'left' });
    doc.moveDown();

    // Calculează totalul
    const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0);
    doc.fontSize(14).text(`Total suma: ${total.toFixed(2)} RON`, { align: 'right' });
    doc.moveDown();

    // Tabel header
    doc.fontSize(10);
    const tableTop = doc.y;
    const dateX = 50;
    const amountX = 150;
    const categoryX = 220;
    const descriptionX = 320;

    doc.text('Data', dateX, tableTop, { width: 90 });
    doc.text('Suma', amountX, tableTop, { width: 60 });
    doc.text('Categorie', categoryX, tableTop, { width: 90 });
    doc.text('Descriere', descriptionX, tableTop, { width: 200 });

    // Linie separator
    doc.moveTo(50, tableTop + 15)
       .lineTo(550, tableTop + 15)
       .stroke();

    let currentY = tableTop + 25;

    // Adaugă cheltuielile
    expenses.forEach((expense, index) => {
      // Verifică dacă trebuie să adaugi o pagină nouă
      if (currentY > 700) {
        doc.addPage();
        currentY = 50;
      }

      const date = new Date(expense.date).toLocaleDateString('ro-RO');
      const amount = `${Number(expense.amount).toFixed(2)} RON`;
      const category = expense.category?.name || 'N/A';
      const description = expense.description.length > 40
        ? `${expense.description.substring(0, 37)}...`
        : expense.description;

      doc.text(date, dateX, currentY, { width: 90 });
      doc.text(amount, amountX, currentY, { width: 60 });
      doc.text(category, categoryX, currentY, { width: 90 });
      doc.text(description, descriptionX, currentY, { width: 200 });

      currentY += 20;

      // Adaugă linie separator la fiecare 5 rânduri
      if ((index + 1) % 5 === 0) {
        doc.moveTo(50, currentY - 5)
           .lineTo(550, currentY - 5)
           .stroke();
      }
    });

    // Finalizează PDF
    doc.end();

    logger.info(`PDF export generat pentru utilizatorul ${userId}`, {
      userId,
      expenseCount: expenses.length,
      total: total.toFixed(2),
      filters: { startDate, endDate, categoryId }
    });

  } catch (error) {
    logger.error('Eroare la exportul PDF:', (error as Error));
    throw new AppError('Eroare la generarea exportului PDF', 500);
  }
};

/**
 * Export cheltuieli în format Excel
 */
const exportExcel = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req;
    const { startDate, endDate, categoryId } = req.query;

    // Convert query parameters to proper types
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;

    // Construiește filtrul pentru cheltuieli
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
      ...(startDateStr && endDateStr && {
        date: {
          gte: new Date(startDateStr),
          lte: new Date(endDateStr)
        }
      }),
      ...(categoryIdStr && { categoryId: categoryIdStr })
    };

    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Obține informații utilizator
    const user = await prisma.user.findUnique({
      where: { id: String(userId) },
      select: { email: true, firstName: true, lastName: true }
    });

    // Creează workbook Excel
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Cheltuieli');

    // Setează proprietățile workbook-ului
    workbook.creator = user ? `${user.firstName} ${user.lastName}` : 'Expense Tracker';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Header informații
    worksheet.mergeCells('A1:D1');
    worksheet.getCell('A1').value = 'RAPORT CHELTUIELI';
    worksheet.getCell('A1').font = { size: 16, bold: true };
    worksheet.getCell('A1').alignment = { horizontal: 'center' };

    // Informații utilizator
    let currentRow = 3;
    if (user) {
      worksheet.getCell(`A${currentRow}`).value = 'Utilizator:';
      worksheet.getCell(`B${currentRow}`).value = `${user.firstName} ${user.lastName}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = 'Email:';
      worksheet.getCell(`B${currentRow}`).value = user.email;
      currentRow++;
    }

    // Perioada și filtre
    if (startDateStr && endDateStr) {
      worksheet.getCell(`A${currentRow}`).value = 'Perioada:';
      worksheet.getCell(`B${currentRow}`).value = `${new Date(startDateStr).toLocaleDateString('ro-RO')} - ${new Date(endDateStr).toLocaleDateString('ro-RO')}`;
      currentRow++;
    }

    worksheet.getCell(`A${currentRow}`).value = 'Data generării:';
    worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleDateString('ro-RO');
    currentRow++;

    worksheet.getCell(`A${currentRow}`).value = 'Total cheltuieli:';
    worksheet.getCell(`B${currentRow}`).value = expenses.length;
    currentRow += 2;

    // Calculează totalul
    const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0);
    worksheet.getCell(`A${currentRow}`).value = 'TOTAL SUMA:';
    worksheet.getCell(`B${currentRow}`).value = `${total.toFixed(2)} RON`;
    worksheet.getCell(`A${currentRow}`).font = { bold: true };
    worksheet.getCell(`B${currentRow}`).font = { bold: true };
    currentRow += 2;

    // Header tabel
    const headerRow = currentRow;
    worksheet.getCell(`A${headerRow}`).value = 'Data';
    worksheet.getCell(`B${headerRow}`).value = 'Suma (RON)';
    worksheet.getCell(`C${headerRow}`).value = 'Categorie';
    worksheet.getCell(`D${headerRow}`).value = 'Descriere';
    worksheet.getCell(`E${headerRow}`).value = 'Metoda de plată';
    worksheet.getCell(`F${headerRow}`).value = 'Tag-uri';

    // Stilizează header-ul
    ['A', 'B', 'C', 'D', 'E', 'F'].forEach(col => {
      const cell = worksheet.getCell(`${col}${headerRow}`);
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // Adaugă datele
    expenses.forEach((expense, index) => {
      const row = headerRow + 1 + index;

      worksheet.getCell(`A${row}`).value = new Date(expense.date).toLocaleDateString('ro-RO');
      worksheet.getCell(`B${row}`).value = Number(expense.amount);
      worksheet.getCell(`C${row}`).value = expense.category?.name || 'N/A';
      worksheet.getCell(`D${row}`).value = expense.description;
      worksheet.getCell(`E${row}`).value = expense.paymentMethod || 'N/A';
      worksheet.getCell(`F${row}`).value = Array.isArray(expense.tags) ? expense.tags.join(', ') : '';

      // Formatează suma ca număr cu 2 zecimale
      worksheet.getCell(`B${row}`).numFmt = '#,##0.00';

      // Adaugă borduri
      ['A', 'B', 'C', 'D', 'E', 'F'].forEach(col => {
        const cell = worksheet.getCell(`${col}${row}`);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // Ajustează lățimea coloanelor
    worksheet.getColumn('A').width = 12; // Data
    worksheet.getColumn('B').width = 15; // Suma
    worksheet.getColumn('C').width = 20; // Categorie
    worksheet.getColumn('D').width = 40; // Descriere
    worksheet.getColumn('E').width = 15; // Metoda de plată
    worksheet.getColumn('F').width = 25; // Tag-uri

    // Set headers pentru download
    const filename = `cheltuieli_${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Scrie workbook-ul în response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Excel export generat pentru utilizatorul ${userId}`, {
      userId,
      expenseCount: expenses.length,
      total: total.toFixed(2),
      filters: { startDate, endDate, categoryId }
    });

  } catch (error) {
    logger.error('Eroare la exportul Excel:', (error as Error));
    throw new AppError('Eroare la generarea exportului Excel', 500);
  }
};

/**
 * Export cheltuieli în format JSON
 */
const exportJSON = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req;
    const { startDate, endDate, categoryId } = req.query;

    // Convert query parameters to proper types
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;

    // Construiește filtrul pentru cheltuieli
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
      ...(startDateStr && endDateStr && {
        date: {
          gte: new Date(startDateStr),
          lte: new Date(endDateStr)
        }
      }),
      ...(categoryIdStr && { categoryId: categoryIdStr })
    };

    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Obține informații utilizator
    const user = await prisma.user.findUnique({
      where: { id: String(userId) },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        currency: true,
        timezone: true
      }
    });

    // Calculează statistici
    const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0);
    const categoriesStats = expenses.reduce((acc, expense) => {
      const categoryName = expense.category?.name || 'Necategorizat';
      if (!acc[categoryName]) {
        acc[categoryName] = { count: 0, total: 0 };
      }
      acc[categoryName].count++;
      acc[categoryName].total += Number(expense.amount);
      return acc;
    }, {} as Record<string, { count: number; total: number }>);

    // Construiește obiectul de export
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        exportType: 'JSON',
        user: user ? {
          id: user.id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email,
          currency: user.currency,
          timezone: user.timezone
        } : null,
        filters: {
          startDate: startDateStr,
          endDate: endDateStr,
          categoryId: categoryIdStr
        },
        period: startDateStr && endDateStr ? {
          start: startDateStr,
          end: endDateStr,
          days: Math.ceil((new Date(endDateStr).getTime() - new Date(startDateStr).getTime()) / (1000 * 60 * 60 * 24))
        } : null
      },
      summary: {
        totalExpenses: expenses.length,
        totalAmount: Number(total.toFixed(2)),
        currency: user?.currency || 'RON',
        averageExpense: expenses.length > 0 ? Number((total / expenses.length).toFixed(2)) : 0,
        categoriesCount: Object.keys(categoriesStats).length
      },
      categoriesStats,
      expenses: expenses.map(expense => ({
        id: expense.id,
        date: expense.date.toISOString().split('T')[0],
        amount: Number(expense.amount),
        description: expense.description,
        category: expense.category ? {
          id: expense.category.id,
          name: expense.category.name,
          color: expense.category.color,
          icon: expense.category.icon
        } : null,
        paymentMethod: expense.paymentMethod,
        tags: expense.tags,
        createdAt: expense.createdAt.toISOString(),
        updatedAt: expense.updatedAt.toISOString()
      }))
    };

    // Set headers pentru download
    const filename = `cheltuieli_${new Date().toISOString().split('T')[0]}.json`;
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Returnează JSON formatat
    res.json(exportData);

    logger.info(`JSON export generat pentru utilizatorul ${userId}`, {
      userId,
      expenseCount: expenses.length,
      total: total.toFixed(2),
      filters: { startDate, endDate, categoryId }
    });

  } catch (error) {
    logger.error('Eroare la exportul JSON:', (error as Error));
    throw new AppError('Eroare la generarea exportului JSON', 500);
  }
};

/**
 * Obține statistici pentru export
 */
const getExportStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req;
    const { startDate, endDate } = req.query;

    // Convert query parameters to proper types
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;

    // Construiește filtrul pentru cheltuieli
    const where: Prisma.ExpenseWhereInput = {
      userId: String(userId),
      ...(startDateStr && endDateStr && {
        date: {
          gte: new Date(startDateStr),
          lte: new Date(endDateStr)
        }
      })
    };

    // Obține statistici
    const [totalExpenses, totalAmount, categoriesStats] = await Promise.all([
      // Total cheltuieli
      prisma.expense.count({ where }),

      // Suma totală
      prisma.expense.aggregate({
        where,
        _sum: { amount: true }
      }),

      // Statistici pe categorii
      prisma.expense.groupBy({
        by: ['categoryId'],
        where,
        _count: { id: true },
        _sum: { amount: true },
        orderBy: { _sum: { amount: 'desc' } }
      })
    ]);

    // Obține detaliile categoriilor
    const categoryIds = categoriesStats.map(stat => stat.categoryId).filter(Boolean);
    const categories = await prisma.category.findMany({
      where: { id: { in: categoryIds } },
      select: { id: true, name: true, color: true, icon: true }
    });

    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat.id] = cat;
      return acc;
    }, {} as Record<string, typeof categories[0]>);

    // Formatează statisticile
    const formattedStats = categoriesStats.map(stat => ({
      category: stat.categoryId ? categoryMap[stat.categoryId] : { name: 'Necategorizat' },
      count: stat._count.id,
      total: Number(stat._sum.amount || 0),
      percentage: totalAmount._sum.amount ?
        Number(((Number(stat._sum.amount || 0) / Number(totalAmount._sum.amount)) * 100).toFixed(2)) : 0
    }));

    res.json({
      success: true,
      data: {
        totalExpenses,
        totalAmount: Number(totalAmount._sum.amount || 0),
        period: startDateStr && endDateStr ? {
          start: startDateStr,
          end: endDateStr,
          days: Math.ceil((new Date(endDateStr).getTime() - new Date(startDateStr).getTime()) / (1000 * 60 * 60 * 24))
        } : null,
        categoriesStats: formattedStats,
        averageExpense: totalExpenses > 0 ?
          Number((Number(totalAmount._sum.amount || 0) / totalExpenses).toFixed(2)) : 0
      }
    });

    logger.info(`Statistici export obținute pentru utilizatorul ${userId}`, {
      userId,
      totalExpenses,
      totalAmount: Number(totalAmount._sum.amount || 0)
    });

  } catch (error) {
    logger.error('Eroare la obținerea statisticilor de export:', (error as Error));
    throw new AppError('Eroare la obținerea statisticilor de export', 500);
  }
};

export const exportController = {
  exportCSV,
  exportPDF,
  exportExcel,
  exportJSON,
  getExportStats
};