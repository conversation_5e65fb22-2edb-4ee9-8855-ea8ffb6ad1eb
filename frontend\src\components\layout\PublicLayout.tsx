import { CurrencyDollarIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { useLanguage } from '../../hooks/useLanguage';
import Button from '../ui/Button';
import { Select } from '../ui/Dropdown';

interface PublicLayoutProps {
  children: React.ReactNode;
}

interface LanguageOption {
  value: string;
  label: string;
}

/**
 * Layout pentru paginile publice care include header și footer
 */
const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const { currentLanguage, setLanguage } = useLanguage();

  // Opțiuni pentru selectorul de limbă
  const languageOptions: LanguageOption[] = [
    { value: 'ro', label: 'Română' },
    { value: 'en', label: 'English' },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                FinanceFlow
              </span>
            </Link>
            <nav className="hidden md:flex space-x-8">
              <Link to="/#features" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('landing.header.features')}
              </Link>
              <Link
                to="/#testimonials"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('landing.header.testimonials')}
              </Link>
              <Link to="/#pricing" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('landing.header.pricing')}
              </Link>
            </nav>
            <div className="flex items-center space-x-4">
              <Select
                value={languageOptions.find(opt => opt.value === currentLanguage)}
                onChange={option => setLanguage((option as any).value)}
                options={languageOptions as any}
                className="w-32"
                buttonClassName="bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md"
                optionClassName="hover:bg-blue-50 font-medium"
                menuClassName="border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg"
                offset={0}
              />
              <Link to="/login" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('landing.header.login')}
              </Link>
              <Link to="/register">
                <Button variant="primary" size="sm">
                  {t('landing.header.register')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">{children}</main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">FinanceFlow</span>
              </div>
              <p className="text-gray-400">{t('landing.footer.description')}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.product.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/features" className="hover:text-white transition-colors">
                    {t('landing.footer.product.features')}
                  </Link>
                </li>
                <li>
                  <Link to="/pricing" className="hover:text-white transition-colors">
                    {t('landing.footer.product.pricing')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.product.api')}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.product.integrations')}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.support.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/documentation" className="hover:text-white transition-colors">
                    {t('landing.footer.support.documentation')}
                  </Link>
                </li>
                <li>
                  <Link to="/help" className="hover:text-white transition-colors">
                    {t('landing.footer.support.guides')}
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="hover:text-white transition-colors">
                    {t('landing.footer.support.contact')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.support.status')}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.legal.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/terms" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.terms')}
                  </Link>
                </li>
                <li>
                  <Link to="/privacy" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.privacy')}
                  </Link>
                </li>
                <li>
                  <Link to="/cookies" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.cookies')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.gdpr')}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>{t('landing.footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;
