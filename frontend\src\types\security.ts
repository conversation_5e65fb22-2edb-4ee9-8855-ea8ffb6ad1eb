/**
 * Tipuri de securitate pentru frontend - înlocuirea tipurilor `any`
 */

// Tipuri pentru API responses
export interface ApiResponse<T = unknown> {
  data?: T;
  message?: string;
  success: boolean;
  error?: string;
  errors?: ValidationError[];
}

export interface PaginatedApiResponse<T = unknown> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Tipuri pentru validare
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  data?: Record<string, unknown>;
}

// Tipuri pentru evenimente DOM
export interface SafeEvent {
  preventDefault: () => void;
  stopPropagation: () => void;
  target: {
    value?: string;
    checked?: boolean;
    name?: string;
    [key: string]: unknown;
  } | null;
  currentTarget: {
    value?: string;
    checked?: boolean;
    name?: string;
    [key: string]: unknown;
  } | null;
}

export interface SafeFormEvent extends SafeEvent {
  target: HTMLFormElement | null;
  currentTarget: HTMLFormElement | null;
}

export interface SafeInputEvent extends Omit<SafeEvent, 'target' | 'currentTarget'> {
  target: HTMLInputElement | null;
  currentTarget: HTMLInputElement | null;
}

export interface SafeSelectEvent extends Omit<SafeEvent, 'target' | 'currentTarget'> {
  target: HTMLSelectElement | null;
  currentTarget: HTMLSelectElement | null;
}

// Tipuri pentru componente React
export interface ComponentProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  [key: string]: unknown;
}

export interface ModalProps extends ComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface TableProps<T = Record<string, unknown>> extends ComponentProps {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: PaginationProps;
  onRowClick?: (row: T) => void;
}

export interface TableColumn<T = Record<string, unknown>> {
  key: keyof T;
  title: string;
  render?: (value: unknown, row: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

export interface PaginationProps {
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
}

// Tipuri pentru store/state management
export interface StoreState {
  loading: boolean;
  error: string | null;
  data: Record<string, unknown>;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  role: 'user' | 'admin';
  subscription?: {
    plan: string;
    status: string;
    expiresAt?: string;
  };
  preferences?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

// Tipuri pentru hook-uri
export interface UseApiResult<T = unknown> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseFormResult<T = Record<string, unknown>> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  handleChange: (event: SafeInputEvent | SafeSelectEvent) => void;
  handleBlur: (event: SafeEvent) => void;
  handleSubmit: (event: SafeFormEvent) => void;
  setFieldValue: (field: keyof T, value: unknown) => void;
  setFieldError: (field: keyof T, error: string) => void;
  resetForm: () => void;
}

export interface UsePaginationResult {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  goToPage: (page: number) => void;
  nextPage: () => void;
  prevPage: () => void;
  setLimit: (limit: number) => void;
}

// Tipuri pentru servicii API
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  withCredentials?: boolean;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: Record<string, unknown>;
  params?: Record<string, string | number>;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: ValidationError[];
}

// Tipuri pentru expense management
export interface Expense {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  userId: string;
  tags?: string[];
  receipt?: {
    filename: string;
    url: string;
    size: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ExpenseFilters {
  category?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  tags?: string[];
  search?: string;
}

export interface ExpenseStats {
  totalAmount: number;
  totalCount: number;
  averageAmount: number;
  categoryBreakdown: Array<{
    category: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
}

// Tipuri pentru subscription management
export interface Subscription {
  id: string;
  userId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'canceled' | 'pastDue' | 'trialing';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    expenses: number;
    categories: number;
    exports: number;
  };
}

// Tipuri pentru charts și rapoarte
export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }>;
}

export interface ChartOptions {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: Record<string, unknown>;
  scales?: Record<string, unknown>;
}

export interface ReportData {
  period: {
    start: string;
    end: string;
  };
  summary: {
    totalExpenses: number;
    totalAmount: number;
    averageAmount: number;
    topCategory: string;
  };
  categoryBreakdown: Array<{
    category: string;
    amount: number;
    count: number;
    percentage: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
  topExpenses: Expense[];
}

// Tipuri pentru notificări
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
  createdAt: string;
}

// Tipuri pentru configurări
export interface AppConfig {
  api: {
    baseURL: string;
    timeout: number;
  };
  features: {
    darkMode: boolean;
    notifications: boolean;
    analytics: boolean;
  };
  limits: {
    fileUploadSize: number;
    requestTimeout: number;
  };
}

export interface UserPreferences {
  language: 'en' | 'ro';
  theme: 'light' | 'dark' | 'auto';
  currency: string;
  dateFormat: string;
  notifications: {
    email: boolean;
    push: boolean;
    categories: string[];
  };
  dashboard: {
    defaultView: 'list' | 'grid' | 'chart';
    itemsPerPage: number;
    autoRefresh: boolean;
  };
}

// Type guards pentru validare runtime
export function isValidApiResponse<T>(obj: unknown): obj is ApiResponse<T> {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'success' in obj &&
    typeof (obj as ApiResponse).success === 'boolean'
  );
}

export function isValidUser(obj: unknown): obj is User {
  return typeof obj === 'object' && obj !== null && 'id' in obj && 'email' in obj && 'role' in obj;
}

export function isValidExpense(obj: unknown): obj is Expense {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'amount' in obj &&
    'description' in obj &&
    'category' in obj
  );
}

export function isValidEvent(obj: unknown): obj is SafeEvent {
  return (
    typeof obj === 'object' && obj !== null && 'preventDefault' in obj && 'stopPropagation' in obj
  );
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export type SortOrder = 'asc' | 'desc';

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';
