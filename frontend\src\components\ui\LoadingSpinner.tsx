import React from 'react';

import { cn } from '../../utils/helpers';

type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
type SpinnerColor =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'white'
  | 'gray'
  | 'currentColor';
type SpinnerType = 'circular' | 'dots' | 'pulse' | 'bars';

interface SpinnerProps {
  size: SpinnerSize;
  color: SpinnerColor;
  className?: string;
}

interface LoadingSpinnerProps {
  size?: SpinnerSize;
  color?: SpinnerColor;
  type?: SpinnerType;
  className?: string;
  label?: string;
  inline?: boolean;
  overlay?: boolean;
  overlayClassName?: string;
}

// Variante de dimensiuni pentru spinner
const sizeVariants = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
  '2xl': 'w-16 h-16',
};

// Variante de culori pentru spinner
const colorVariants = {
  primary: 'text-primary-600',
  secondary: 'text-secondary-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  danger: 'text-red-600',
  white: 'text-white',
  gray: 'text-gray-600',
  currentColor: 'text-current',
};

// Tipuri de spinner
const SpinnerTypes = {
  CIRCULAR: 'circular',
  DOTS: 'dots',
  PULSE: 'pulse',
  BARS: 'bars',
};

// Componenta Spinner circular
const CircularSpinner: React.FC<SpinnerProps> = ({ size, color, className }) => (
  <svg
    className={cn('animate-spin', sizeVariants[size], colorVariants[color], className)}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
    <path
      className="opacity-75"
      fill="currentColor"
      d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

// Componenta Spinner cu puncte
const DotsSpinner: React.FC<SpinnerProps> = ({ size, color, className }) => {
  const dotSize = {
    xs: 'w-1 h-1',
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-2.5 h-2.5',
    xl: 'w-3 h-3',
    '2xl': 'w-4 h-4',
  };

  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map(index => (
        <div
          key={index}
          className={cn(
            'rounded-full animate-pulse',
            dotSize[size],
            colorVariants[color].replace('text-', 'bg-'),
          )}
          style={{
            animationDelay: `${index * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

// Componenta Spinner cu puls
const PulseSpinner: React.FC<SpinnerProps> = ({ size, color, className }) => (
  <div
    className={cn(
      'rounded-full animate-pulse',
      sizeVariants[size],
      colorVariants[color].replace('text-', 'bg-'),
      className,
    )}
    style={{
      animationDuration: '1.5s',
    }}
  />
);

// Componenta Spinner cu bare
const BarsSpinner: React.FC<SpinnerProps> = ({ size, color, className }) => {
  const barHeight = {
    xs: 'h-2',
    sm: 'h-3',
    md: 'h-4',
    lg: 'h-6',
    xl: 'h-8',
    '2xl': 'h-10',
  };

  const barWidth = {
    xs: 'w-0.5',
    sm: 'w-0.5',
    md: 'w-1',
    lg: 'w-1',
    xl: 'w-1.5',
    '2xl': 'w-2',
  };

  return (
    <div className={cn('flex items-end space-x-0.5', className)}>
      {[0, 1, 2, 3, 4].map(index => (
        <div
          key={index}
          className={cn(
            'animate-pulse',
            barHeight[size],
            barWidth[size],
            colorVariants[color].replace('text-', 'bg-'),
          )}
          style={{
            animationDelay: `${index * 0.1}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

// Componenta principală LoadingSpinner
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  type = 'circular',
  className,
  label,
  inline = false,
  overlay = false,
  overlayClassName,
}) => {
  // Selectează componenta de spinner în funcție de tip
  const SpinnerComponent = {
    circular: CircularSpinner,
    dots: DotsSpinner,
    pulse: PulseSpinner,
    bars: BarsSpinner,
  }[type];

  const spinner = <SpinnerComponent size={size} color={color} className={className || ''} />;

  // Dacă este inline, returnează doar spinner-ul
  if (inline) {
    return label ? (
      <div className="flex items-center space-x-2">
        {spinner}
        {label && <span className={cn('text-sm', colorVariants[color])}>{label}</span>}
      </div>
    ) : (
      spinner
    );
  }

  // Dacă este overlay, afișează peste conținut
  if (overlay) {
    return (
      <div
        className={cn(
          'absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-50',
          overlayClassName,
        )}
      >
        <div className="flex flex-col items-center space-y-2">
          {spinner}
          {label && (
            <span className={cn('text-sm font-medium', colorVariants[color])}>{label}</span>
          )}
        </div>
      </div>
    );
  }

  // Afișare normală cu centrat
  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {spinner}
      {label && <span className={cn('text-sm font-medium', colorVariants[color])}>{label}</span>}
    </div>
  );
};

interface PageLoaderProps {
  label?: string;
}

interface ButtonSpinnerProps {
  size?: SpinnerSize;
  color?: SpinnerColor;
}

interface CardLoaderProps {
  label?: string;
}

interface OverlayLoaderProps {
  label?: string;
  className?: string;
}

interface InlineLoaderProps {
  size?: SpinnerSize;
  label?: string;
  className?: string;
}

// Componente specializate pentru cazuri comune
export const PageLoader: React.FC<PageLoaderProps> = ({ label = 'Se încarcă...' }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <LoadingSpinner size="lg" label={label} />
  </div>
);

export const ButtonSpinner: React.FC<ButtonSpinnerProps> = ({ size = 'sm', color = 'white' }) => (
  <LoadingSpinner size={size} color={color} inline />
);

export const CardLoader: React.FC<CardLoaderProps> = ({ label }) => (
  <div className="flex items-center justify-center p-8">
    <LoadingSpinner size="md" label={label || ''} />
  </div>
);

export const OverlayLoader: React.FC<OverlayLoaderProps> = ({
  label = 'Se încarcă...',
  className,
}) => <LoadingSpinner overlay label={label} overlayClassName={className || ''} />;

export const InlineLoader: React.FC<InlineLoaderProps> = ({ size = 'sm', label, className }) => (
  <LoadingSpinner size={size} label={label || ''} inline className={className || ''} />
);

// Export tipurile pentru utilizare externă
export type { SpinnerSize, SpinnerColor, SpinnerType };

export default LoadingSpinner;
