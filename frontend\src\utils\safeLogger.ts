/**
 * Sistem de logging sigur pentru frontend - înlocuirea console.log
 */

// Niveluri de logging
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

// Configurația logger-ului pentru frontend
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  enableLocalStorage: boolean;
  maxLocalStorageEntries: number;
  remoteEndpoint?: string;
  enableProduction: boolean;
}

// Configurația implicită
const defaultConfig: LoggerConfig = {
  level: process.env['NODE_ENV'] === 'production' ? LogLevel.WARN : LogLevel.DEBUG,
  enableConsole: process.env['NODE_ENV'] !== 'production',
  enableRemote: process.env['NODE_ENV'] === 'production',
  enableLocalStorage: true,
  maxLocalStorageEntries: 100,
  remoteEndpoint: '/api/logs',
  enableProduction: false,
};

// Interfața pentru log entry
interface LogEntry {
  id: string;
  timestamp: string;
  level: string;
  message: string;
  url?: string;
  userAgent?: string;
  userId?: string | undefined;
  sessionId?: string | undefined;
  metadata?: Record<string, unknown> | undefined;
  stack?: string | undefined;
  errorName?: string;
  errorMessage?: string;
}

// Context pentru logging
export interface LogContext {
  userId?: string | undefined;
  sessionId?: string;
  component?: string;
  action?: string;
  page?: string;
  feature?: string;
}

// Clasa principală de logging pentru frontend
class SafeLogger {
  private config: LoggerConfig;
  private isProduction: boolean;
  private sessionId: string;
  private logQueue: LogEntry[] = [];
  private isOnline: boolean = navigator.onLine;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.isProduction = process.env['NODE_ENV'] === 'production';
    this.sessionId = this.generateSessionId();

    // Ascultă pentru schimbări în starea de conectivitate
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushQueuedLogs();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Flush logs înainte de închiderea paginii
    window.addEventListener('beforeunload', () => {
      this.flushQueuedLogs();
    });
  }

  /**
   * Generează un ID unic pentru sesiune
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Sanitizează datele sensibile din log-uri
   */
  private sanitizeData(data: unknown): unknown {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sensitiveKeys = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'cookie',
      'session',
      'credit_card',
      'ssn',
      'email',
      'phone',
      'address',
      'personal',
    ];

    const sanitized = { ...(data as Record<string, unknown>) };

    for (const key of Object.keys(sanitized)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Formatează mesajul de log
   */
  private formatMessage(
    level: LogLevel,
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): LogEntry {
    return {
      id: this.generateSessionId(),
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: context?.userId || undefined,
      sessionId: this.sessionId,
      metadata: metadata ? (this.sanitizeData(metadata) as Record<string, unknown>) : undefined,
    };
  }

  /**
   * Scrie log-ul în consolă (doar în development)
   */
  private writeToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole || this.isProduction) {
      return;
    }

    const styles = {
      ERROR: 'color: #ff4444; font-weight: bold;',
      WARN: 'color: #ffaa00; font-weight: bold;',
      INFO: 'color: #0088cc; font-weight: bold;',
      DEBUG: 'color: #888888;',
      TRACE: 'color: #cccccc;',
    };

    const style = styles[entry.level as keyof typeof styles] || '';

    // eslint-disable-next-line no-console
    console.log(
      `%c[${entry.timestamp}] ${entry.level}: ${entry.message}`,
      style,
      entry.metadata ? entry.metadata : '',
    );
  }

  /**
   * Salvează log-ul în localStorage
   */
  private saveToLocalStorage(entry: LogEntry): void {
    if (!this.config.enableLocalStorage) {
      return;
    }

    try {
      const storageKey = 'app_logs';
      const existingLogs = JSON.parse(localStorage.getItem(storageKey) || '[]');

      existingLogs.push(entry);

      // Păstrează doar ultimele N intrări
      if (existingLogs.length > this.config.maxLocalStorageEntries) {
        existingLogs.splice(0, existingLogs.length - this.config.maxLocalStorageEntries);
      }

      localStorage.setItem(storageKey, JSON.stringify(existingLogs));
    } catch (error) {
      // Ignoră erorile de localStorage (quota exceeded, etc.)
      if (!this.isProduction) {
        // eslint-disable-next-line no-console
        console.warn('Failed to save log to localStorage:', error);
      }
    }
  }

  /**
   * Trimite log-ul la server (în producție)
   */
  private async sendToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.enableRemote || !this.config.remoteEndpoint) {
      return;
    }

    try {
      if (!this.isOnline) {
        this.logQueue.push(entry);
        return;
      }

      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      // Adaugă în coadă pentru retrimitere
      this.logQueue.push(entry);

      if (!this.isProduction) {
        // eslint-disable-next-line no-console
        console.warn('Failed to send log to remote:', error);
      }
    }
  }

  /**
   * Trimite log-urile din coadă
   */
  private async flushQueuedLogs(): Promise<void> {
    if (this.logQueue.length === 0 || !this.isOnline) {
      return;
    }

    const logsToSend = [...this.logQueue];
    this.logQueue = [];

    for (const log of logsToSend) {
      await this.sendToRemote(log);
    }
  }

  /**
   * Metodă generică de logging
   */
  private async log(
    level: LogLevel,
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    if (level > this.config.level) {
      return;
    }

    const entry = this.formatMessage(level, message, context, metadata);

    this.writeToConsole(entry);
    this.saveToLocalStorage(entry);
    await this.sendToRemote(entry);
  }

  /**
   * Log de eroare
   */
  async error(
    message: string,
    error?: Error,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    const entry = this.formatMessage(LogLevel.ERROR, message, context, metadata);

    if (error) {
      entry.stack = error.stack || undefined;
      entry.errorName = error.name;
      entry.errorMessage = error.message;
    }

    this.writeToConsole(entry);
    this.saveToLocalStorage(entry);
    await this.sendToRemote(entry);
  }

  /**
   * Log de avertisment
   */
  async warn(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    await this.log(LogLevel.WARN, message, context, metadata);
  }

  /**
   * Log informativ
   */
  async info(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    await this.log(LogLevel.INFO, message, context, metadata);
  }

  /**
   * Log de debug
   */
  async debug(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    await this.log(LogLevel.DEBUG, message, context, metadata);
  }

  /**
   * Log de trace
   */
  async trace(
    message: string,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    await this.log(LogLevel.TRACE, message, context, metadata);
  }

  /**
   * Log pentru interacțiuni utilizator
   */
  async userAction(
    action: string,
    component: string,
    userId?: string | undefined,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    const context: LogContext = {
      userId: userId || undefined,
      component,
      action,
      page: window.location.pathname,
    };

    await this.log(LogLevel.INFO, `User action: ${action} in ${component}`, context, metadata);
  }

  /**
   * Log pentru performanță
   */
  async performance(
    operation: string,
    duration: number,
    context?: LogContext,
    metadata?: Record<string, unknown>,
  ): Promise<void> {
    const message = `Performance: ${operation} completed in ${duration}ms`;

    await this.log(LogLevel.INFO, message, context, {
      ...metadata,
      duration,
      operation,
      performanceNow: performance.now(),
    });
  }

  /**
   * Log pentru API calls
   */
  async apiCall(
    method: string,
    url: string,
    status: number,
    duration: number,
    context?: LogContext,
  ): Promise<void> {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    const message = `API ${method} ${url} - ${status} (${duration}ms)`;

    await this.log(level, message, context, {
      method,
      url,
      status,
      duration,
      type: 'api_call',
    });
  }

  /**
   * Obține log-urile din localStorage
   */
  getLocalLogs(): LogEntry[] {
    try {
      const storageKey = 'app_logs';
      return JSON.parse(localStorage.getItem(storageKey) || '[]');
    } catch {
      return [];
    }
  }

  /**
   * Șterge log-urile din localStorage
   */
  clearLocalLogs(): void {
    try {
      localStorage.removeItem('app_logs');
    } catch (error) {
      if (!this.isProduction) {
        // eslint-disable-next-line no-console
        console.warn('Failed to clear local logs:', error);
      }
    }
  }

  /**
   * Creează un logger cu context predefinit
   */
  createContextLogger(defaultContext: LogContext): SafeLogger {
    const contextLogger = new SafeLogger(this.config);

    // Override metodele pentru a include contextul implicit
    const originalLog = contextLogger.log.bind(contextLogger);
    contextLogger.log = async (
      level: LogLevel,
      message: string,
      context?: LogContext,
      metadata?: Record<string, unknown>,
    ) => {
      const mergedContext = { ...defaultContext, ...context };
      return originalLog(level, message, mergedContext, metadata);
    };

    return contextLogger;
  }
}

// Instanța globală
export const logger = new SafeLogger();

// Export pentru utilizare în alte module
export { SafeLogger };

// Funcții de conveniență pentru înlocuirea console.log
export const safeLog = {
  error: (message: string, error?: Error, context?: LogContext) =>
    logger.error(message, error, context),

  warn: (message: string, context?: LogContext) => logger.warn(message, context),

  info: (message: string, context?: LogContext) => logger.info(message, context),

  debug: (message: string, context?: LogContext) => logger.debug(message, context),

  trace: (message: string, context?: LogContext) => logger.trace(message, context),

  userAction: (action: string, component: string, userId?: string | undefined) =>
    logger.userAction(action, component, userId),

  performance: (operation: string, duration: number) => logger.performance(operation, duration),

  apiCall: (method: string, url: string, status: number, duration: number) =>
    logger.apiCall(method, url, status, duration),
};

// Hook pentru React components
export function useLogger(componentName: string, userId?: string | undefined) {
  const contextLogger = logger.createContextLogger({
    component: componentName,
    userId: userId || undefined,
    page: window.location.pathname,
  });

  return {
    logError: (message: string, error?: Error, metadata?: Record<string, unknown>) =>
      contextLogger.error(message, error, undefined, metadata),

    logWarn: (message: string, metadata?: Record<string, unknown>) =>
      contextLogger.warn(message, undefined, metadata),

    logInfo: (message: string, metadata?: Record<string, unknown>) =>
      contextLogger.info(message, undefined, metadata),

    logDebug: (message: string, metadata?: Record<string, unknown>) =>
      contextLogger.debug(message, undefined, metadata),

    logUserAction: (action: string, metadata?: Record<string, unknown>) =>
      contextLogger.userAction(action, componentName, userId, metadata),

    logPerformance: (operation: string, duration: number, metadata?: Record<string, unknown>) =>
      contextLogger.performance(operation, duration, undefined, metadata),
  };
}

// Interceptor pentru erori globale
window.addEventListener('error', event => {
  logger.error(
    'Global error caught',
    new Error(event.message),
    {
      page: window.location.pathname,
    },
    {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      type: 'global_error',
    },
  );
});

// Interceptor pentru promise rejections
window.addEventListener('unhandledrejection', event => {
  logger.error(
    'Unhandled promise rejection',
    event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
    {
      page: window.location.pathname,
    },
    {
      type: 'unhandled_rejection',
    },
  );
});

// Override console methods în development pentru a folosi logger-ul nostru
if (process.env['NODE_ENV'] !== 'production') {
  // eslint-disable-next-line no-console
  console.log = (...args: unknown[]) => {
    logger.debug(
      args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' '),
    );
  };

  // eslint-disable-next-line no-console
  console.error = (...args: unknown[]) => {
    logger.error(
      args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' '),
    );
  };

  // eslint-disable-next-line no-console
  console.warn = (...args: unknown[]) => {
    logger.warn(
      args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' '),
    );
  };

  // eslint-disable-next-line no-console
  console.info = (...args: unknown[]) => {
    logger.info(
      args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' '),
    );
  };
}
