{"compilerOptions": {"target": "ES2023", "useDefineForClassFields": true, "lib": ["ES2023", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "verbatimModuleSyntax": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noImplicitThis": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "exactOptionalPropertyTypes": true, "noUnusedLocals": true, "noUnusedParameters": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "incremental": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/hooks/*": ["src/hooks/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/store/*": ["src/store/*"], "@/config/*": ["src/config/*"], "@/assets/*": ["src/assets/*"]}, "types": ["vitest/globals", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "dist", "build", "**/*.js", "**/*.jsx"]}