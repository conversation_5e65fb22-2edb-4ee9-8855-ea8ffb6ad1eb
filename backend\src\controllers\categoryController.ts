import { Response } from 'express';
import { prisma } from '../config/prisma';
import { AuthenticatedRequest, CreateCategoryDto } from '../types';
import { CaseConverter } from '../utils/caseConverter';
import { safeLog } from '../utils/safeLogger';

// Get all categories for the authenticated user
const getCategories = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {userId} = req;
    const { includeInactive = false } = req.query;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const categories = await prisma.category.findMany({
      where: {
        userId: String(userId),
        ...(includeInactive !== 'true' && { isActive: true }),
      },
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' },
      ],
    });

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseCategories = CaseConverter.toCamel(categories);

    res.json({
      success: true,
      data: {
        categories: camelCaseCategories,
      },
    });
  } catch (error) {
    safeLog.error('Get categories error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching categories',
    });
  }
};

// Get a single category by ID
const getCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {userId} = req;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const category = await prisma.category.findFirst({
      where: {
        id: id as string,
        userId: String(userId),
      },
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Category not found',
      });
      return;
    }

    res.json({
      success: true,
      data: {
        category,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Get category error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching category',
    });
  }
};

// Create a new category
const createCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {userId} = req;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const { name, description, color, icon, sortOrder, budgetLimit, budgetPeriod, isDefault } = req.body as CreateCategoryDto;

    const categoryData = {
      name,
      description,
      color,
      icon,
      sortOrder: sortOrder || 0,
      budgetLimit,
      budgetPeriod: budgetPeriod || 'monthly',
      isDefault: isDefault || false,
      isActive: true,
      userId: String(userId),
    };

    // Check if category name already exists for this user
    const existingCategory = await prisma.category.findFirst({
      where: {
        userId: String(userId),
        name: categoryData.name,
      },
    });

    if (existingCategory) {
      res.status(409).json({
        success: false,
        message: 'A category with this name already exists',
      });
      return;
    }

    const category = await prisma.category.create({
      data: categoryData,
    });

    // Convertește răspunsul la camelCase pentru frontend
    const camelCaseCategory = CaseConverter.toCamel(category);

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: {
        category: camelCaseCategory,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Create category error:', error as Error);

    if ((error as any).code === 'P2002') {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: [{ field: 'name', message: 'Category name must be unique' }],
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while creating category',
    });
  }
};

// Update a category
const updateCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {userId} = req;
    const updateData = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const category = await prisma.category.findFirst({
      where: {
        id: id as string,
        userId: String(userId),
      },
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Category not found',
      });
      return;
    }

    // Check if new name conflicts with existing category
    if (updateData.name && updateData.name !== category.name) {
      const existingCategory = await prisma.category.findFirst({
        where: {
          userId: String(userId),
          name: updateData.name,
          NOT: {
            id: category.id,
          },
        },
      });

      if (existingCategory) {
        res.status(409).json({
          success: false,
          message: 'A category with this name already exists',
        });
        return;
      }
    }

    const updatedCategory = await prisma.category.update({
      where: { id: category.id },
      data: updateData,
    });

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: {
        category: updatedCategory,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Update category error:', error as Error);

    if ((error as any).code === 'P2002') {
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: [{ field: 'name', message: 'Category name must be unique' }],
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while updating category',
    });
  }
};

// Delete a category
const deleteCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {userId} = req;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const category = await prisma.category.findFirst({
      where: {
        id: id as string,
        userId: String(userId),
      },
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Category not found',
      });
      return;
    }

    // Check if category has expenses
    const expenseCount = await prisma.expense.count({
      where: {
        categoryId: id as string,
      },
    });

    if (expenseCount > 0) {
      res.status(400).json({
        success: false,
        message: `Cannot delete category. It has ${expenseCount} associated expenses. Please move or delete the expenses first.`,
        data: {
          expenseCount: expenseCount,
        },
      });
      return;
    }

    await prisma.category.delete({
      where: { id: category.id },
    });

    res.json({
      success: true,
      message: 'Category deleted successfully',
    });
  } catch (error: unknown) {
    safeLog.error('Delete category error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while deleting category',
    });
  }
};

// Get category statistics
const getCategoryStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {userId} = req;
    const { period = 'monthly', start_date, end_date } = req.query;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const category = await prisma.category.findFirst({
      where: {
        id: id as string,
        userId: String(userId),
      },
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Category not found',
      });
      return;
    }

    let startDate, endDate;

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam as string);
      endDate = new Date(endDateParam as string);
    } else {
      // Calculate period dates
      const now = new Date();
      switch (period) {
        case 'daily':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
          break;
        case 'yearly':
          startDate = new Date(now.getFullYear(), 0, 1);
          endDate = new Date(now.getFullYear() + 1, 0, 1);
          break;
        default: // monthly
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      }
    }

    // Get total expenses and count
    const expenseStats = await prisma.expense.aggregate({
      where: {
        categoryId: id as string,
        date: {
          gte: startDate,
          lt: endDate,
        },
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    const totalExpenses = Number(expenseStats._sum.amount || 0);
    const expenseCount = expenseStats._count.id || 0;

    // Calculate budget status
    const budgetUsed = totalExpenses;
    const budgetLimit = Number(category.budgetLimit || 0);
    const budgetRemaining = budgetLimit - budgetUsed;
    const budgetPercentage = budgetLimit > 0 ? (budgetUsed / budgetLimit) * 100 : 0;

    const budgetStatus = {
      limit: budgetLimit,
      used: budgetUsed,
      remaining: budgetRemaining,
      percentage: budgetPercentage,
      isOverBudget: budgetUsed > budgetLimit && budgetLimit > 0,
    };

    // Get recent expenses
    const recentExpenses = await prisma.expense.findMany({
      where: {
        categoryId: id as string,
        date: {
          gte: startDate,
          lt: endDate,
        },
      },
      orderBy: {
        date: 'desc',
      },
      take: 5,
      select: {
        id: true,
        amount: true,
        description: true,
        date: true,
      },
    });

    res.json({
      success: true,
      data: {
        category: {
          id: category.id,
          name: category.name,
          color: category.color,
          icon: category.icon,
        },
        period: {
          type: period,
          startDate: startDate,
          endDate: endDate,
        },
        statistics: {
          totalExpenses: totalExpenses,
          expenseCount: expenseCount,
          averageExpense: expenseCount > 0 ? totalExpenses / expenseCount : 0,
        },
        budget: budgetStatus,
        recentExpenses: recentExpenses,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Get category stats error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching category statistics',
    });
  }
};

// Get categories with expense summaries
const getCategoriesWithStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {userId} = req;
    const { startDate: startDateParam, endDate: endDateParam, period = 'monthly' } = req.query;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    let startDate, endDate;

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam as string);
      endDate = new Date(endDateParam as string);
    } else {
      const now = new Date();
      switch (period) {
        case 'daily':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
          break;
        case 'yearly':
          startDate = new Date(now.getFullYear(), 0, 1);
          endDate = new Date(now.getFullYear() + 1, 0, 1);
          break;
        default: // monthly
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      }
    }

    const categories = await prisma.category.findMany({
      where: {
        userId: String(userId),
        isActive: true,
      },
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' },
      ],
    });

    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        // Get expense statistics for this category
        const expenseStats = await prisma.expense.aggregate({
          where: {
            categoryId: category.id,
            date: {
              gte: startDate,
              lt: endDate,
            },
          },
          _sum: {
            amount: true,
          },
          _count: {
            id: true,
          },
        });

        const totalExpenses = Number(expenseStats._sum.amount || 0);
        const expenseCount = expenseStats._count.id || 0;

        // Calculate budget status
        const budgetUsed = totalExpenses;
        const budgetLimit = Number(category.budgetLimit || 0);
        const budgetRemaining = budgetLimit - budgetUsed;
        const budgetPercentage = budgetLimit > 0 ? (budgetUsed / budgetLimit) * 100 : 0;

        const budgetStatus = {
          limit: budgetLimit,
          used: budgetUsed,
          remaining: budgetRemaining,
          percentage: budgetPercentage,
          isOverBudget: budgetUsed > budgetLimit && budgetLimit > 0,
        };

        return {
          id: category.id,
          name: category.name,
          description: category.description,
          color: category.color,
          icon: category.icon,
          budgetLimit: category.budgetLimit,
          budgetPeriod: category.budgetPeriod,
          isDefault: category.isDefault,
          sortOrder: category.sortOrder,
          statistics: {
            totalExpenses: totalExpenses,
            expenseCount: expenseCount,
            averageExpense: expenseCount > 0 ? totalExpenses / expenseCount : 0,
          },
          budget: budgetStatus,
        };
      }),
    );

    res.json({
      success: true,
      data: {
        period: {
          type: period,
          startDate: startDate,
          endDate: endDate,
        },
        categories: categoriesWithStats,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Get categories with stats error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching categories with statistics',
    });
  }
};

// Reorder categories
const reorderCategories = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {userId} = req;
    const { categories } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    if (!Array.isArray(categories)) {
      res.status(400).json({
        success: false,
        message: 'Categories must be an array',
      });
      return;
    }

    // Validate that all categories belong to the user
    const categoryIds = categories.map(cat => cat.id as string);
    const userCategories = await prisma.category.findMany({
      where: {
        id: { in: categoryIds },
        userId: String(userId),
      },
    });

    if (userCategories.length !== categoryIds.length) {
      res.status(400).json({
        success: false,
        message: 'Some categories do not belong to you or do not exist',
      });
      return;
    }

    // Reorder categories
    await Promise.all(
      categories.map(async (cat, index) => {
        await prisma.category.update({
          where: { id: cat.id as string },
          data: { sortOrder: index + 1 },
        });
      }),
    );

    res.json({
      success: true,
      message: 'Categories reordered successfully',
    });
  } catch (error: unknown) {
    safeLog.error('Reorder categories error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while reordering categories',
    });
  }
};

// Set default category
const setDefaultCategory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {userId} = req;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    const category = await prisma.category.findFirst({
      where: {
        id: id as string,
        userId: String(userId),
        isActive: true,
      },
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Category not found or inactive',
      });
      return;
    }

    // Clear other default categories for this user
    await prisma.category.updateMany({
      where: {
        userId: String(userId),
        isDefault: true,
      },
      data: {
        isDefault: false,
      },
    });

    // Set this category as default
    const updatedCategory = await prisma.category.update({
      where: { id: category.id },
      data: { isDefault: true },
    });

    res.json({
      success: true,
      message: 'Default category updated successfully',
      data: {
        category: updatedCategory,
      },
    });
  } catch (error: unknown) {
    safeLog.error('Set default category error:', error as Error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while setting default category',
    });
  }
};

export {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats,
  getCategoriesWithStats,
  reorderCategories,
  setDefaultCategory,
};
