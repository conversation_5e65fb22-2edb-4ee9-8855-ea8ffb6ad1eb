/**
 * Teste pentru middleware-ul de validare actualizat cu suport camelCase
 */

import { vi } from 'vitest';
import { Request, Response } from 'express';
import { validate, validateQuery, userSchemas, categorySchemas, expenseSchemas, paramSchemas } from '../../../src/middleware/validation';

// Mock console.error to avoid noise in tests
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = vi.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

// Mock pentru Express Request și Response
const mockRequest = (body = {}, query = {}, params = {}) => {
  return {
    body,
    query,
    params,
    path: '/test',
    method: 'POST'
  } as Request;
};

const mockResponse = () => {
  const res: any = {};
  res.status = vi.fn().mockReturnValue(res);
  res.json = vi.fn().mockReturnValue(res);
  return res as Response;
};

const mockNext = vi.fn();

describe('Updated Validation Middleware', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('User Schemas', () => {
    describe('register schema', () => {
      it('should validate camelCase registration data', () => {
        const req = mockRequest({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'John',
          lastName: 'Doe',
          currency: 'USD',
          timezone: 'UTC'
        });

        const middleware = validate(userSchemas.register);
        middleware(req, mockResponse(), mockNext);

        expect(mockNext).toHaveBeenCalled();
        expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
      });

      it('should require firstName and lastName', () => {
        const req = mockRequest({
          email: '<EMAIL>',
          password: 'password123'
          // Missing firstName and lastName
        });

        const res = mockResponse();
        const middleware = validate(userSchemas.register);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          success: false,
          message: 'Validation failed',
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'firstName',
              message: expect.stringContaining('required')
            }),
            expect.objectContaining({
              field: 'lastName',
              message: expect.stringContaining('required')
            })
          ])
        }));
      });

      it('should validate email format', () => {
        const req = mockRequest({
          email: 'invalid-email',
          password: 'password123',
          firstName: 'John',
          lastName: 'Doe'
        });

        const res = mockResponse();
        const middleware = validate(userSchemas.register);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'email',
              message: expect.stringContaining('valid email')
            })
          ])
        }));
      });
    });

    describe('updateProfile schema', () => {
      it('should validate profile update with camelCase', () => {
        const req = mockRequest({
          firstName: 'Jane',
          lastName: 'Smith',
          currency: 'EUR',
          timezone: 'Europe/London',
          avatar: 'https://example.com/avatar.jpg'
        });

        const middleware = validate(userSchemas.updateProfile);
        middleware(req, mockResponse(), mockNext);

        expect(mockNext).toHaveBeenCalled();
        expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
      });
    });
  });

  describe('Category Schemas', () => {
    describe('create schema', () => {
      it('should validate camelCase category creation', () => {
        const req = mockRequest({
          name: 'Food',
          description: 'Food and dining expenses',
          color: '#FF0000',
          icon: 'food',
          sortOrder: 1,
          budgetLimit: 500.00,
          budgetPeriod: 'monthly',
          isDefault: false
        });

        const middleware = validate(categorySchemas.create);
        middleware(req, mockResponse(), mockNext);

        expect(mockNext).toHaveBeenCalled();
        expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
      });

      it('should require category name', () => {
        const req = mockRequest({
          color: '#FF0000',
          icon: 'food'
          // Missing name
        });

        const res = mockResponse();
        const middleware = validate(categorySchemas.create);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'name',
              message: expect.stringContaining('required')
            })
          ])
        }));
      });

      it('should validate budget period enum', () => {
        const req = mockRequest({
          name: 'Food',
          budgetPeriod: 'invalid-period'
        });

        const res = mockResponse();
        const middleware = validate(categorySchemas.create);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'budgetPeriod',
              message: expect.stringContaining('daily, weekly, monthly, yearly')
            })
          ])
        }));
      });
    });
  });

  describe('Expense Schemas', () => {
    describe('create schema', () => {
      it('should validate camelCase expense creation', () => {
        const req = mockRequest({
          amount: 25.50,
          description: 'Lunch at restaurant',
          categoryId: 'category-cuid-123',
          date: '2023-01-01T12:00:00.000Z',
          paymentMethod: 'card',
          tags: ['lunch', 'restaurant'],
          notes: 'Great food!',
          receiptUrl: 'https://example.com/receipt.jpg',
          location: 'Downtown Restaurant',
          isRecurring: false,
          recurringFrequency: 'monthly',
          recurringEndDate: '2023-12-31T23:59:59.999Z'
        });

        const middleware = validate(expenseSchemas.create);
        middleware(req, mockResponse(), mockNext);

        expect(mockNext).toHaveBeenCalled();
        expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
      });

      it('should require amount, description, categoryId, date, and paymentMethod', () => {
        const req = mockRequest({
          // Missing required fields
        });

        const res = mockResponse();
        const middleware = validate(expenseSchemas.create);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'amount',
              message: expect.stringContaining('required')
            }),
            expect.objectContaining({
              field: 'description',
              message: expect.stringContaining('required')
            }),
            expect.objectContaining({
              field: 'categoryId',
              message: expect.stringContaining('required')
            }),
            expect.objectContaining({
              field: 'date',
              message: expect.stringContaining('required')
            }),
            expect.objectContaining({
              field: 'paymentMethod',
              message: expect.stringContaining('required')
            })
          ])
        }));
      });

      it('should validate payment method enum', () => {
        const req = mockRequest({
          amount: 25.50,
          description: 'Test expense',
          categoryId: 'category-cuid-123',
          date: '2023-01-01T12:00:00.000Z',
          paymentMethod: 'invalid-method'
        });

        const res = mockResponse();
        const middleware = validate(expenseSchemas.create);
        middleware(req, res, mockNext);

        expect(mockNext).not.toHaveBeenCalled();
        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'paymentMethod',
              message: expect.stringContaining('cash, card, bank_transfer, digital_wallet, other')
            })
          ])
        }));
      });
    });

    describe('query schema', () => {
      it('should validate camelCase query parameters', () => {
        const req = mockRequest({}, {
          page: '1',
          limit: '20',
          categoryId: 'category-cuid-123',
          startDate: '2023-01-01T00:00:00.000Z',
          endDate: '2023-01-31T23:59:59.999Z',
          minAmount: '10',
          maxAmount: '100',
          tags: ['food', 'restaurant'],
          search: 'lunch',
          sortBy: 'date',
          sortOrder: 'desc',
          paymentMethod: 'card',
          isRecurring: 'false'
        });

        const middleware = validateQuery(expenseSchemas.query);
        middleware(req, mockResponse(), mockNext);

        expect(mockNext).toHaveBeenCalled();
        expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
      });
    });
  });

  describe('Parameter Schemas', () => {
    it('should validate CUID format', () => {
      const req = mockRequest({}, {}, {
        id: 'c1234567890123456789012345' // Valid CUID format
      });

      const middleware = validate(paramSchemas.cuid);
      middleware(req, mockResponse(), mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeUndefined(); // No error
    });

    it('should reject invalid CUID format', () => {
      const req = mockRequest({}, {}, {
        id: 'invalid-cuid-format'
      });

      const res = mockResponse();
      const middleware = validate(paramSchemas.cuid);
      middleware(req, res, mockNext);

      expect(mockNext).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'id',
            message: expect.stringContaining('Invalid CUID format')
          })
        ])
      }));
    });
  });
});
