// Jest setup file - separate from main setup to avoid variable conflicts

// Mock environment variables pentru teste
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
process.env['JWT_REFRESH_SECRET'] = 'test-refresh-secret-key-for-testing-only';
process.env['DATABASE_URL'] = '************************************************/expense_tracker_test';
process.env['REDIS_URL'] = 'redis://localhost:6379/1';

// Mock pentru logger - folosim mock-ul din tests/setup.ts
jest.mock('./src/utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    stream: {
      write: jest.fn(),
    },
    logRequest: jest.fn(),
    logPerformance: jest.fn(),
    logAudit: jest.fn(),
    logSecurity: jest.fn(),
    logDatabase: jest.fn(),
    add: jest.fn(),
  },
  __esModule: true,
}));

// Mock pentru Redis - definit în tests/setup.ts

// Mock pentru Stripe - definit în tests/setup.ts

console.log('✅ Jest setup completed');