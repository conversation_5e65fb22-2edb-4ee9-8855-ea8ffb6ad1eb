import {
  UserIcon,
  CreditCardIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowRightIcon,
  ClockIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import Badge from '../../components/ui/Badge';
// import { Button } from '../../components/ui/Button'; // Unused import
import Card from '../../components/ui/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import { useActivityFeed } from '../../hooks/useAdminData';
import { formatDate } from '../../utils/helpers';
import type { ActivityType, Activity, ActivityFeedItem } from '../../types';

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('ro-RO', {
    style: 'currency',
    currency: 'RON',
  }).format(amount);
};
// Tipurile sunt importate din types/index.ts

const ActivityFeed: React.FC = () => {
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [showActivityModal, setShowActivityModal] = useState<boolean>(false);
  const [filter, setFilter] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<string>('24h');

  // Query pentru activitatea recentă
  const {
    data: activities,
    isLoading,
    error,
  } = useActivityFeed({
    type: filter === 'all' ? undefined : filter,
    timeRange,
    limit: 50,
  }) as {
    data: ActivityFeedItem[] | undefined;
    isLoading: boolean;
    error: any;
  };

  const getActivityIcon = (type: ActivityType): React.ComponentType<any> => {
    const iconMap: Record<ActivityType, React.ComponentType<any>> = {
      userRegistered: UserIcon,
      userLogin: UserIcon,
      subscriptionCreated: CreditCardIcon,
      subscriptionCanceled: XMarkIcon,
      paymentSucceeded: CheckCircleIcon,
      paymentFailed: ExclamationTriangleIcon,
      planUpgraded: ArrowRightIcon,
      planDowngraded: ArrowRightIcon,
      usageLimitReached: ExclamationTriangleIcon,
      systemError: ExclamationTriangleIcon,
    };

    return iconMap[type] || ClockIcon;
  };

  const getActivityColor = (type: ActivityType): string => {
    const colorMap: Record<ActivityType, string> = {
      userRegistered: 'text-green-600 bg-green-100',
      userLogin: 'text-blue-600 bg-blue-100',
      subscriptionCreated: 'text-green-600 bg-green-100',
      subscriptionCanceled: 'text-red-600 bg-red-100',
      paymentSucceeded: 'text-green-600 bg-green-100',
      paymentFailed: 'text-red-600 bg-red-100',
      planUpgraded: 'text-blue-600 bg-blue-100',
      planDowngraded: 'text-yellow-600 bg-yellow-100',
      usageLimitReached: 'text-yellow-600 bg-yellow-100',
      systemError: 'text-red-600 bg-red-100',
    };

    return colorMap[type] || 'text-gray-600 bg-gray-100';
  };

  const getActivityTitle = (activity: Activity): string => {
    const titleMap: Record<ActivityType, string> = {
      userRegistered: 'Utilizator nou înregistrat',
      userLogin: 'Utilizator autentificat',
      subscriptionCreated: 'Abonament creat',
      subscriptionCanceled: 'Abonament anulat',
      paymentSucceeded: 'Plată reușită',
      paymentFailed: 'Plată eșuată',
      planUpgraded: 'Plan îmbunătățit',
      planDowngraded: 'Plan retrogradat',
      usageLimitReached: 'Limită de utilizare atinsă',
      systemError: 'Eroare de sistem',
    };

    return titleMap[activity.type] || 'Activitate necunoscută';
  };

  const getActivityDescription = (activity: Activity): string => {
    const { type, data } = activity;

    if (!data) {
      return activity.description || 'Fără descriere';
    }

    switch (type) {
      case 'userRegistered':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''} (${data.user?.email || ''})`;
      case 'userLogin':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''} s-a autentificat`;
      case 'subscriptionCreated':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a creat abonament ${data.plan?.name || ''}`;
      case 'subscriptionCanceled':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a anulat abonamentul ${data.plan?.name || ''}`;
      case 'paymentSucceeded':
        return `Plată de ${formatCurrency(data.amount || 0)} pentru ${data.user?.firstName || ''} ${data.user?.lastName || ''}`;
      case 'paymentFailed':
        return `Plată eșuată de ${formatCurrency(data.amount || 0)} pentru ${data.user?.firstName || ''} ${data.user?.lastName || ''}`;
      case 'planUpgraded':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''}: ${data.fromPlan || ''} → ${data.toPlan || ''}`;
      case 'planDowngraded':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''}: ${data.fromPlan || ''} → ${data.toPlan || ''}`;
      case 'usageLimitReached':
        return `${data.user?.firstName || ''} ${data.user?.lastName || ''} a atins limita pentru ${data.feature || ''}`;
      case 'systemError':
        return data.message || 'Eroare de sistem';
      default:
        return activity.description || 'Fără descriere';
    }
  };

  const getPriorityBadge = (priority: 'low' | 'medium' | 'high'): JSX.Element => {
    const priorityConfig: Record<'low' | 'medium' | 'high', { variant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'; label: string }> = {
      high: { variant: 'danger', label: 'Prioritate mare' },
      medium: { variant: 'warning', label: 'Prioritate medie' },
      low: { variant: 'secondary', label: 'Prioritate mică' },
    };

    const config = priorityConfig[priority] || priorityConfig.low;
    return (
      <Badge variant={config.variant} size="sm">
        {config.label}
      </Badge>
    );
  };

  const getRelativeTime = (date: string): string => {
    const now = new Date();
    const activityDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - activityDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Acum';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}z`;
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">Eroare la încărcarea activității</div>
      </Card>
    );
  }

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Activitate recentă</h3>
          <div className="flex gap-2">
            <select
              value={timeRange}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="1h">Ultima oră</option>
              <option value="24h">Ultimele 24h</option>
              <option value="7d">Ultima săptămână</option>
              <option value="30d">Ultima lună</option>
            </select>
            <select
              value={filter}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFilter(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate</option>
              <option value="users">Utilizatori</option>
              <option value="payments">Plăți</option>
              <option value="subscriptions">Abonamente</option>
              <option value="errors">Erori</option>
            </select>
          </div>
        </div>

        {/* Lista de activități */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {activities?.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              Nu există activitate în perioada selectată
            </div>
          ) : (
            activities?.map((activity: ActivityFeedItem) => {
              const activityType = activity.type as ActivityType;
              const IconComponent = getActivityIcon(activityType);
              const colorClasses = getActivityColor(activityType);

              return (
                <div
                  key={activity.id}
                  className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => {
                    const activityData: Activity = {
                      ...activity,
                      type: activity.type as ActivityType,
                      createdAt: activity.timestamp,
                      priority: 'medium',
                    };
                    setSelectedActivity(activityData);
                    setShowActivityModal(true);
                  }}
                >
                  <div
                    className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`}
                  >
                    <IconComponent className="h-4 w-4" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {getActivityTitle({
                          ...activity,
                          type: activity.type as ActivityType,
                          createdAt: activity.timestamp,
                          priority: 'medium',
                        } as Activity)}
                      </p>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {getRelativeTime(activity.timestamp)}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 truncate">
                      {getActivityDescription({
                        ...activity,
                        type: activity.type as ActivityType,
                        createdAt: activity.timestamp,
                        priority: 'medium',
                      } as Activity)}
                    </p>
                    {activity.metadata?.['ip'] && (
                      <p className="text-xs text-gray-400">IP: {activity.metadata['ip']}</p>
                    )}
                  </div>

                  <div className="flex-shrink-0">
                    <EyeIcon className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Statistici rapide */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-green-600">
                {activities?.filter(
                  (a: ActivityFeedItem) =>
                    a.type.includes('succeeded') || a.type.includes('created'),
                ).length || 0}
              </div>
              <div className="text-xs text-gray-500">Acțiuni pozitive</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">
                {activities?.filter(
                  (a: ActivityFeedItem) => a.type.includes('failed') || a.type.includes('error'),
                ).length || 0}
              </div>
              <div className="text-xs text-gray-500">Erori</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {activities?.filter((a: ActivityFeedItem) => a.type.includes('user')).length || 0}
              </div>
              <div className="text-xs text-gray-500">Activitate utilizatori</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-purple-600">
                {activities?.filter((a: ActivityFeedItem) => a.type.includes('payment')).length ||
                  0}
              </div>
              <div className="text-xs text-gray-500">Tranzacții</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal detalii activitate */}
      <Modal
        isOpen={showActivityModal}
        onClose={() => setShowActivityModal(false)}
        title="Detalii activitate"
        size="lg"
      >
        {selectedActivity && (
          <div className="space-y-6">
            <div className="flex items-start space-x-3">
              <div
                className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${getActivityColor(
                  selectedActivity.type,
                )}`}
              >
                {React.createElement(getActivityIcon(selectedActivity.type), {
                  className: 'h-5 w-5',
                })}
              </div>
              <div className="flex-1">
                <h4 className="text-lg font-medium text-gray-900">
                  {getActivityTitle(selectedActivity)}
                </h4>
                <p className="text-sm text-gray-600">{getActivityDescription(selectedActivity)}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {formatDate(selectedActivity.createdAt)}
                </p>
              </div>
              {selectedActivity.priority && selectedActivity.priority !== 'low' && (
                <div className="flex-shrink-0">{getPriorityBadge(selectedActivity.priority)}</div>
              )}
            </div>

            {/* Detalii utilizator */}
            {selectedActivity.data?.user && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Utilizator</h5>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Nume:</span>
                    <span className="ml-2 text-gray-900">
                      {selectedActivity.data.user.firstName || ''}{' '}
                      {selectedActivity.data.user.lastName || ''}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <span className="ml-2 text-gray-900">{selectedActivity.data.user.email}</span>
                  </div>
                  {selectedActivity.data.user.id && (
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <span className="ml-2 text-gray-900 font-mono text-xs">
                        {selectedActivity.data.user.id}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Detalii financiare */}
            {selectedActivity.data?.amount && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Detalii financiare</h5>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Sumă:</span>
                    <span className="ml-2 text-gray-900 font-semibold">
                      {formatCurrency(selectedActivity.data.amount)}
                    </span>
                  </div>
                  {selectedActivity.data.currency && (
                    <div>
                      <span className="text-gray-500">Monedă:</span>
                      <span className="ml-2 text-gray-900">{selectedActivity.data.currency}</span>
                    </div>
                  )}
                  {selectedActivity.data.paymentMethod && (
                    <div>
                      <span className="text-gray-500">Metodă plată:</span>
                      <span className="ml-2 text-gray-900">
                        {selectedActivity.data.paymentMethod}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metadata tehnică */}
            {selectedActivity.metadata && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Informații tehnice</h5>
                <div className="bg-gray-50 p-3 rounded-md">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(selectedActivity.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Detalii complete */}
            {selectedActivity.data && Object.keys(selectedActivity.data).length > 0 && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Date complete</h5>
                <div className="bg-gray-50 p-3 rounded-md max-h-40 overflow-y-auto">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(selectedActivity.data, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default ActivityFeed;
