/**
 * Utilitare pentru gestionarea numelor în aplicație
 * Adaptate pentru noua structură firstName/lastName
 */

/**
 * Obține numele complet al utilizatorului
 */
export function getFullName(firstName?: string, lastName?: string): string {
  if (!firstName && !lastName) {
    return 'Utilizator anonim';
  }
  
  const parts = [firstName, lastName].filter(Boolean);
  return parts.join(' ');
}

/**
 * Obține inițialele utilizatorului
 */
export function getInitials(firstName?: string, lastName?: string): string {
  const firstInitial = firstName?.charAt(0)?.toUpperCase() || '';
  const lastInitial = lastName?.charAt(0)?.toUpperCase() || '';
  
  if (!firstInitial && !lastInitial) {
    return 'U';
  }
  
  return `${firstInitial}${lastInitial}`;
}

/**
 * Formatează numele pentru afișare
 */
export function formatDisplayName(
  firstName?: string, 
  lastName?: string, 
  format: 'full' | 'first' | 'last' | 'initials' | 'firstLast' | 'lastFirst' = 'full'
): string {
  switch (format) {
    case 'full':
      return getFullName(firstName, lastName);
    case 'first':
      return firstName || 'Utilizator';
    case 'last':
      return lastName || '';
    case 'initials':
      return getInitials(firstName, lastName);
    case 'firstLast':
      return getFullName(firstName, lastName);
    case 'lastFirst':
      if (!firstName && !lastName) return 'Utilizator anonim';
      const parts = [lastName, firstName].filter(Boolean);
      return parts.join(', ');
    default:
      return getFullName(firstName, lastName);
  }
}

/**
 * Parsează un nume complet în firstName și lastName
 */
export function parseFullName(fullName: string): { firstName: string; lastName: string } {
  const trimmed = fullName.trim();
  
  if (!trimmed) {
    return { firstName: '', lastName: '' };
  }
  
  const parts = trimmed.split(/\s+/);
  
  if (parts.length === 1) {
    return { firstName: parts[0] || '', lastName: '' };
  }
  
  const firstName = parts[0] || '';
  const lastName = parts.slice(1).join(' ') || '';
  
  return { firstName, lastName };
}

/**
 * Validează dacă un nume este valid
 */
export function isValidName(name: string): boolean {
  if (!name || typeof name !== 'string') {
    return false;
  }
  
  const trimmed = name.trim();
  
  // Verifică lungimea minimă
  if (trimmed.length < 1) {
    return false;
  }
  
  // Verifică lungimea maximă
  if (trimmed.length > 50) {
    return false;
  }
  
  // Verifică caracterele permise (litere, spații, apostroafe, cratimă)
  const nameRegex = /^[a-zA-ZăâîșțĂÂÎȘȚ\s'\-]+$/;
  return nameRegex.test(trimmed);
}

/**
 * Validează firstName și lastName
 */
export function validateNames(firstName?: string, lastName?: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!firstName || !firstName.trim()) {
    errors.push('Prenumele este obligatoriu');
  } else if (!isValidName(firstName)) {
    errors.push('Prenumele conține caractere nevalide');
  }
  
  if (lastName && !isValidName(lastName)) {
    errors.push('Numele de familie conține caractere nevalide');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Capitalizează prima literă a fiecărui cuvânt
 */
export function capitalizeWords(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  return text
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formatează numele pentru stocare (capitalizare corectă)
 */
export function formatNameForStorage(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }
  
  return capitalizeWords(name.trim());
}

/**
 * Generează un nume de utilizator pe baza numelui complet
 */
export function generateUsername(firstName?: string, lastName?: string): string {
  const first = firstName?.toLowerCase().replace(/[^a-z]/g, '') || '';
  const last = lastName?.toLowerCase().replace(/[^a-z]/g, '') || '';
  
  if (!first && !last) {
    return `user${Date.now()}`;
  }
  
  if (!last) {
    return first;
  }
  
  if (!first) {
    return last;
  }
  
  return `${first}.${last}`;
}

/**
 * Creează o salutare personalizată
 */
export function createGreeting(
  firstName?: string, 
  lastName?: string, 
  timeOfDay: 'morning' | 'afternoon' | 'evening' = 'morning'
): string {
  const name = firstName || 'Utilizator';
  
  const greetings = {
    morning: 'Bună dimineața',
    afternoon: 'Bună ziua',
    evening: 'Bună seara'
  };
  
  return `${greetings[timeOfDay]}, ${name}!`;
}

/**
 * Obține timpul zilei pe baza orei curente
 */
export function getTimeOfDay(): 'morning' | 'afternoon' | 'evening' {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return 'morning';
  } else if (hour < 18) {
    return 'afternoon';
  } else {
    return 'evening';
  }
}

/**
 * Hook React pentru utilitarele de nume
 */
export function useNameHelpers() {
  return {
    getFullName,
    getInitials,
    formatDisplayName,
    parseFullName,
    isValidName,
    validateNames,
    capitalizeWords,
    formatNameForStorage,
    generateUsername,
    createGreeting,
    getTimeOfDay
  };
}

/**
 * Tipuri pentru export
 */
export interface NameValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ParsedName {
  firstName: string;
  lastName: string;
}

export type DisplayNameFormat = 'full' | 'first' | 'last' | 'initials' | 'firstLast' | 'lastFirst';
export type TimeOfDay = 'morning' | 'afternoon' | 'evening';