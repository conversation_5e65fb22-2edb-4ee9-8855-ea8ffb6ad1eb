module.exports = {
  parser: '@typescript-eslint/parser',
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react/jsx-runtime',
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
  },
  plugins: ['@typescript-eslint', 'react', 'react-hooks', 'react-refresh'],
  rules: {
    // React hooks rules (essential for React)
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',

    // React JSX rules
    'react/jsx-uses-react': 'off', // Not needed with new JSX transform
    'react/react-in-jsx-scope': 'off', // Not needed with new JSX transform
    'react/prop-types': 'off', // Using TypeScript instead
    'react/no-unescaped-entities': 'warn',
    'react/jsx-key': [
      'error',
      {
        checkFragmentShorthand: true,
        checkKeyMustBeforeSpread: true,
        warnOnDuplicates: true,
      },
    ],
    'react/jsx-no-useless-fragment': 'warn',
    'react/jsx-curly-brace-presence': 'warn',
    'react/self-closing-comp': 'warn',
    'react/no-unstable-nested-components': ['error', { allowAsProps: true }],
    'react/jsx-fragments': 'error',
    'react/button-has-type': 'off', // Reduced from error
    'react/no-children-prop': 'error',
    'react/jsx-filename-extension': ['warn', { extensions: ['.tsx'] }],

    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off', // Reduced from warn
    '@typescript-eslint/no-var-requires': 'error',
    '@typescript-eslint/no-empty-object-type': 'warn', // Replaced ban-types
    '@typescript-eslint/no-unsafe-assignment': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-call': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-member-access': 'warn', // Changed from error to warn
    '@typescript-eslint/no-unsafe-return': 'warn', // Changed from error to warn
    '@typescript-eslint/ban-ts-comment': 'warn', // Added for @ts-ignore issues

    // General rules
    'no-unused-vars': 'off', // Using TypeScript version instead
    'no-console': process.env['NODE_ENV'] === 'production' ? 'error' : 'warn',
    'no-debugger': process.env['NODE_ENV'] === 'production' ? 'error' : 'warn',
    'no-var': 'error',
    'no-undef': 'off', // TypeScript handles this
    'prefer-const': 'error',

    // Security rules
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    'no-caller': 'error',
    'no-extend-native': 'error',
    'no-extra-bind': 'error',
    'no-iterator': 'error',
    'no-lone-blocks': 'error',
    'no-loop-func': 'error',
    'no-multi-str': 'error',
    'no-new-wrappers': 'error',
    'no-octal-escape': 'error',
    'no-proto': 'error',
    'no-return-assign': 'error',
    'no-self-compare': 'error',
    'no-sequences': 'error',
    'no-throw-literal': 'error',
    'no-unused-expressions': 'error',
    'no-useless-call': 'error',
    'no-useless-concat': 'error',
    'no-void': 'error',
    'no-with': 'error',
    'no-alert': 'error',

    // DOM Security
    'no-innerHTML': 'off', // Will be handled by React rules
    'prefer-template': 'error',
    'prefer-spread': 'error',
    'prefer-rest-params': 'error',

    // React Refresh
    'react-refresh/only-export-components': 'warn',
  },
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
      webpack: {
        config: 'webpack.config.js',
      },
    },
  },
  ignorePatterns: [
    'dist/',
    'build/',
    'node_modules/',
    '*.config.js',
    '*.config.ts',
    'test-*.js',
    'vite.config.js',
    'coverage/',
    '.eslintrc.js',
    'scripts/',
  ],
};
