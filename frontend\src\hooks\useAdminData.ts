import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

import adminService from '../services/adminService';

// Tipuri pentru hook-urile admin
interface UseUsersParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  plan?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UseSubscriptionsParams {
  page?: number;
  limit?: number;
  status?: string;
  plan?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UseActivityFeedParams {
  page?: number;
  limit?: number;
  type?: string | undefined;
  timeRange?: string;
}

interface BlockUserParams {
  userId: string | number;
  reason: string;
}

interface SuspendSubscriptionParams {
  subscriptionId: string | number;
  reason: string;
}

interface CancelSubscriptionParams {
  subscriptionId: string | number;
  reason: string;
}

interface ExportDataParams {
  type: string;
  params: Record<string, any>;
}

/**
 * Hook pentru statisticile dashboard-ului admin
 */
export function useAdminDashboardStats() {
  return useQuery({
    queryKey: ['admin', 'dashboard', 'stats'],
    queryFn: adminService.getDashboardStats,
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 10 * 60 * 1000, // 10 minute (renamed from cacheTime)
  });
}

/**
 * Hook pentru statisticile de abonament
 */
export function useSubscriptionStats() {
  return useQuery({
    queryKey: ['admin', 'subscriptions', 'stats'],
    queryFn: adminService.getSubscriptionStats,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Hook pentru statisticile planurilor
 */
export function usePlanStats() {
  return useQuery({
    queryKey: ['admin', 'plans', 'stats'],
    queryFn: adminService.getPlanStats,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Hook pentru statisticile de utilizare
 */
export function useUsageStats() {
  return useQuery({
    queryKey: ['admin', 'usage', 'stats'],
    queryFn: adminService.getUsageStats,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Hook pentru datele de venituri
 */
export function useRevenueData(period: string = '12months') {
  return useQuery({
    queryKey: ['admin', 'revenue', 'data', period],
    queryFn: () => adminService.getRevenueData(period),
    staleTime: 10 * 60 * 1000, // 10 minute
    gcTime: 30 * 60 * 1000, // 30 minute
  });
}

/**
 * Hook pentru lista utilizatorilor cu paginare infinită
 */
export function useUsers(params: UseUsersParams = {}) {
  return useInfiniteQuery({
    queryKey: ['admin', 'users', params],
    queryFn: ({ pageParam = 1 }) => adminService.getUsers({ ...params, page: pageParam }),
    getNextPageParam: lastPage => {
      if (!lastPage.data || !lastPage.pagination) return undefined;
      const { page: currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: 'always',
    initialPageParam: 1,
  });
}

/**
 * Hook pentru lista utilizatorilor (versiune simplă pentru compatibilitate)
 */
export function useUsersSimple(params: UseUsersParams = {}) {
  return useQuery({
    queryKey: ['admin', 'users', 'simple', params],
    queryFn: () => adminService.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook pentru detaliile unui utilizator
 */
export function useUserDetails(userId: string | number | null | undefined) {
  return useQuery({
    queryKey: ['admin', 'users', userId],
    queryFn: () => adminService.getUserDetails(userId!),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook pentru blocarea unui utilizator
 */
export function useBlockUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, reason }: BlockUserParams) => adminService.blockUser(userId, reason),
    onSuccess: (_data, { userId }) => {
      // Invalidează cache-ul pentru utilizatori
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', userId] });
      toast.success('Utilizatorul a fost blocat cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la blocarea utilizatorului');
    },
  });
}

/**
 * Hook pentru deblocarea unui utilizator
 */
export function useUnblockUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string | number) => adminService.unblockUser(userId),
    onSuccess: (_data, userId) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'users', userId] });
      toast.success('Utilizatorul a fost deblocat cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la deblocarea utilizatorului');
    },
  });
}

/**
 * Hook pentru lista abonamentelor cu paginare infinită
 */
export function useSubscriptions(params: UseSubscriptionsParams = {}) {
  return useInfiniteQuery({
    queryKey: ['admin', 'subscriptions', params],
    queryFn: ({ pageParam = 1 }) => adminService.getSubscriptions({ ...params, page: pageParam }),
    getNextPageParam: lastPage => {
      if (!lastPage.data || !lastPage.pagination) return undefined;
      const { page: currentPage, totalPages } = lastPage.pagination;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: 'always',
    initialPageParam: 1,
  });
}

/**
 * Hook pentru lista abonamentelor (versiune simplă pentru compatibilitate)
 */
export function useSubscriptionsSimple(params: UseSubscriptionsParams = {}) {
  return useQuery({
    queryKey: ['admin', 'subscriptions', 'simple', params],
    queryFn: () => adminService.getSubscriptions(params),
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook pentru detaliile unui abonament
 */
export function useSubscriptionDetails(subscriptionId: string | number | null | undefined) {
  return useQuery({
    queryKey: ['admin', 'subscriptions', subscriptionId],
    queryFn: () => adminService.getSubscriptionDetails(subscriptionId!),
    enabled: !!subscriptionId,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook pentru suspendarea unui abonament
 */
export function useSuspendSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ subscriptionId, reason }: SuspendSubscriptionParams) =>
      adminService.suspendSubscription(subscriptionId, reason),
    onSuccess: (_data, { subscriptionId }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
      toast.success('Abonamentul a fost suspendat cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la suspendarea abonamentului');
    },
  });
}

/**
 * Hook pentru reactivarea unui abonament
 */
export function useReactivateSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (subscriptionId: string | number) =>
      adminService.reactivateSubscription(subscriptionId),
    onSuccess: (data, subscriptionId) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
      toast.success('Abonamentul a fost reactivat cu succes');
    },
    onError: (error: unknown) => {
      toast.error(error.response?.data?.message || 'Eroare la reactivarea abonamentului');
    },
  });
}

/**
 * Hook pentru anularea unui abonament
 */
export function useCancelSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ subscriptionId, reason }: CancelSubscriptionParams) =>
      adminService.cancelSubscription(subscriptionId, reason),
    onSuccess: (data, { subscriptionId }) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', 'stats'] });
      toast.success('Abonamentul a fost anulat cu succes');
    },
    onError: (error: unknown) => {
      toast.error(error.response?.data?.message || 'Eroare la anularea abonamentului');
    },
  });
}

/**
 * Hook pentru sincronizarea cu Stripe
 */
export function useSyncWithStripe() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (subscriptionId: string | number) =>
      adminService.syncSubscriptionWithStripe(subscriptionId),
    onSuccess: (data, subscriptionId) => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'subscriptions', subscriptionId] });
      toast.success('Sincronizarea cu Stripe a fost completă');
    },
    onError: (error: unknown) => {
      toast.error(error.response?.data?.message || 'Eroare la sincronizarea cu Stripe');
    },
  });
}

/**
 * Hook pentru activitatea recentă
 */
export function useActivityFeed(params: UseActivityFeedParams = {}) {
  return useQuery({
    queryKey: ['admin', 'activity', params],
    queryFn: async () => {
      const response = await adminService.getActivityFeed(params);
      // Returnează doar array-ul de activități din structura de răspuns
      return response.data || [];
    },
    staleTime: 1 * 60 * 1000, // 1 minut
    refetchInterval: 5 * 60 * 1000, // Refresh la 5 minute
  });
}

/**
 * Hook pentru statisticile activității
 */
export function useActivityStats(timeRange: string = '7d') {
  return useQuery({
    queryKey: ['admin', 'activity', 'stats', timeRange],
    queryFn: () => adminService.getActivityStats(timeRange),
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook pentru alertele sistemului
 */
export function useSystemAlerts() {
  return useQuery({
    queryKey: ['admin', 'alerts'],
    queryFn: adminService.getSystemAlerts,
    staleTime: 1 * 60 * 1000,
    refetchInterval: 2 * 60 * 1000, // Refresh la 2 minute
  });
}

/**
 * Hook pentru marcarea alertelor ca citite
 */
export function useMarkAlertAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (alertId: string | number) => adminService.markAlertAsRead(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'alerts'] });
    },
    onError: (error: unknown) => {
      toast.error(error.response?.data?.message || 'Eroare la marcarea alertei');
    },
  });
}

/**
 * Hook pentru exportul de date
 */
export function useExportData() {
  return useMutation({
    mutationFn: ({ type, params }: ExportDataParams) => adminService.exportData(type, params),
    onSuccess: (blob, { type }) => {
      // Creează și descarcă fișierul
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${type}_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Exportul a fost descărcat cu succes');
    },
    onError: (error: unknown) => {
      toast.error(error.response?.data?.message || 'Eroare la exportul datelor');
    },
  });
}
