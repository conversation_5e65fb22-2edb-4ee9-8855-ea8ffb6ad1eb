import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

import expenseService from '../services/expenseService';
import type {
  CreateExpenseForm,
  UpdateExpenseForm,
  UseExpensesParams,
  ExpenseFilters,
} from '../types';

// Tipuri pentru hook-urile de cheltuieli (folosind tipurile din index.ts)
interface UseExpenseStatsParams {
  startDate?: string;
  endDate?: string;
  categoryId?: string;
  period?: 'week' | 'month' | 'quarter' | 'year';
}

// Parametri pentru actualizarea cheltuielilor
interface UpdateExpenseParams {
  id: string;
  data: Partial<UpdateExpenseForm>;
}

// Parametri pentru exportul cheltuielilor
interface ExportExpensesParams {
  format: 'csv' | 'pdf' | 'excel';
  params?: ExpenseFilters;
}

/**
 * Hook pentru obținerea cheltuielilor
 */
export function useExpenses(params: UseExpensesParams = {}) {
  return useQuery({
    queryKey: ['expenses', params],
    queryFn: () => expenseService.getExpenses(params),
    staleTime: 2 * 60 * 1000, // 2 minute
    gcTime: 10 * 60 * 1000, // 10 minute (renamed from cacheTime)
  });
}

/**
 * Hook pentru obținerea statisticilor cheltuielilor
 */
export function useExpenseStats(params: UseExpenseStatsParams = {}) {
  return useQuery({
    queryKey: ['expense-stats', params],
    queryFn: () => expenseService.getExpenseStats(params),
    staleTime: 5 * 60 * 1000, // 5 minute
    gcTime: 15 * 60 * 1000, // 15 minute
  });
}

/**
 * Hook pentru crearea unei cheltuieli
 */
export function useCreateExpense() {
  return useMutation({
    mutationFn: (data: CreateExpenseForm) => expenseService.createExpense(data),
    onSuccess: () => {
      toast.success('Cheltuiala a fost adăugată cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la adăugarea cheltuielii');
    },
  });
}

/**
 * Hook pentru actualizarea unei cheltuieli
 */
export function useUpdateExpense() {
  return useMutation({
    mutationFn: ({ id, data }: UpdateExpenseParams) => expenseService.updateExpense(id, data),
    onSuccess: () => {
      toast.success('Cheltuiala a fost actualizată cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la actualizarea cheltuielii');
    },
  });
}

/**
 * Hook pentru ștergerea unei cheltuieli
 */
export function useDeleteExpense() {
  return useMutation({
    mutationFn: (id: string) => expenseService.deleteExpense(id),
    onSuccess: () => {
      toast.success('Cheltuiala a fost ștearsă cu succes');
    },
    onError: (error: unknown) => {
      toast.error((error as any).response?.data?.message || 'Eroare la ștergerea cheltuielii');
    },
  });
}

/**
 * Hook pentru exportul cheltuielilor
 */
export function useExportExpenses() {
  return useMutation({
    mutationFn: ({ format, params = {} }: ExportExpensesParams) =>
      expenseService.exportExpenses(format, params),
    onSuccess: (blob, { format }) => {
      // Determină extensia fișierului bazată pe format
      let fileExtension = 'csv';
      let mimeType = 'text/csv';

      if (format === 'pdf') {
        fileExtension = 'pdf';
        mimeType = 'application/pdf';
      } else if (format === 'excel') {
        fileExtension = 'xlsx';
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      }

      // Creează și descarcă fișierul
      const url = window.URL.createObjectURL(new Blob([blob], { type: mimeType }));
      const link = document.createElement('a');
      link.href = url;
      link.download = `cheltuieli_export_${new Date().toISOString().split('T')[0]}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Exportul ${format.toUpperCase()} a fost descărcat cu succes`);
    },
    onError: (error: unknown) => {
      const errorMessage =
        (error as any).response?.data?.message || `Eroare la exportul cheltuielilor`;
      toast.error(errorMessage);
    },
  });
}
