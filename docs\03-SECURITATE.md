# 🔒 SECURITATE ȘI BEST PRACTICES

## 🛡️ OVERVIEW SECURITATE

### Principii de Securitate
1. **Defense in Depth** - Multiple layere de protecție
2. **Least Privilege** - Acces minim necesar
3. **Zero Trust** - Verificare continuă
4. **Security by Design** - Securitate integrată din design
5. **Fail Secure** - Eșec în mod sigur

### Threat Model
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ATACATORI     │    │   VULNERABILITĂȚI│    │   MĂSURI        │
│                 │    │                 │    │   PROTECȚIE     │
│ • Script Kiddies│    │ • XSS           │    │ • Input Validation│
│ • Hackers       │    │ • SQL Injection │    │ • HTTPS/TLS     │
│ • Bots          │    │ • CSRF          │    │ • Rate Limiting │
│ • Insiders      │    │ • Auth Bypass   │    │ • Audit Logging │
│ • Competitors   │    │ • Data Leaks    │    │ • Encryption    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 🔐 AUTENTIFICARE ȘI AUTORIZARE

### JWT Implementation

#### Token Structure
```typescript
// Access Token (15 minute TTL)
interface AccessTokenPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin';
  subscriptionPlan: 'free' | 'basic' | 'premium';
  iat: number; // issued at
  exp: number; // expires at
}

// Refresh Token (7 zile TTL)
interface RefreshTokenPayload {
  userId: string;
  tokenVersion: number; // pentru invalidare
  iat: number;
  exp: number;
}
```

#### Token Management
```typescript
// authService.ts
class AuthService {
  generateTokens(user: User) {
    const accessToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        subscriptionPlan: user.subscriptionPlan,
      },
      process.env.JWT_SECRET!,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      {
        userId: user.id,
        tokenVersion: user.tokenVersion,
      },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );

    return { accessToken, refreshToken };
  }

  async refreshTokens(refreshToken: string) {
    try {
      const payload = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as RefreshTokenPayload;
      const user = await User.findById(payload.userId);
      
      if (!user || user.tokenVersion !== payload.tokenVersion) {
        throw new Error('Invalid refresh token');
      }

      return this.generateTokens(user);
    } catch (error) {
      throw new Error('Token refresh failed');
    }
  }
}
```

### Role-Based Access Control (RBAC)

#### Middleware de Autorizare
```typescript
// authMiddleware.ts
export const requireAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const payload = jwt.verify(token, process.env.JWT_SECRET!) as AccessTokenPayload;
    const user = await User.findById(payload.userId);

    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid or expired token' });
  }
};

export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

export const requireSubscription = (plans: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || !plans.includes(req.user.subscriptionPlan)) {
      return res.status(403).json({ 
        error: 'Subscription upgrade required',
        requiredPlans: plans 
      });
    }
    next();
  };
};
```

#### Protecția Rutelor
```typescript
// routes/expenses.ts
router.get('/expenses', requireAuth, expenseController.getExpenses);
router.post('/expenses', requireAuth, validateExpense, expenseController.createExpense);
router.get('/reports/advanced', requireAuth, requireSubscription(['premium']), reportController.getAdvancedReports);
router.get('/admin/users', requireAuth, requireRole(['admin']), adminController.getUsers);
```

---

## 🛡️ PROTECȚIA ÎMPOTRIVA ATACURILOR

### Cross-Site Scripting (XSS)

#### Content Security Policy
```typescript
// helmet configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.stripe.com"],
      frameSrc: ["https://js.stripe.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
}));
```

#### Input Sanitization
```typescript
// xssMiddleware.ts
import xss from 'xss';

export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return xss(obj, {
        whiteList: {}, // Nu permite niciun tag HTML
        stripIgnoreTag: true,
        stripIgnoreTagBody: ['script'],
      });
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const key in obj) {
        sanitized[key] = sanitizeObject(obj[key]);
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  next();
};
```

### SQL Injection Protection

#### Prisma ORM Usage
```typescript
// Prisma oferă protecție automată împotriva SQL injection
// prin prepared statements și type safety

// ✅ SIGUR - Prisma ORM
const expenses = await prisma.expense.findMany({
  where: {
    userId: userId, // Parametru sigur
    amount: {
      gte: minAmount, // Parametru sigur
    },
    description: {
      contains: searchTerm, // Parametru sigur
    },
  },
});

// ❌ NESIGUR - Raw SQL (evitat)
// const expenses = await prisma.$queryRaw`
//   SELECT * FROM expenses WHERE user_id = ${userId}
// `;
```

#### Input Validation cu Joi
```typescript
// validationSchemas.ts
export const expenseSchema = Joi.object({
  amount: Joi.number().positive().precision(2).required(),
  description: Joi.string().max(500).trim().required(),
  categoryId: Joi.string().uuid().required(),
  expenseDate: Joi.date().max('now').required(),
  tags: Joi.array().items(Joi.string().max(50)).max(10),
});

export const userRegistrationSchema = Joi.object({
  email: Joi.string().email().lowercase().required(),
  password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required(),
  name: Joi.string().min(2).max(100).trim().required(),
});
```

### Cross-Site Request Forgery (CSRF)

#### CSRF Protection
```typescript
// csrfMiddleware.ts
import csrf from 'csurf';

const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env['NODE_ENV'] === 'production',
    sameSite: 'strict',
  },
});

// Aplicare pentru rute sensibile
app.use('/api/expenses', csrfProtection);
app.use('/api/subscriptions', csrfProtection);
app.use('/api/admin', csrfProtection);

// Endpoint pentru obținerea token-ului CSRF
app.get('/api/csrf-token', csrfProtection, (req, res) => {
  res.json({ csrfToken: req.csrfToken() });
});
```

### Rate Limiting

#### Configurare Rate Limiting
```typescript
// rateLimiter.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

// Rate limiting general
export const generalLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minute
  max: 1000, // 1000 requests per window
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting pentru autentificare
export const authLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 15 * 60 * 1000, // 15 minute
  max: 5, // 5 încercări de login per IP
  skipSuccessfulRequests: true,
  message: 'Too many login attempts, please try again later',
});

// Rate limiting pentru API-uri premium
export const premiumLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minut
  max: 100, // 100 requests per minute pentru premium
  skip: (req) => req.user?.subscriptionPlan !== 'premium',
});
```

---

## 🔍 AUDIT ȘI MONITORING

### Audit Trail Implementation

#### Audit Logging
```typescript
// auditLogger.ts
interface AuditEvent {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  details?: any;
}

class AuditLogger {
  async log(event: AuditEvent) {
    try {
      await prisma.auditLog.create({
        data: {
          userId: event.userId,
          action: event.action,
          resource: event.resource,
          resourceId: event.resourceId,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          timestamp: event.timestamp,
          success: event.success,
          details: event.details ? JSON.stringify(event.details) : null,
        },
      });
    } catch (error) {
      console.error('Audit logging failed:', error);
    }
  }
}

// Middleware pentru audit
export const auditMiddleware = (action: string, resource: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      const success = res.statusCode < 400;
      
      auditLogger.log({
        userId: req.user?.id,
        action,
        resource,
        resourceId: req.params.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent') || '',
        timestamp: new Date(),
        success,
        details: success ? null : { error: data },
      });
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};
```

### Security Monitoring

#### Intrusion Detection
```typescript
// securityMonitor.ts
class SecurityMonitor {
  private suspiciousActivities = new Map<string, number>();
  
  async detectSuspiciousActivity(req: Request) {
    const ip = req.ip;
    const patterns = [
      // Multiple failed login attempts
      { pattern: /\/api\/auth\/login/, threshold: 5, window: 15 * 60 * 1000 },
      // Rapid API calls
      { pattern: /\/api\//, threshold: 100, window: 60 * 1000 },
      // Admin endpoint access
      { pattern: /\/api\/admin/, threshold: 10, window: 60 * 1000 },
    ];

    for (const { pattern, threshold, window } of patterns) {
      if (pattern.test(req.path)) {
        const key = `${ip}:${pattern.source}`;
        const count = this.suspiciousActivities.get(key) || 0;
        
        if (count >= threshold) {
          await this.alertSecurity({
            type: 'SUSPICIOUS_ACTIVITY',
            ip,
            pattern: pattern.source,
            count,
            timestamp: new Date(),
          });
          
          // Temporary IP ban
          await this.banIP(ip, window);
        }
        
        this.suspiciousActivities.set(key, count + 1);
        
        // Cleanup old entries
        setTimeout(() => {
          this.suspiciousActivities.delete(key);
        }, window);
      }
    }
  }

  private async alertSecurity(alert: any) {
    // Send alert to monitoring system
    console.warn('SECURITY ALERT:', alert);
    
    // În producție: trimite către Slack, email, sau monitoring service
    // await notificationService.sendSecurityAlert(alert);
  }

  private async banIP(ip: string, duration: number) {
    // Add IP to temporary ban list
    await redis.setex(`banned:${ip}`, Math.floor(duration / 1000), '1');
  }
}
```

---

## 🔐 CRIPTARE ȘI PROTECȚIA DATELOR

### Password Security

#### Hashing cu bcrypt
```typescript
// passwordService.ts
import bcrypt from 'bcrypt';

class PasswordService {
  private readonly saltRounds = 12;

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { valid: errors.length === 0, errors };
  }
}
```

### Data Encryption

#### Sensitive Data Encryption
```typescript
// encryptionService.ts
import crypto from 'crypto';

class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);

  encrypt(text: string): { encrypted: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('expense-tracker', 'utf8'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }

  decrypt(encrypted: string, iv: string, tag: string): string {
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('expense-tracker', 'utf8'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// Usage pentru date sensibile
const encryptSensitiveData = (user: User) => {
  if (user.bankAccount) {
    const encrypted = encryptionService.encrypt(user.bankAccount);
    user.bankAccountEncrypted = `${encrypted.encrypted}:${encrypted.iv}:${encrypted.tag}`;
    delete user.bankAccount;
  }
};
```

---

## 🌐 SECURITATEA FRONTEND-ULUI

### Token Storage

#### Secure Token Management
```typescript
// authStore.ts (Zustand)
interface AuthState {
  accessToken: string | null;
  refreshToken: string | null;
  user: User | null;
  
  // Secure token storage
  setTokens: (tokens: { accessToken: string; refreshToken: string }) => void;
  clearTokens: () => void;
  refreshAuth: () => Promise<void>;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      accessToken: null,
      refreshToken: null,
      user: null,

      setTokens: (tokens) => {
        set({
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
        });
      },

      clearTokens: () => {
        set({
          accessToken: null,
          refreshToken: null,
          user: null,
        });
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        if (!refreshToken) return;

        try {
          const response = await authService.refreshTokens(refreshToken);
          get().setTokens(response.tokens);
        } catch (error) {
          get().clearTokens();
          window.location.href = '/login';
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        refreshToken: state.refreshToken, // Doar refresh token în localStorage
        user: state.user,
      }),
    }
  )
);
```

### Route Protection

#### Protected Routes
```typescript
// ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'user' | 'admin';
  requiredSubscription?: 'basic' | 'premium';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredSubscription,
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }

    if (requiredRole && user?.role !== requiredRole) {
      navigate('/unauthorized');
      return;
    }

    if (requiredSubscription && !hasSubscription(user, requiredSubscription)) {
      navigate('/upgrade');
      return;
    }
  }, [isAuthenticated, isLoading, user, navigate]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
};
```

### Input Validation

#### Client-side Validation
```typescript
// validationSchemas.ts (Zod)
export const expenseFormSchema = z.object({
  amount: z.number()
    .positive('Amount must be positive')
    .max(999999.99, 'Amount too large'),
  
  description: z.string()
    .min(1, 'Description is required')
    .max(500, 'Description too long')
    .refine(val => !/<script|javascript:/i.test(val), 'Invalid characters'),
  
  categoryId: z.string().uuid('Invalid category'),
  
  expenseDate: z.date()
    .max(new Date(), 'Date cannot be in the future'),
  
  tags: z.array(z.string().max(50)).max(10, 'Too many tags'),
});

// ExpenseForm.tsx
const ExpenseForm = () => {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(expenseFormSchema),
  });

  const onSubmit = async (data: ExpenseFormData) => {
    // Additional client-side sanitization
    const sanitizedData = {
      ...data,
      description: DOMPurify.sanitize(data.description),
      tags: data.tags?.map(tag => DOMPurify.sanitize(tag)),
    };

    await createExpense(sanitizedData);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields cu validare */}
    </form>
  );
};
```

---

## 🚨 INCIDENT RESPONSE

### Security Incident Playbook

#### 1. Detection și Alerting
```typescript
// incidentResponse.ts
enum IncidentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

interface SecurityIncident {
  id: string;
  type: string;
  severity: IncidentSeverity;
  description: string;
  affectedUsers?: string[];
  detectedAt: Date;
  resolvedAt?: Date;
  actions: string[];
}

class IncidentResponseService {
  async handleSecurityIncident(incident: SecurityIncident) {
    // 1. Log incident
    await this.logIncident(incident);
    
    // 2. Alert security team
    await this.alertSecurityTeam(incident);
    
    // 3. Automatic containment for critical incidents
    if (incident.severity === IncidentSeverity.CRITICAL) {
      await this.automaticContainment(incident);
    }
    
    // 4. Start investigation
    await this.startInvestigation(incident);
  }

  private async automaticContainment(incident: SecurityIncident) {
    switch (incident.type) {
      case 'MASS_DATA_ACCESS':
        // Temporarily disable API access
        await this.enableMaintenanceMode();
        break;
        
      case 'SUSPICIOUS_ADMIN_ACCESS':
        // Revoke all admin sessions
        await this.revokeAllAdminSessions();
        break;
        
      case 'PAYMENT_FRAUD':
        // Disable payment processing
        await this.disablePayments();
        break;
    }
  }
}
```

#### 2. Recovery Procedures
```bash
# Emergency response commands

# 1. Enable maintenance mode
curl -X POST https://api.expense-tracker.com/admin/maintenance \
  -H "Authorization: Bearer $EMERGENCY_TOKEN" \
  -d '{"enabled": true, "message": "Emergency maintenance"}'

# 2. Revoke all user sessions
curl -X POST https://api.expense-tracker.com/admin/revoke-sessions \
  -H "Authorization: Bearer $EMERGENCY_TOKEN"

# 3. Backup current database
pg_dump expense_tracker > emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# 4. Check system integrity
npm run security:audit
npm run db:integrity-check
```

---

## 📋 SECURITY CHECKLIST

### Pre-deployment Security Checklist

#### Backend Security
- [ ] Toate variabilele de mediu sunt configurate corect
- [ ] JWT secrets sunt generate securizat și unice
- [ ] Rate limiting este configurat pentru toate endpoint-urile
- [ ] Input validation este implementată pentru toate API-urile
- [ ] HTTPS este forțat în producție
- [ ] CORS este configurat restrictiv
- [ ] Security headers sunt setate (Helmet)
- [ ] Audit logging este funcțional
- [ ] Database backup este automatizat
- [ ] Monitoring și alerting sunt configurate

#### Frontend Security
- [ ] CSP headers sunt configurate
- [ ] Token-urile sunt stocate securizat
- [ ] Input sanitization este implementată
- [ ] Route protection funcționează
- [ ] Error handling nu expune informații sensibile
- [ ] Build-ul de producție nu conține dev tools
- [ ] Dependencies sunt actualizate și fără vulnerabilități
- [ ] HTTPS redirect este configurat

#### Infrastructure Security
- [ ] Firewall rules sunt configurate
- [ ] Database access este restricționat
- [ ] SSL certificates sunt valide
- [ ] Backup și recovery sunt testate
- [ ] Monitoring și logging sunt centralizate
- [ ] Incident response plan este documentat

---

*Ultima actualizare: Ianuarie 2025*