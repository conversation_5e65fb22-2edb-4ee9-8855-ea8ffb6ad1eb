import {
  MagnifyingGlassIcon,
  UserIcon,
  EllipsisVerticalIcon,
  NoSymbolIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CreditCardIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import Badge from '../../components/ui/Badge';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Dropdown from '../../components/ui/Dropdown';
import Input from '../../components/ui/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import Pagination from '../../components/ui/Pagination';
import { DataTable } from '../../components/ui/Table';
import UserAvatar from '../../components/ui/UserAvatar';
import { useUsers, useUserDetails, useBlockUser, useUnblockUser } from '../../hooks/useAdminData';

import { type User, type Subscription, type Plan } from '../../types';
// import { formatDate } from '../../utils/helpers'; // Not used currently
import { format, formatDistanceToNow } from 'date-fns';
import { ro } from 'date-fns/locale';

// Funcție locală pentru formatarea monedei
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('ro-RO', {
    style: 'currency',
    currency: 'RON',
    minimumFractionDigits: 2,
  }).format(amount);
};

// Interfețe pentru tipizarea datelor
interface UserWithDetails extends Omit<User, 'subscription'> {
  subscription?: Subscription & {
    plan: Plan;
  };
  totalRevenue?: number;
  totalExpenses?: number;
  totalExpenseAmount?: number;
  status?: 'active' | 'blocked' | 'pending';
  lastActiveAt?: string;
}

// interface UsersData { // Not used currently
//   users: UserWithDetails[];
//   total: number;
//   page: number;
//   totalPages: number;
// }

const UsersList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [planFilter, setPlanFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedUser, setSelectedUser] = useState<UserWithDetails | null>(null);
  const [showUserModal, setShowUserModal] = useState<boolean>(false);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [actionType, setActionType] = useState<string>('');

  const itemsPerPage = 10;

  // Hook-uri pentru gestionarea utilizatorilor
  const {
    data: usersData,
    isLoading,
    error,
  } = useUsers({
    search: searchTerm,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    plan: planFilter !== 'all' ? planFilter : undefined,
    limit: itemsPerPage,
  });

  const { data: _userDetails } = useUserDetails(selectedUser?.id);

  const blockUser = useBlockUser();
  const unblockUser = useUnblockUser();

  const handleUserAction = (user: UserWithDetails, action: string): void => {
    setSelectedUser(user);
    setActionType(action);
    setShowConfirmModal(true);
  };

  const confirmAction = () => {
    if (selectedUser && actionType && selectedUser?.id) {
      if (actionType === 'block') {
        blockUser.mutate({ userId: selectedUser.id, reason: '' });
      } else if (actionType === 'unblock') {
        unblockUser.mutate(selectedUser.id);
      }
      setShowConfirmModal(false);
      setSelectedUser(null);
    }
  };

  const getStatusBadge = (status: string): JSX.Element => {
    const statusConfig: Record<string, { variant: unknown; label: string }> = {
      active: { variant: 'default', label: 'Activ' },
      inactive: { variant: 'secondary', label: 'Inactiv' },
      blocked: { variant: 'destructive', label: 'Blocat' },
      pending: { variant: 'outline', label: 'În așteptare' },
    };

    const config = statusConfig[status] || statusConfig['inactive'];
    return (
      <Badge variant={(config?.variant as any) || 'secondary'}>
        {config?.label || 'Necunoscut'}
      </Badge>
    );
  };

  const getPlanBadge = (plan?: string): JSX.Element => {
    const planConfig: Record<string, { variant: unknown; label: string }> = {
      free: { variant: 'secondary', label: 'Gratuit' },
      basic: { variant: 'default', label: 'Basic' },
      premium: { variant: 'default', label: 'Premium' },
      enterprise: { variant: 'outline', label: 'Enterprise' },
    };

    const config = planConfig[plan?.toLowerCase() ?? 'free'] ?? planConfig['free'];
    return (
      <Badge variant={(config?.variant as any) || 'secondary'}>
        {config?.label || 'Necunoscut'}
      </Badge>
    );
  };

  const columns: any[] = [
    {
      key: 'user',
      title: 'Utilizator',
      render: (user: UserWithDetails) => {
        if (!user) {
          return (
            <div className="flex items-center">
              <div className="text-sm text-gray-500">Date indisponibile</div>
            </div>
          );
        }

        return (
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserAvatar
                user={{
                  firstName: user.firstName || undefined,
                  lastName: user.lastName || undefined,
                  avatar: user.avatar || undefined,
                  subscription: user.subscription || undefined,
                }}
                size="sm"
              />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900">
                {`${user.firstName || ''} ${user.lastName || ''}`}
              </div>
              <div className="text-sm text-gray-500">{user.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      key: 'plan',
      title: 'Plan',
      render: (user: UserWithDetails) => {
        if (!user) return getPlanBadge('N/A');
        return getPlanBadge(user.subscription?.plan?.name ?? 'N/A');
      },
    },
    {
      key: 'status',
      title: 'Status',
      render: (user: UserWithDetails) => {
        if (!user) return getStatusBadge('inactive');
        const status = user.status || 'active';
        return getStatusBadge(status);
      },
    },
    {
      key: 'revenue',
      title: 'Venituri',
      render: (user: UserWithDetails) => {
        if (!user) {
          return (
            <div className="text-sm">
              <div className="font-medium text-gray-900">-</div>
              <div className="text-gray-500">-</div>
            </div>
          );
        }

        return (
          <div className="text-sm">
            <div className="font-medium text-gray-900">
              {formatCurrency(user.totalRevenue ?? 0)}
            </div>
            <div className="text-gray-500">
              {user.subscription?.plan?.price
                ? `${formatCurrency(user.subscription.plan.price)}/lună`
                : 'Gratuit'}
            </div>
          </div>
        );
      },
    },
    {
      key: 'joinDate',
      title: 'Data înregistrării',
      render: (user: UserWithDetails) => {
        if (!user) return '-';
        return user.createdAt ? format(new Date(user.createdAt), 'dd/MM/yyyy') : '-';
      },
    },
    {
      key: 'lastActive',
      title: 'Ultima activitate',
      render: (user: UserWithDetails) => {
        if (!user) return <span className="text-sm text-gray-400">-</span>;

        return user.lastActiveAt ? (
          <span className="text-sm text-gray-600">
            {formatDistanceToNow(new Date(user.lastActiveAt), { addSuffix: true, locale: ro })}
          </span>
        ) : (
          <span className="text-sm text-gray-400">Niciodată</span>
        );
      },
    },
    {
      key: 'actions',
      title: 'Acțiuni',
      render: (user: UserWithDetails) => (
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm">
              <EllipsisVerticalIcon className="h-4 w-4" />
            </Button>
          }
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedUser(user);
              setShowUserModal(true);
            }}
            className="w-full justify-start"
          >
            <UserIcon className="h-4 w-4 mr-2" />
            Vezi detalii
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              handleUserAction(user, (user.status || 'active') === 'blocked' ? 'unblock' : 'block')
            }
            className={`w-full justify-start ${
              (user.status || 'active') === 'blocked' ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {(user.status || 'active') === 'blocked' ? (
              <CheckCircleIcon className="h-4 w-4 mr-2" />
            ) : (
              <NoSymbolIcon className="h-4 w-4 mr-2" />
            )}
            {(user.status || 'active') === 'blocked' ? 'Deblochează' : 'Blochează'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleUserAction(user, 'manage-subscription')}
            className="w-full justify-start"
          >
            <CreditCardIcon className="h-4 w-4 mr-2" />
            Gestionează abonament
          </Button>
        </Dropdown>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">Eroare la încărcarea utilizatorilor</div>
      </Card>
    );
  }

  const users = usersData?.pages?.flatMap(page => page.data) || [];
  const totalPages = usersData?.pages?.[0]?.pagination?.totalPages || 1;

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Gestionarea utilizatorilor</h3>
          <div className="text-sm text-gray-500">
            {usersData?.pages?.[0]?.pagination?.total || 0} utilizatori
          </div>
        </div>

        {/* Filtre și căutare */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Caută după nume sau email..."
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
            />
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                setStatusFilter(e.target.value)
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate statusurile</option>
              <option value="active">Activ</option>
              <option value="inactive">Inactiv</option>
              <option value="blocked">Blocat</option>
            </select>
            <select
              value={planFilter}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setPlanFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate planurile</option>
              <option value="free">Gratuit</option>
              <option value="basic">Basic</option>
              <option value="premium">Premium</option>
              <option value="enterprise">Enterprise</option>
            </select>
          </div>
        </div>

        {/* Tabel utilizatori */}
        <DataTable columns={columns} data={users} emptyMessage="Nu au fost găsiți utilizatori" />

        {/* Paginare */}
        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </Card>

      {/* Modal detalii utilizator */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title="Detalii utilizator"
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nume complet</label>
                <p className="text-sm text-gray-900">
                  {`${selectedUser?.firstName || ''} ${selectedUser?.lastName || ''}` || 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <p className="text-sm text-gray-900">{selectedUser?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                {getStatusBadge(selectedUser?.status || 'active')}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plan curent</label>
                {getPlanBadge(selectedUser?.subscription?.plan?.name)}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data înregistrării
                </label>
                <p className="text-sm text-gray-900">
                  {selectedUser?.createdAt
                    ? new Date(selectedUser.createdAt).toLocaleDateString('ro-RO')
                    : 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ultima activitate
                </label>
                <p className="text-sm text-gray-900">
                  {selectedUser?.lastActiveAt
                    ? new Date(selectedUser.lastActiveAt).toLocaleDateString('ro-RO')
                    : 'Niciodată'}
                </p>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Informații suplimentare</h4>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Monedă preferată
                  </label>
                  <p className="text-sm text-gray-900">{selectedUser?.currency || 'USD'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Fus orar</label>
                  <p className="text-sm text-gray-900">{selectedUser?.timezone || 'UTC'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email verificat
                  </label>
                  <p className="text-sm text-gray-900">
                    {selectedUser?.emailVerified ? 'Da' : 'Nu'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Limită cheltuieli lunare
                  </label>
                  <p className="text-sm text-gray-900">
                    {selectedUser?.monthlyExpenseCount || 0} /{' '}
                    {selectedUser?.monthlyExpenseLimit || 50}
                  </p>
                </div>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Statistici</h4>
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {selectedUser?.totalExpenses || 0}
                  </p>
                  <p className="text-xs text-gray-500">Cheltuieli</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedUser?.totalExpenseAmount || 0)}
                  </p>
                  <p className="text-xs text-gray-500">Suma cheltuieli</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedUser?.totalRevenue || 0)}
                  </p>
                  <p className="text-xs text-gray-500">Venituri generate</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {selectedUser?.loginCount || 0}
                  </p>
                  <p className="text-xs text-gray-500">Autentificări</p>
                </div>
              </div>
            </div>

            {selectedUser?.subscription && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Detalii abonament</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status abonament
                    </label>
                    <p className="text-sm text-gray-900">
                      {selectedUser?.subscription.status || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Perioada curentă
                    </label>
                    <p className="text-sm text-gray-900">
                      {selectedUser?.subscription.currentPeriodStart &&
                      selectedUser?.subscription.currentPeriodEnd
                        ? `${new Date(selectedUser.subscription.currentPeriodStart).toLocaleDateString('ro-RO')} - ${new Date(selectedUser.subscription.currentPeriodEnd).toLocaleDateString('ro-RO')}`
                        : 'N/A'}
                    </p>
                  </div>
                  {selectedUser?.subscription.currentPeriodStart &&
                    selectedUser?.subscription.currentPeriodEnd && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Perioada de facturare
                        </label>
                        <p className="text-sm text-gray-900">
                          {`${new Date(selectedUser?.subscription.currentPeriodStart).toLocaleDateString('ro-RO')} - ${new Date(selectedUser?.subscription.currentPeriodEnd).toLocaleDateString('ro-RO')}`}
                        </p>
                      </div>
                    )}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Modal confirmare acțiune */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirmă acțiunea"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            <p className="text-sm text-gray-700">
              Ești sigur că vrei să{' '}
              {actionType === 'block'
                ? 'blochezi'
                : actionType === 'unblock'
                  ? 'deblochezi'
                  : 'modifici'}{' '}
              acest utilizator?
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="secondary" onClick={() => setShowConfirmModal(false)}>
              Anulează
            </Button>
            <Button
              variant={actionType === 'block' ? 'danger' : 'primary'}
              onClick={confirmAction}
              disabled={blockUser.isPending || unblockUser.isPending}
            >
              {blockUser.isPending || unblockUser.isPending ? 'Se procesează...' : 'Confirmă'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default UsersList;
