import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  ClockIcon,
  UserGroupIcon,
  CheckIcon,
  StarIcon,
  ChevronDownIcon,
  BellIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import Button from '../components/ui/Button';
import { Select } from '../components/ui/Dropdown';
import { useLanguage } from '../hooks/useLanguage';
import { useAuthStore } from '../store/authStore';

const Landing: React.FC = () => {
  const { t } = useTranslation();
  const { currentLanguage, setLanguage } = useLanguage();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [showUserMenu, setShowUserMenu] = useState<boolean>(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Închide meniul utilizatorului când se face click în afara acestuia
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Opțiuni pentru selectorul de limbă
  const languageOptions = [
    { value: 'ro', label: 'Română' },
    { value: 'en', label: 'English' },
  ];

  const features = [
    {
      icon: ChartBarIcon,
      title: t('landing.features.items.analytics.title'),
      description: t('landing.features.items.analytics.description'),
    },
    {
      icon: CurrencyDollarIcon,
      title: t('landing.features.items.categories.title'),
      description: t('landing.features.items.categories.description'),
    },
    {
      icon: ShieldCheckIcon,
      title: t('landing.features.items.security.title'),
      description: t('landing.features.items.security.description'),
    },
    {
      icon: DevicePhoneMobileIcon,
      title: t('landing.features.items.mobile.title'),
      description: t('landing.features.items.mobile.description'),
    },
    {
      icon: ClockIcon,
      title: t('landing.features.items.realtime.title'),
      description: t('landing.features.items.realtime.description'),
    },
    {
      icon: UserGroupIcon,
      title: t('landing.features.items.sharing.title'),
      description: t('landing.features.items.sharing.description'),
    },
  ];

  const testimonials = [
    {
      name: t('landing.testimonials.items.maria.name'),
      role: t('landing.testimonials.items.maria.role'),
      content: t('landing.testimonials.items.maria.content'),
      rating: 5,
    },
    {
      name: t('landing.testimonials.items.alexandru.name'),
      role: t('landing.testimonials.items.alexandru.role'),
      content: t('landing.testimonials.items.alexandru.content'),
      rating: 5,
    },
    {
      name: t('landing.testimonials.items.elena.name'),
      role: t('landing.testimonials.items.elena.role'),
      content: t('landing.testimonials.items.elena.content'),
      rating: 5,
    },
  ];

  const pricingPlans = [
    {
      name: t('landing.pricing.plans.free.name'),
      price: '0',
      period: t('landing.pricing.plans.free.period'),
      features: [
        t('landing.pricing.plans.free.features.transactions'),
        t('landing.pricing.plans.free.features.categories'),
        t('landing.pricing.plans.free.features.reports'),
        t('landing.pricing.plans.free.features.support'),
      ],
      popular: false,
    },
    {
      name: t('landing.pricing.plans.pro.name'),
      price: '29',
      period: t('landing.pricing.plans.pro.period'),
      features: [
        t('landing.pricing.plans.pro.features.transactions'),
        t('landing.pricing.plans.pro.features.categories'),
        t('landing.pricing.plans.pro.features.reports'),
        t('landing.pricing.plans.pro.features.export'),
        t('landing.pricing.plans.pro.features.support'),
        t('landing.pricing.plans.pro.features.backup'),
      ],
      popular: true,
    },
    {
      name: t('landing.pricing.plans.business.name'),
      price: '99',
      period: t('landing.pricing.plans.business.period'),
      features: [
        t('landing.pricing.plans.business.features.allPro'),
        t('landing.pricing.plans.business.features.multipleAccounts'),
        t('landing.pricing.plans.business.features.apiAccess'),
        t('landing.pricing.plans.business.features.integrations'),
        t('landing.pricing.plans.business.features.manager'),
        t('landing.pricing.plans.business.features.sla'),
      ],
      popular: false,
    },
  ];

  const stats = [
    {
      number: t('landing.stats.activeUsers.number'),
      label: t('landing.stats.activeUsers.label'),
    },
    {
      number: t('landing.stats.expensesTracked.number'),
      label: t('landing.stats.expensesTracked.label'),
    },
    {
      number: t('landing.stats.averageSavings.number'),
      label: t('landing.stats.averageSavings.label'),
    },
    {
      number: t('landing.stats.uptime.number'),
      label: t('landing.stats.uptime.label'),
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                FinanceFlow
              </span>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('landing.header.features')}
              </a>
              <a
                href="#testimonials"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('landing.header.testimonials')}
              </a>
              <a href="#pricing" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('landing.header.pricing')}
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <Select
                value={languageOptions.find(opt => opt.value === currentLanguage)}
                onChange={(option: unknown) =>
                  setLanguage((option as { value: string; label: string }).value)
                }
                options={languageOptions}
                className="w-32"
                buttonClassName="bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md"
                optionClassName="hover:bg-blue-50 font-medium"
                menuClassName="border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg"
                offset={0}
              />

              {isAuthenticated ? (
                // Meniu pentru utilizatori autentificați
                <>
                  {/* Notificări */}
                  <div className="relative">
                    <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <BellIcon className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Meniu utilizator */}
                  <div className="relative" ref={userMenuRef}>
                    <button
                      onClick={() => setShowUserMenu(!showUserMenu)}
                      className="flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <UserIcon className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-medium">{user?.firstName}</span>
                      <ChevronDownIcon className="w-4 h-4" />
                    </button>

                    {/* Dropdown meniu */}
                    {showUserMenu && (
                      <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        <div className="px-4 py-3 border-b border-gray-100">
                          <p className="text-sm font-medium text-gray-900">
                            {user?.firstName} {user?.lastName}
                          </p>
                          <p className="text-sm text-gray-500">{user?.email}</p>
                        </div>

                        <Link
                          to="/app/dashboard"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <ChartBarIcon className="w-4 h-4 mr-3" />
                          Dashboard
                        </Link>

                        <Link
                          to="/app/profile"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <UserIcon className="w-4 h-4 mr-3" />
                          Profil
                        </Link>

                        <Link
                          to="/app/settings"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <Cog6ToothIcon className="w-4 h-4 mr-3" />
                          Setări
                        </Link>

                        <div className="border-t border-gray-100 mt-2 pt-2">
                          <button
                            onClick={() => {
                              logout();
                              setShowUserMenu(false);
                            }}
                            className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                          >
                            <ArrowRightOnRectangleIcon className="w-4 h-4 mr-3" />
                            Deconectare
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                // Meniu pentru utilizatori neautentificați
                <>
                  <Link to="/login" className="text-gray-600 hover:text-blue-600 transition-colors">
                    {t('landing.header.login')}
                  </Link>
                  <Link to="/register">
                    <Button variant="primary" size="sm">
                      {t('landing.header.register')}
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 pt-20 pb-32 overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000" />
          <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-fade-in-up">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {t('landing.hero.title.part1')}
                </span>
                <span className="block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                  {t('landing.hero.title.part2')}
                </span>
              </h1>
            </div>
            <div className="animate-fade-in-up animation-delay-200">
              <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                {t('landing.hero.subtitle')}
              </p>
            </div>
            <div className="animate-fade-in-up animation-delay-400 flex flex-row gap-4 justify-center flex-wrap">
              <Link to="/register">
                <Button
                  variant="primary"
                  size="lg"
                  className="w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {t('landing.hero.cta.primary')}
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 hover:shadow-lg"
              >
                {t('landing.hero.cta.secondary')}
              </Button>
            </div>
            <p className="text-sm text-gray-500 mt-4">{t('landing.hero.features')}</p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('landing.features.title')}</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t('landing.features.subtitle')}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={index}
                  className="group p-6 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            {stats.map((stat, index) => (
              <div key={index} className="group">
                <div className="text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  {stat.number}
                </div>
                <div className="text-blue-100 text-lg font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('landing.testimonials.title')}
            </h2>
            <p className="text-xl text-gray-600">{t('landing.testimonials.subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 border border-gray-100"
              >
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-current group-hover:scale-110 transition-transform duration-300"
                      style={{ animationDelay: `${i * 100}ms` }}
                    />
                  ))}
                </div>
                <p className="text-gray-700 mb-6 italic text-lg leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.role}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section
        id="pricing"
        className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden"
      >
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" />
          <div className="absolute bottom-20 left-20 w-64 h-64 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('landing.pricing.title')}
            </h2>
            <p className="text-xl text-gray-600">{t('landing.pricing.subtitle')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`group relative p-8 rounded-2xl border-2 transition-all duration-500 hover:-translate-y-2 ${
                  plan.popular
                    ? 'border-blue-500 shadow-2xl scale-105 bg-gradient-to-b from-blue-50 to-white'
                    : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-xl'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg">
                      {t('landing.pricing.popular')}
                    </span>
                  </div>
                )}
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                    {plan.name}
                  </h3>
                  <div className="flex items-center justify-center">
                    <span className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 ml-2 text-lg">
                      {t('landing.pricing.currency')} {plan.period}
                    </span>
                  </div>
                </div>
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center group-hover:translate-x-1 transition-transform duration-300"
                      style={{ animationDelay: `${featureIndex * 100}ms` }}
                    >
                      <CheckIcon className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link to="/register">
                  <Button
                    variant={plan.popular ? 'primary' : 'outline'}
                    size="lg"
                    className="w-full transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    {plan.price === '0'
                      ? t('landing.pricing.buttons.free')
                      : t('landing.pricing.buttons.choose')}
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse" />
          <div className="absolute bottom-10 right-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse animation-delay-2000" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full filter blur-3xl" />
        </div>
        <div className="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            {t('landing.cta.title')}
          </h2>
          <p className="text-lg md:text-xl text-blue-100 mb-10 leading-relaxed max-w-2xl mx-auto">
            {t('landing.cta.subtitle')}
          </p>
          <div className="flex justify-center mb-8">
            <Link to="/register">
              <Button
                variant="white"
                size="lg"
                className="whitespace-nowrap transform hover:scale-110 transition-all duration-300 shadow-2xl hover:shadow-3xl"
              >
                {t('landing.cta.button')}
              </Button>
            </Link>
          </div>
          <p className="text-blue-100 text-base md:text-lg font-medium">
            {t('landing.cta.features')}
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">FinanceFlow</span>
              </div>
              <p className="text-gray-400">{t('landing.footer.description')}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.product.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/features" className="hover:text-white transition-colors">
                    {t('landing.footer.product.features')}
                  </Link>
                </li>
                <li>
                  <Link to="/pricing" className="hover:text-white transition-colors">
                    {t('landing.footer.product.pricing')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.product.api')}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.product.integrations')}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.support.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/documentation" className="hover:text-white transition-colors">
                    {t('landing.footer.support.documentation')}
                  </Link>
                </li>
                <li>
                  <Link to="/help" className="hover:text-white transition-colors">
                    {t('landing.footer.support.guides')}
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="hover:text-white transition-colors">
                    {t('landing.footer.support.contact')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.support.status')}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">{t('landing.footer.legal.title')}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/terms" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.terms')}
                  </Link>
                </li>
                <li>
                  <Link to="/privacy" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.privacy')}
                  </Link>
                </li>
                <li>
                  <Link to="/cookies" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.cookies')}
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {t('landing.footer.legal.gdpr')}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>{t('landing.footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
