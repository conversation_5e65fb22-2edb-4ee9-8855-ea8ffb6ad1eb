const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const { InjectManifest } = require('workbox-webpack-plugin');

const isProduction = process.env['NODE_ENV'] === 'production';
const isDevelopment = !isProduction;

module.exports = {
  mode: process.env['NODE_ENV'] || 'development',
  entry: {
    main: './src/main.tsx',
    sw: './src/sw.ts',
  },
  // Memory optimization
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
  },
  stats: {
    preset: 'minimal',
    moduleTrace: false,
    errorDetails: false,
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: isProduction ? 'js/[name].[contenthash:8].js' : 'js/[name].js',
    chunkFilename: isProduction ? 'js/[name].[contenthash:8].chunk.js' : 'js/[name].chunk.js',
    assetModuleFilename: 'assets/[name].[contenthash:8][ext]',
    clean: true,
    publicPath: '/',
    crossOriginLoading: 'anonymous',
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/store': path.resolve(__dirname, 'src/store'),
      '@/config': path.resolve(__dirname, 'src/config'),
      '@/assets': path.resolve(__dirname, 'src/assets'),
    },
    fullySpecified: false,
    fallback: {
      crypto: false,
      stream: false,
      assert: false,
      http: false,
      https: false,
      os: false,
      url: false,
    },
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'ts-loader',
          options: {
            transpileOnly: true,
            experimentalWatchApi: true,
            compilerOptions: {
              skipLibCheck: true,
              incremental: true,
            },
          },
        },
      },
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { targets: 'defaults' }],
              ['@babel/preset-react', { runtime: 'automatic' }],
            ],
            parserOpts: {
              strictMode: false,
              allowImportExportEverywhere: true,
              allowReturnOutsideFunction: true,
            },
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024, // 8kb
          },
        },
        generator: {
          filename: 'images/[name].[hash][ext]',
        },
        use: [
          {
            loader: 'image-webpack-loader',
            options: {
              disable: process.env['NODE_ENV'] === 'development',
              mozjpeg: {
                enabled: false,
              },
              optipng: {
                enabled: false,
              },
              pngquant: {
                enabled: false,
              },
              gifsicle: {
                enabled: false,
              },
              webp: {
                enabled: false,
              },
            },
          },
        ],
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './index.html',
      inject: true,
    }),
    new webpack.DefinePlugin({
      'process.env['NODE_ENV']': JSON.stringify(process.env['NODE_ENV'] || 'development'),
      'process.env.REACT_APP_API_URL': JSON.stringify(
        process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
      ),
      'process.env.REACT_APP_API_BASE_URL': JSON.stringify(
        process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000',
      ),
      'process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY': JSON.stringify(
        process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '',
      ),
      'process.env.REACT_APP_APP_NAME': JSON.stringify(
        process.env.REACT_APP_APP_NAME || 'FinanceApp',
      ),
      'process.env.REACT_APP_VERSION': JSON.stringify(
        process.env.REACT_APP_VERSION || '1.0.0',
      ),
      'process.env.REACT_APP_DEBUG': JSON.stringify(
        process.env.REACT_APP_DEBUG || 'false',
      ),
    }),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'public',
          to: '.',
          globOptions: {
            ignore: ['**/index.html'],
          },
        },
      ],
    }),
    isProduction && new InjectManifest({
      swSrc: './src/sw.ts',
      swDest: 'sw.js',
      exclude: [/\.map$/, /manifest\.json$/, /LICENSE/],
    }),
    isProduction && new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
    }),
    process.env.ANALYZE_BUNDLE && new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: 'bundle-report.html',
    }),
  ].filter(Boolean),
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist'),
    },
    host: '0.0.0.0',
    port: 5173,
    hot: true,
    historyApiFallback: true,
    allowedHosts: 'all',
    // Memory optimization settings
    devMiddleware: {
      writeToDisk: false,
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    proxy: [
      {
        context: ['/api'],
        target: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    ],
  },
  optimization: {
    minimize: isProduction,
    minimizer: isProduction
      ? [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ['console.log', 'console.info', 'console.debug'],
              },
              mangle: {
                safari10: true,
              },
              format: {
                comments: false,
              },
            },
            extractComments: false,
          }),
        ]
      : [],
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: 10,
          chunks: 'all',
        },
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          priority: 20,
          chunks: 'all',
        },
        router: {
          test: /[\\/]node_modules[\\/]react-router-dom[\\/]/,
          name: 'router',
          priority: 15,
          chunks: 'all',
        },
        query: {
          test: /[\\/]node_modules[\\/]@tanstack[\\/]react-query[\\/]/,
          name: 'query',
          priority: 15,
          chunks: 'all',
        },
        ui: {
          test: /[\\/]node_modules[\\/](@headlessui|@heroicons)[\\/]/,
          name: 'ui',
          priority: 15,
          chunks: 'all',
        },
        charts: {
          test: /[\\/]node_modules[\\/](chart\.js|react-chartjs-2)[\\/]/,
          name: 'charts',
          priority: 15,
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          priority: 5,
          chunks: 'all',
          reuseExistingChunk: true,
        },
      },
    },
    runtimeChunk: {
      name: 'runtime',
    },
    usedExports: true,
    sideEffects: false,
  },
  performance: {
    hints: process.env['NODE_ENV'] === 'production' ? 'warning' : false,
    maxAssetSize: 512000,
    maxEntrypointSize: 512000,
  },
};
