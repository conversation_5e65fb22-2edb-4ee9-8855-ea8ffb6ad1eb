generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                             String              @id @default(cuid())
  email                          String              @unique
  password                       String
  firstName                      String
  lastName                       String
  avatar                         String?
  role                           UserRole            @default(user)
  currency                       String              @default("USD") @db.VarChar(3)
  timezone                       String              @default("UTC")
  isActive                       Boolean             @default(true) @map("is_active")
  emailVerified                  Boolean             @default(false) @map("email_verified")
  emailVerificationToken         String?             @map("email_verification_token")
  passwordResetToken             String?             @map("password_reset_token")
  passwordResetExpires           DateTime?           @map("password_reset_expires")
  lastLogin                      DateTime?           @map("last_login")
  loginCount                     Int                 @default(0) @map("login_count")
  preferences                    Json?               @default("{\"theme\": \"light\", \"dashboard\": {\"show_trends\": true, \"default_period\": \"month\", \"show_categories\": true}, \"notifications\": {\"push\": false, \"email\": true, \"monthly_report\": true, \"weekly_summary\": true}}")
  createdAt                      DateTime            @default(now()) @map("created_at")
  updatedAt                      DateTime            @updatedAt @map("updated_at")
  lastUsageReset                 DateTime            @default(now()) @map("last_usage_reset")
  monthlyExpenseCount            Int                 @default(0) @map("monthly_expense_count")
  monthlyExpenseLimit            Int                 @default(50) @map("monthly_expense_limit")
  planType                       PlanType            @default(free) @map("plan_type")
  stripeCustomerId               String?             @unique @map("stripe_customer_id")
  subscriptionCurrentPeriodEnd   DateTime?           @map("subscription_current_period_end")
  subscriptionCurrentPeriodStart DateTime?           @map("subscription_current_period_start")
  subscriptionId                 String?             @unique @map("subscription_id")
  subscriptionStatus             SubscriptionStatus? @map("subscription_status")
  trialEndsAt                    DateTime?           @map("trial_ends_at")
  refreshToken                   String?             @map("refresh_token")
  categories                     Category[]
  expenses                       Expense[]
  subscriptions                  Subscription?
  usageLogs                      UsageLog[]          @relation("UserUsageLogs")

  @@index([emailVerified])
  @@index([isActive])
  @@map("users")
}

model Category {
  id           String       @id @default(cuid())
  name         String
  description  String?
  color        String       @default("#3B82F6") @db.VarChar(7)
  icon         String       @default("shopping-bag")
  budgetLimit  Decimal?     @map("budget_limit") @db.Decimal(10, 2)
  budgetPeriod BudgetPeriod @default(monthly) @map("budget_period")
  isActive     Boolean      @default(true) @map("is_active")
  isDefault    Boolean      @default(false) @map("is_default")
  sortOrder    Int          @default(0) @map("sort_order")
  userId       String       @map("user_id")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  expenses     Expense[]

  @@unique([userId, name])
  @@index([isActive])
  @@index([sortOrder])
  @@index([userId])
  @@map("categories")
}

model Expense {
  id                 String              @id @default(cuid())
  amount             Decimal             @db.Decimal(10, 2)
  description        String
  date               DateTime            @db.Date
  notes              String?
  paymentMethod      PaymentMethod       @default(card) @map("payment_method")
  location           String?
  receiptUrl         String?             @map("receipt_url")
  tags               Json?               @default("[]")
  isRecurring        Boolean             @default(false) @map("is_recurring")
  recurringFrequency RecurringFrequency? @map("recurring_frequency")
  recurringEndDate   DateTime?           @map("recurring_end_date") @db.Date
  originalExpenseId  String?             @map("original_expense_id")
  userId             String              @map("user_id")
  categoryId         String              @map("category_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  category           Category            @relation(fields: [categoryId], references: [id])
  originalExpense    Expense?            @relation("ExpenseToOriginal", fields: [originalExpenseId], references: [id])
  recurringExpenses  Expense[]           @relation("ExpenseToOriginal")
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([amount])
  @@index([date])
  @@index([isRecurring])
  @@index([paymentMethod])
  @@index([categoryId])
  @@index([userId, categoryId])
  @@index([userId, date])
  @@index([userId])
  @@map("expenses")
}

model Plan {
  id            String         @id @default(cuid())
  stripeId      String         @unique @map("stripe_id")
  name          String
  description   String?
  price         Decimal        @db.Decimal(10, 2)
  currency      String         @default("USD") @db.VarChar(3)
  interval      String
  features      Json           @default("{}")
  limits        Json           @default("{}")
  isActive      Boolean        @default(true) @map("is_active")
  sortOrder     Int            @default(0) @map("sort_order")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  subscriptions Subscription[]

  @@index([isActive])
  @@index([sortOrder])
  @@map("plans")
}

model Subscription {
  id                 String             @id @default(cuid())
  stripeId           String             @unique @map("stripe_id")
  userId             String             @unique @map("user_id")
  planId             String             @map("plan_id")
  status             SubscriptionStatus
  currentPeriodStart DateTime           @map("current_period_start")
  currentPeriodEnd   DateTime           @map("current_period_end")
  trialStart         DateTime?          @map("trial_start")
  trialEnd           DateTime?          @map("trial_end")
  canceledAt         DateTime?          @map("canceled_at")
  endedAt            DateTime?          @map("ended_at")
  metadata           Json?              @default("{}")
  createdAt          DateTime           @default(now()) @map("created_at")
  updatedAt          DateTime           @updatedAt @map("updated_at")
  plan               Plan               @relation(fields: [planId], references: [id])
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([currentPeriodEnd])
  @@index([status])
  @@index([planId])
  @@index([userId])
  @@map("subscriptions")
}

model UsageLog {
  id         String   @id @default(cuid())
  userId     String   @map("user_id")
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  metadata   Json?    @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation("UserUsageLogs", fields: [userId], references: [id], onDelete: Cascade)

  @@index([action])
  @@index([createdAt])
  @@index([userId, createdAt])
  @@index([userId])
  @@map("usage_logs")
}

model WebhookEvent {
  id          String    @id @default(cuid())
  stripeId    String    @unique @map("stripe_id")
  type        String
  data        Json
  processed   Boolean   @default(false)
  processedAt DateTime? @map("processed_at")
  error       String?
  retryCount  Int       @default(0) @map("retry_count")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  @@index([createdAt])
  @@index([processed])
  @@index([stripeId])
  @@index([type])
  @@map("webhook_events")
}

enum UserRole {
  user
  admin
}

enum SubscriptionStatus {
  active
  canceled
  incomplete
  incomplete_expired
  past_due
  trialing
  unpaid
}

enum PlanType {
  free
  basic
  premium
}

enum BudgetPeriod {
  daily
  weekly
  monthly
  yearly
}

enum PaymentMethod {
  cash
  card
  bank_transfer
  digital_wallet
  check
  other
}

enum RecurringFrequency {
  daily
  weekly
  monthly
  yearly
}
