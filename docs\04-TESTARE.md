# 🧪 TESTARE ȘI CI/CD

## 📋 OVERVIEW TESTARE

### Strategia de Testare
1. **Unit Tests** - Testarea componentelor individuale
2. **Integration Tests** - Testarea interacțiunii între componente
3. **E2E Tests** - Testarea fluxurilor complete de utilizator
4. **Performance Tests** - Testarea performanței și scalabilității
5. **Security Tests** - Testarea vulnerabilităților de securitate

### Framework-uri Utilizate
- **Backend**: Jest + Supertest pentru API testing
- **Frontend**: Vitest + Testing Library pentru componente React
- **E2E**: Playwright pentru testare end-to-end
- **Performance**: Lighthouse CI pentru audit performanță
- **Security**: npm audit + Snyk pentru vulnerabilități

---

## 🔧 CONFIGURARE TESTARE

### Backend Testing Setup

#### Jest Configuration
```javascript
// backend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/types/**',
    '!src/config/**',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
};
```

#### Test Database Setup
```typescript
// tests/setup.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test.db',
    },
  },
});

beforeAll(async () => {
  await prisma.$executeRaw`PRAGMA foreign_keys = OFF`;
  await prisma.$executeRaw`DELETE FROM users`;
  await prisma.$executeRaw`DELETE FROM expenses`;
  await prisma.$executeRaw`DELETE FROM categories`;
  await prisma.$executeRaw`PRAGMA foreign_keys = ON`;
});

afterAll(async () => {
  await prisma.$disconnect();
});

export { prisma };
```

### Frontend Testing Setup

#### Vitest Configuration
```typescript
// frontend/vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
});
```

#### Testing Library Setup
```typescript
// src/tests/setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach } from 'vitest';

afterEach(() => {
  cleanup();
});
```

---

## 🧪 EXEMPLE DE TESTE

### Backend Unit Tests

#### Controller Tests
```typescript
// tests/controllers/expenseController.test.ts
import request from 'supertest';
import app from '../../src/app';
import { prisma } from '../setup';

describe('Expense Controller', () => {
  let authToken: string;
  let userId: string;

  beforeEach(async () => {
    // Create test user and get auth token
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test User',
      },
    });
    userId = user.id;
    
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });
    
    authToken = response.body.accessToken;
  });

  describe('POST /api/expenses', () => {
    it('should create a new expense', async () => {
      const expenseData = {
        amount: 25.50,
        description: 'Test expense',
        categoryId: 'category-id',
        expenseDate: '2024-01-15',
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.amount).toBe(25.50);
      expect(response.body.data.description).toBe('Test expense');
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        amount: -10, // Invalid negative amount
        description: '',
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });
});
```

#### Service Tests
```typescript
// tests/services/expenseService.test.ts
import { ExpenseService } from '../../src/services/expenseService';
import { prisma } from '../setup';

describe('ExpenseService', () => {
  const expenseService = new ExpenseService();

  describe('calculateMonthlyTotal', () => {
    it('should calculate correct monthly total', async () => {
      const userId = 'test-user-id';
      const month = '2024-01';

      // Create test expenses
      await prisma.expense.createMany({
        data: [
          {
            userId,
            amount: 100,
            description: 'Expense 1',
            categoryId: 'cat-1',
            expenseDate: new Date('2024-01-15'),
          },
          {
            userId,
            amount: 50,
            description: 'Expense 2',
            categoryId: 'cat-2',
            expenseDate: new Date('2024-01-20'),
          },
        ],
      });

      const total = await expenseService.calculateMonthlyTotal(userId, month);
      expect(total).toBe(150);
    });
  });
});
```

### Frontend Component Tests

#### Component Unit Tests
```typescript
// src/components/__tests__/ExpenseForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import ExpenseForm from '../ExpenseForm';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ExpenseForm', () => {
  const mockOnSubmit = vi.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('renders form fields correctly', () => {
    render(<ExpenseForm onSubmit={mockOnSubmit} />, {
      wrapper: createWrapper(),
    });

    expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<ExpenseForm onSubmit={mockOnSubmit} />, {
      wrapper: createWrapper(),
    });

    const submitButton = screen.getByRole('button', { name: /add expense/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/amount is required/i)).toBeInTheDocument();
      expect(screen.getByText(/description is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    render(<ExpenseForm onSubmit={mockOnSubmit} />, {
      wrapper: createWrapper(),
    });

    fireEvent.change(screen.getByLabelText(/amount/i), {
      target: { value: '25.50' },
    });
    fireEvent.change(screen.getByLabelText(/description/i), {
      target: { value: 'Test expense' },
    });

    const submitButton = screen.getByRole('button', { name: /add expense/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        amount: 25.50,
        description: 'Test expense',
        categoryId: expect.any(String),
        expenseDate: expect.any(String),
      });
    });
  });
});
```

#### Hook Tests
```typescript
// src/hooks/__tests__/useExpenses.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useExpenses } from '../useExpenses';
import * as expenseService from '../../services/expenseService';

vi.mock('../../services/expenseService');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useExpenses', () => {
  it('fetches expenses successfully', async () => {
    const mockExpenses = [
      {
        id: '1',
        amount: 25.50,
        description: 'Test expense',
        categoryId: 'cat-1',
        expenseDate: '2024-01-15',
      },
    ];

    vi.mocked(expenseService.getExpenses).mockResolvedValue({
      data: mockExpenses,
      total: 1,
    });

    const { result } = renderHook(() => useExpenses({}), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data?.data).toEqual(mockExpenses);
  });
});
```

---

## 🎭 E2E TESTING CU PLAYWRIGHT

### Configurare Playwright
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples
```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('user can login and logout', async ({ page }) => {
    await page.goto('/login');

    // Login
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'admin123');
    await page.click('[data-testid="login-button"]');

    // Verify successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();

    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');

    // Verify successful logout
    await expect(page).toHaveURL('/');
  });

  test('shows error for invalid credentials', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
  });
});
```

---

## 🚀 CI/CD PIPELINE

### GitHub Actions Workflow
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: expense_tracker_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
      
      - name: Install dependencies
        working-directory: backend
        run: npm ci
      
      - name: Run linting
        working-directory: backend
        run: npm run lint
      
      - name: Run type checking
        working-directory: backend
        run: npm run type-check
      
      - name: Run tests
        working-directory: backend
        run: npm run test:coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/expense_tracker_test
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: backend/coverage/lcov.info

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install dependencies
        working-directory: frontend
        run: npm ci
      
      - name: Run linting
        working-directory: frontend
        run: npm run lint
      
      - name: Run type checking
        working-directory: frontend
        run: npm run type-check
      
      - name: Run tests
        working-directory: frontend
        run: npm run test:coverage
      
      - name: Build application
        working-directory: frontend
        run: npm run build

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Start backend
        working-directory: backend
        run: |
          npm run build
          npm start &
          sleep 10
        env:
          NODE_ENV: test
          DATABASE_URL: file:./test.db
      
      - name: Start frontend
        working-directory: frontend
        run: |
          npm run build
          npm run preview &
          sleep 5
      
      - name: Run E2E tests
        run: npx playwright test
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security audit
        run: |
          cd backend && npm audit --audit-level high
          cd ../frontend && npm audit --audit-level high
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  deploy:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to production
        run: |
          # Add deployment steps here
          echo "Deploying to production..."
```

---

*Ultima actualizare: Ianuarie 2025*
