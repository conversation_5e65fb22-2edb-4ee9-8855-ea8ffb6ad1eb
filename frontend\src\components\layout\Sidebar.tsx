import {
  HomeIcon,
  CreditCardIcon,
  TagIcon,
  ChartBarIcon,
  UserIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CurrencyDollarIcon,
  DocumentChartBarIcon,
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  CreditCardIcon as CreditCardIconSolid,
  TagIcon as TagIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  UserIcon as UserIconSolid,
} from '@heroicons/react/24/solid';
import React from 'react';
import { Link, useLocation } from 'react-router-dom';

import { useAuthStore } from '../../store/authStore';
import { cn } from '../../utils/helpers';

interface SidebarProps {
  isOpen?: boolean;
  isCollapsed?: boolean;
  onClose: () => void;
  onCollapse: () => void;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  iconSolid: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  badge?: string | number;
  description?: string;
}

/**
 * Componenta Sidebar pentru navigația laterală
 */
const Sidebar: React.FC<SidebarProps> = ({
  isOpen = false,
  isCollapsed = false,
  onClose,
  onCollapse,
}) => {
  const { user } = useAuthStore();
  const location = useLocation();

  // Definirea elementelor de navigație bazat pe rolul utilizatorului
  const getNavigationItems = (): NavigationItem[] => {
    if (user?.role === 'admin') {
      return [
        {
          name: 'Dashboard Admin',
          href: '/app/admin/dashboard',
          icon: HomeIcon,
          iconSolid: HomeIconSolid,
          description: 'Panou administrare',
        },
        {
          name: 'Utilizatori',
          href: '/app/admin/users',
          icon: UserIcon,
          iconSolid: UserIconSolid,
          description: 'Gestionează utilizatorii',
        },
        {
          name: 'Abonamente',
          href: '/app/admin/subscriptions',
          icon: CreditCardIcon,
          iconSolid: CreditCardIconSolid,
          description: 'Gestionează abonamentele',
        },
        {
          name: 'Venituri',
          href: '/app/admin/revenue',
          icon: CurrencyDollarIcon,
          iconSolid: CurrencyDollarIcon,
          description: 'Analize financiare',
        },
        {
          name: 'Statistici',
          href: '/app/admin/stats',
          icon: ChartBarIcon,
          iconSolid: ChartBarIconSolid,
          description: 'Statistici detaliate',
        },
        {
          name: 'Activitate',
          href: '/app/admin/activity',
          icon: DocumentChartBarIcon,
          iconSolid: DocumentChartBarIcon,
          description: 'Monitorizare activitate',
        },
      ];
    }

    return [
      {
        name: 'Dashboard',
        href: '/app/dashboard',
        icon: HomeIcon,
        iconSolid: HomeIconSolid,
        description: 'Vedere generală',
      },
      {
        name: 'Cheltuieli',
        href: '/app/expenses',
        icon: CreditCardIcon,
        iconSolid: CreditCardIconSolid,
        description: 'Gestionează cheltuielile',
        badge: 'Nou',
      },
      {
        name: 'Categorii',
        href: '/app/categories',
        icon: TagIcon,
        iconSolid: TagIconSolid,
        description: 'Organizează categoriile',
      },
      {
        name: 'Rapoarte',
        href: '/app/reports',
        icon: ChartBarIcon,
        iconSolid: ChartBarIconSolid,
        description: 'Analize și statistici',
      },
    ];
  };

  const navigationItems = getNavigationItems();

  const getSecondaryItems = (): NavigationItem[] => {
    return [];
  };

  const secondaryItems = getSecondaryItems();

  // Verifică dacă o rută este activă
  const isActiveRoute = (href: string): boolean => {
    return (
      location.pathname === href || (href !== '/dashboard' && location.pathname.startsWith(href))
    );
  };

  // Componenta pentru un element de navigație
  const NavigationItem: React.FC<{ item: NavigationItem; isActive: boolean }> = ({
    item,
    isActive,
  }) => {
    const IconComponent = isActive ? item.iconSolid : item.icon;

    return (
      <Link
        to={item.href}
        onClick={() => onClose && onClose()}
        className={cn(
          'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
          isActive
            ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
          isCollapsed ? 'justify-center' : 'justify-start',
        )}
        title={isCollapsed ? item.name : ''}
      >
        <IconComponent
          className={cn(
            'flex-shrink-0 h-5 w-5 transition-colors',
            isActive ? 'text-primary-600' : 'text-gray-500 group-hover:text-gray-700',
            isCollapsed ? '' : 'mr-3',
          )}
        />

        {!isCollapsed && (
          <>
            <span className="flex-1 truncate">{item.name}</span>
            {item.badge && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-primary-100 text-primary-700 rounded-full">
                {item.badge}
              </span>
            )}
          </>
        )}

        {/* Tooltip pentru collapsed state */}
        {isCollapsed && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
            {item.name}
            {item.description && (
              <div className="text-gray-300 text-xs mt-1">{item.description}</div>
            )}
          </div>
        )}
      </Link>
    );
  };

  // Componenta pentru separatorul de secțiune
  const SectionSeparator: React.FC<{ title: string }> = ({ title }) => {
    if (isCollapsed) {
      return <div className="my-4 border-t border-gray-200" />;
    }

    return (
      <div className="my-4">
        <div className="flex items-center">
          <div className="flex-1 border-t border-gray-200" />
          <span className="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
            {title}
          </span>
          <div className="flex-1 border-t border-gray-200" />
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Sidebar */}
      <div
        className={cn(
          'fixed left-0 z-40 flex flex-col bg-white border-r border-gray-200 transition-all duration-300 ease-in-out',
          // Mobile and desktop styles - start below header
          'top-16 bottom-0 lg:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full',
          // Desktop styles
          isCollapsed ? 'lg:w-16' : 'lg:w-64',
          // Mobile width
          'w-64',
        )}
      >
        {/* Header - doar pe mobil */}
        <div
          className={cn(
            'flex items-center justify-between h-16 px-4 border-b border-gray-200 lg:hidden',
          )}
        >
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">ET</span>
            </div>
            <span className="font-semibold text-gray-900">Menu</span>
          </div>

          {/* Close button (mobile) */}
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            aria-label="Închide meniul"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Collapse button (desktop only) */}
        <div className="hidden lg:flex justify-end p-2">
          <button
            onClick={onCollapse}
            className="p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            aria-label={isCollapsed ? 'Extinde meniul' : 'Restrânge meniul'}
          >
            {isCollapsed ? (
              <ChevronRightIcon className="h-4 w-4" />
            ) : (
              <ChevronLeftIcon className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* User Info */}
        {!isCollapsed && (
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {user?.firstName?.charAt(0)?.toUpperCase() || 'U'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-gray-900 truncate">
                  {user ? `${user.firstName} ${user.lastName}` : 'Utilizator'}
                </p>
                <p className="text-sm text-gray-600 truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* User Info collapsed - doar pe desktop */}
        {isCollapsed && (
          <div className="hidden lg:flex justify-center p-2 border-b border-gray-200">
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.firstName?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
          {/* Primary Navigation */}
          <div className="space-y-1">
            {navigationItems.map(item => (
              <NavigationItem key={item.name} item={item} isActive={isActiveRoute(item.href)} />
            ))}
          </div>

          {/* Secondary Navigation - doar pentru admin */}
          {secondaryItems.length > 0 && (
            <>
              <SectionSeparator title="Admin" />
              <div className="space-y-1">
                {secondaryItems.map(item => (
                  <NavigationItem key={item.name} item={item} isActive={isActiveRoute(item.href)} />
                ))}
              </div>
            </>
          )}
        </nav>

        {/* Footer */}
        {!isCollapsed && (
          <div className="p-4 border-t border-gray-200">
            <div className="text-center">
              <p className="text-xs text-gray-500">Expense Tracker v1.0</p>
              <p className="text-xs text-gray-400 mt-1">© 2025 Toate drepturile rezervate</p>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

/**
 * Hook pentru controlul sidebar-ului
 * @returns {Object} - Stări și funcții pentru sidebar
 */
export const useSidebar = () => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isCollapsed, setIsCollapsed] = React.useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  const toggle = () => setIsOpen(!isOpen);
  const close = () => setIsOpen(false);
  const open = () => setIsOpen(true);

  const collapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return {
    isOpen,
    isCollapsed,
    toggle,
    close,
    open,
    collapse,
  };
};

export default Sidebar;
