import { HomeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { Link } from 'react-router-dom';

import { useRedirectUrl } from '../components/auth/PublicRoute';

const NotFound: React.FC = () => {
  const dashboardUrl = useRedirectUrl();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* Numărul 404 */}
          <h1 className="text-9xl font-bold text-primary-600 mb-4">
            404
          </h1>

          {/* Mesajul principal */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Pagina nu a fost găsită
          </h2>

          {/* Descrierea */}
          <p className="text-lg text-gray-600 mb-8">
            Ne pare rău, dar pagina pe care o căutați nu există sau a fost mutată.
          </p>

          {/* Butoane de acțiune */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to={dashboardUrl}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            >
              <HomeIcon className="w-5 h-5 mr-2" />
              Înapoi la Dashboard
            </Link>

            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5 mr-2" />
              Înapoi
            </button>
          </div>
        </div>

        {/* Ilustrație sau imagine opțională */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center justify-center w-32 h-32 bg-primary-100 rounded-full mb-4">
            <svg
              className="w-16 h-16 text-primary-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
          </div>

          <p className="text-sm text-gray-500">
            Dacă problema persistă, vă rugăm să contactați echipa de suport.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
