const { PrismaClient } = require('@prisma/client');
const { execSync } = require('child_process');
const { join } = require('path');

// Mock environment variables pentru teste
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
process.env['JWT_REFRESH_SECRET'] = 'test-refresh-secret-key-for-testing-only';
process.env['DATABASE_URL'] = '*********************************************************************';
process.env['REDIS_URL'] = 'redis://localhost:6379/1';

// Creează instanța Prisma pentru teste
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'],
    },
  },
});

// Setup și cleanup functions pentru Jest
const setupTestEnvironment = async () => {
  try {
    console.log('🔧 Setting up test environment...');
    await prisma.$connect();
    console.log('✅ Test environment setup completed');
  } catch (error) {
    console.error('❌ Error setting up test environment:', error);
    console.log('⚠️ Continuing with limited test functionality...');
  }
};

const teardownTestEnvironment = async () => {
  await cleanupDatabase();
  await prisma.$disconnect();
};

const cleanupAfterTest = async () => {
  await cleanupDatabase();
};

// Funcție pentru curățarea bazei de date
async function cleanupDatabase() {
  try {
    // Șterge toate înregistrările din tabele în ordinea corectă pentru a evita conflictele de foreign key
    await prisma.usageLog.deleteMany({});
    await prisma.webhookEvent.deleteMany({});
    await prisma.expense.deleteMany({});
    await prisma.category.deleteMany({});
    await prisma.subscription.deleteMany({});
    await prisma.subscriptionPlan.deleteMany({});
    await prisma.user.deleteMany({});
  } catch (error) {
    console.log('Error cleaning database:', error);
  }
}

// Mock pentru logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  stream: {
    write: jest.fn(),
  },
  logRequest: jest.fn(),
  logPerformance: jest.fn(),
  logAudit: jest.fn(),
  logSecurity: jest.fn(),
  logDatabase: jest.fn(),
  add: jest.fn(),
};

jest.mock('../src/utils/logger', () => ({
  default: mockLogger,
  __esModule: true,
}));

// Mock pentru Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ping: jest.fn().mockResolvedValue('PONG'),
    disconnect: jest.fn(),
  }));
});

// Mock pentru Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
    },
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    subscriptions: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
    prices: {
      list: jest.fn(),
    },
    accounts: {
      retrieve: jest.fn(),
    },
  }));
});

// Helper functions pentru teste (mock)
const createTestUser = async (overrides: Partial<any> = {}) => {
  const uniqueId = Date.now() + Math.random().toString(36).substr(2, 9);
  const now = new Date();
  
  // Returnează un obiect mock în loc să acceseze baza de date
  return {
    id: `test-user-${uniqueId}`,
    email: `test-${uniqueId}@example.com`,
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedpassword',
    role: 'user',
    currency: 'USD',
    timezone: 'UTC',
    isActive: true,
    emailVerified: true,
    loginCount: 0,
    monthlyExpenseCount: 0,
    monthlyExpenseLimit: 50,
    createdAt: now,
    updatedAt: now,
    lastUsageReset: now,
    ...overrides,
  };
};

const createTestCategory = async (userId: string, overrides: Partial<any> = {}) => {
  const uniqueId = Date.now() + Math.random().toString(36).substr(2, 9);
  const now = new Date();
  
  // Returnează un obiect mock în loc să acceseze baza de date
  return {
    id: `test-category-${uniqueId}`,
    name: `Test Category ${uniqueId}`,
    description: 'Test category description',
    color: '#3B82F6',
    icon: 'shopping-bag',
    budgetLimit: 1000,
    budgetPeriod: 'monthly',
    isActive: true,
    isDefault: false,
    sortOrder: 0,
    userId,
    createdAt: now,
    updatedAt: now,
    ...overrides
  };
};

const createTestExpense = async (userId: string, categoryId: string, overrides: Partial<any> = {}) => {
  const uniqueId = Date.now() + Math.random().toString(36).substr(2, 9);
  const now = new Date();
  
  // Returnează un obiect mock în loc să acceseze baza de date
  return {
    id: `test-expense-${uniqueId}`,
    amount: 25.50,
    description: 'Test expense',
    date: now,
    paymentMethod: 'card',
    isRecurring: false,
    createdAt: now,
    updatedAt: now,
    userId: userId,
    categoryId: categoryId,
    ...overrides
  };
};

// Nu mock-uim JWT pentru testele de integrare
// JWT-ul trebuie să funcționeze normal pentru autentificare

// Global test utilities - simplified for Jest
if (typeof global !== 'undefined') {
  (global as any).testUtils = {
    createTestUser,
    createTestCategory,
    createTestExpense,
    prisma,
  };
}

// Export pentru CommonJS
module.exports = {
  prisma,
  createTestUser,
  createTestCategory,
  createTestExpense,
  setupTestEnvironment,
  teardownTestEnvironment,
  cleanupAfterTest,
};
