import { PrismaClient } from '@prisma/client';
import { safeLog } from '../utils/safeLogger';

// Create a single instance of PrismaClient
const prisma = new PrismaClient({
  log: process.env['NODE_ENV'] === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
});

// Handle graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

// Test database connection
const testConnection = async (): Promise<boolean> => {
  try {
    await prisma.$connect();
    safeLog.debug('✅ Database connected successfully');
    return true;
  } catch (error: unknown) {
    safeLog.error('❌ Database connection failed:', error as Error);
    return false;
  }
};

// Initialize database (equivalent to Sequelize sync)
const initializeDatabase = async (): Promise<boolean> => {
  try {
    await testConnection();
    safeLog.debug('📊 Database initialized successfully');
    return true;
  } catch (error: unknown) {
    safeLog.error('❌ Database initialization failed:', error as Error);
    throw error;
  }
};

// Close database connection
const closeConnection = async (): Promise<void> => {
  try {
    await prisma.$disconnect();
    safeLog.debug('🔌 Database connection closed');
  } catch (error: unknown) {
    safeLog.error('❌ Error closing database connection:', error as Error);
  }
};

export {
  prisma,
  testConnection,
  initializeDatabase,
  closeConnection,
};