import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Terms: React.FC = () => {
  const { t } = useTranslation();

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-2xl font-bold text-gray-900">
                {t('legal.terms.title', 'Termeni și Condiții')}
              </h1>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-600 mb-8">
                {t('legal.terms.lastUpdated', 'Ultima actualizare: 1 ianuarie 2024')}
              </p>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.acceptance.title', '1. Acceptarea Termenilor')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.acceptance.content',
                    'Prin accesarea și utilizarea aplicației FinanceFlow, acceptați să fiți legat de acești Termeni și Condiții. Dacă nu sunteți de acord cu oricare dintre acești termeni, vă rugăm să nu utilizați serviciul nostru.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.service.title', '2. Descrierea Serviciului')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.terms.service.content',
                    'FinanceFlow este o aplicație de gestionare a finanțelor personale care vă permite să urmăriți cheltuielile, să analizați tendințele financiare și să vă atingeți obiectivele de economisire.',
                  )}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>
                    {t(
                      'legal.terms.service.features.tracking',
                      'Urmărirea cheltuielilor și veniturilor',
                    )}
                  </li>
                  <li>
                    {t(
                      'legal.terms.service.features.categories',
                      'Organizarea pe categorii personalizabile',
                    )}
                  </li>
                  <li>
                    {t('legal.terms.service.features.reports', 'Generarea de rapoarte și analize')}
                  </li>
                  <li>
                    {t(
                      'legal.terms.service.features.goals',
                      'Stabilirea și urmărirea obiectivelor financiare',
                    )}
                  </li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.account.title', '3. Contul de Utilizator')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t(
                    'legal.terms.account.responsibility',
                    'Sunteți responsabil pentru menținerea confidențialității contului și parolei dvs. și pentru restricționarea accesului la computerul dvs.',
                  )}
                </p>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.account.accuracy',
                    'Vă angajați să furnizați informații exacte, actuale și complete despre dvs. conform solicitărilor din formularul de înregistrare.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.privacy.title', '4. Confidențialitatea Datelor')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.privacy.content',
                    'Protecția datelor dvs. personale este o prioritate pentru noi. Toate informațiile financiare sunt criptate și stocate în siguranță. Pentru detalii complete, consultați Politica noastră de Confidențialitate.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.usage.title', '5. Utilizarea Acceptabilă')}
                </h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {t('legal.terms.usage.intro', 'Vă angajați să nu utilizați serviciul pentru:')}
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li>
                    {t(
                      'legal.terms.usage.prohibited.illegal',
                      'Activități ilegale sau neautorizate',
                    )}
                  </li>
                  <li>
                    {t(
                      'legal.terms.usage.prohibited.harm',
                      'Încercări de a compromite securitatea sistemului',
                    )}
                  </li>
                  <li>
                    {t(
                      'legal.terms.usage.prohibited.spam',
                      'Trimiterea de spam sau conținut nedorit',
                    )}
                  </li>
                  <li>
                    {t(
                      'legal.terms.usage.prohibited.abuse',
                      'Abuzarea sau hărțuirea altor utilizatori',
                    )}
                  </li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.limitation.title', '6. Limitarea Răspunderii')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.limitation.content',
                    'FinanceFlow nu va fi responsabil pentru niciun fel de daune directe, indirecte, incidentale sau consecințiale care rezultă din utilizarea sau incapacitatea de a utiliza serviciul nostru.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.changes.title', '7. Modificări ale Termenilor')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.changes.content',
                    'Ne rezervăm dreptul de a modifica acești termeni în orice moment. Modificările vor fi comunicate prin email și vor intra în vigoare la 30 de zile după notificare.',
                  )}
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {t('legal.terms.contact.title', '8. Contact')}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {t(
                    'legal.terms.contact.content',
                    'Pentru întrebări despre acești Termeni și Condiții, vă rugăm să ne contactați la: ',
                  )}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <EMAIL>
                  </a>
                </p>
              </section>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Terms;
