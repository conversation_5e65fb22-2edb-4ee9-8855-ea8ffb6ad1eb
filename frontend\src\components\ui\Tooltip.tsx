import React, { useState, useRef, useEffect, type ReactNode } from 'react';
import { createPortal } from 'react-dom';

import { cn } from '../../utils/helpers';

// Tipuri pentru Tooltip
export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right';
export type TooltipTrigger = 'hover' | 'click' | 'focus';
export type TooltipVariant = 'dark' | 'light' | 'primary' | 'success' | 'warning' | 'error';
export type TooltipSize = 'xs' | 'sm' | 'md' | 'lg';
export type StatusType = 'success' | 'error' | 'warning' | 'info';

export interface Position {
  top: number;
  left: number;
}

export interface UseTooltipPositionReturn {
  position: Position;
  actualPlacement: TooltipPlacement;
  calculatePosition: () => void;
}

export interface TooltipProps {
  children: ReactNode;
  content?: ReactNode;
  placement?: TooltipPlacement;
  trigger?: TooltipTrigger;
  delay?: number;
  offset?: number;
  disabled?: boolean;
  className?: string;
  tooltipClassName?: string;
  arrow?: boolean;
  maxWidth?: string;
  variant?: TooltipVariant;
  size?: TooltipSize;
  [key: string]: unknown;
}

export interface SimpleTooltipProps extends Omit<TooltipProps, 'content'> {
  text: string;
}

export interface RichTooltipProps extends Omit<TooltipProps, 'content'> {
  title?: string;
  description?: string;
}

export interface HelpTooltipProps extends Omit<TooltipProps, 'content'> {
  help: string;
}

export interface StatusTooltipProps extends Omit<TooltipProps, 'content' | 'variant'> {
  status: StatusType;
  message: string;
}

export interface DelayedTooltipProps extends Omit<TooltipProps, 'delay'> {
  delay?: number;
}

export interface ClickTooltipProps extends Omit<TooltipProps, 'trigger'> {}

export interface FormTooltipProps extends Omit<TooltipProps, 'content' | 'variant'> {
  label?: string;
  description?: string;
  error?: string;
}

export interface UseTooltipReturn {
  isVisible: boolean;
  content: string;
  position: { x: number; y: number };
  show: (newContent: string, x?: number, y?: number) => void;
  hide: () => void;
  toggle: (newContent: string, x?: number, y?: number) => void;
}

export interface VariantConfig {
  [key: string]: string;
}

export interface ArrowColorConfig {
  top: string;
  bottom: string;
  left: string;
  right: string;
}

/**
 * Hook pentru calcularea poziției tooltip-ului
 */
const useTooltipPosition = (
  triggerRef: React.RefObject<HTMLDivElement>,
  tooltipRef: React.RefObject<HTMLDivElement>,
  placement: TooltipPlacement = 'top',
  offset: number = 8,
): UseTooltipPositionReturn => {
  const [position, setPosition] = useState<Position>({ top: 0, left: 0 });
  const [actualPlacement, setActualPlacement] = useState<TooltipPlacement>(placement);

  const calculatePosition = (): void => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const { scrollX } = window;
    const { scrollY } = window;

    let top = 0;
    let left = 0;
    let finalPlacement: TooltipPlacement = placement;

    // Calculează poziția inițială bazată pe placement
    switch (placement) {
      case 'top':
        top = triggerRect.top + scrollY - tooltipRect.height - offset;
        left = triggerRect.left + scrollX + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = triggerRect.bottom + scrollY + offset;
        left = triggerRect.left + scrollX + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = triggerRect.top + scrollY + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.left + scrollX - tooltipRect.width - offset;
        break;
      case 'right':
        top = triggerRect.top + scrollY + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.right + scrollX + offset;
        break;
      default:
        break;
    }

    // Verifică dacă tooltip-ul iese din viewport și ajustează
    if (left < 0) {
      left = 8;
    } else if (left + tooltipRect.width > viewportWidth) {
      left = viewportWidth - tooltipRect.width - 8;
    }

    if (top < 0) {
      if (placement === 'top') {
        top = triggerRect.bottom + scrollY + offset;
        finalPlacement = 'bottom';
      } else {
        top = 8;
      }
    } else if (top + tooltipRect.height > viewportHeight + scrollY) {
      if (placement === 'bottom') {
        top = triggerRect.top + scrollY - tooltipRect.height - offset;
        finalPlacement = 'top';
      } else {
        top = viewportHeight + scrollY - tooltipRect.height - 8;
      }
    }

    setPosition({ top, left });
    setActualPlacement(finalPlacement);
  };

  return { position, actualPlacement, calculatePosition };
};

/**
 * Componenta Tooltip principală
 */
const Tooltip: React.FC<TooltipProps> = ({
  children,
  content,
  placement = 'top',
  trigger = 'hover',
  delay = 0,
  offset = 8,
  disabled = false,
  className = '',
  tooltipClassName = '',
  arrow = true,
  maxWidth = 'max-w-xs',
  variant = 'dark',
  size = 'sm',
  ...rest
}) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const { position, actualPlacement, calculatePosition } = useTooltipPosition(
    triggerRef,
    tooltipRef,
    placement,
    offset,
  );

  const showTooltip = (): void => {
    if (disabled || !content) return;

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (delay > 0) {
      const id = setTimeout(() => {
        setIsVisible(true);
      }, delay);
      setTimeoutId(id);
    } else {
      setIsVisible(true);
    }
  };

  const hideTooltip = (): void => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    setIsVisible(false);
  };

  const handleClick = (): void => {
    if (trigger === 'click') {
      if (isVisible) {
        hideTooltip();
      } else {
        showTooltip();
      }
    }
  };

  const handleFocus = (): void => {
    if (trigger === 'focus' || trigger === 'hover') {
      showTooltip();
    }
  };

  const handleBlur = (): void => {
    if (trigger === 'focus' || trigger === 'hover') {
      hideTooltip();
    }
  };

  const handleMouseEnter = (): void => {
    if (trigger === 'hover') {
      showTooltip();
    }
  };

  const handleMouseLeave = (): void => {
    if (trigger === 'hover') {
      hideTooltip();
    }
  };

  useEffect(() => {
    if (isVisible) {
      calculatePosition();

      const handleResize = () => calculatePosition();
      const handleScroll = () => calculatePosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
    return undefined;
  }, [isVisible, content]);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  // Închide tooltip-ul la click în afara lui
  useEffect(() => {
    if (trigger === 'click' && isVisible) {
      const handleClickOutside = (event: MouseEvent): void => {
        if (
          triggerRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          tooltipRef.current &&
          !tooltipRef.current.contains(event.target as Node)
        ) {
          hideTooltip();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
    return undefined;
  }, [trigger, isVisible]);

  const variantClasses: Record<TooltipVariant, string> = {
    dark: 'bg-gray-900 text-white border-gray-700',
    light: 'bg-white text-gray-900 border-gray-200 shadow-lg',
    primary: 'bg-primary-600 text-white border-primary-700',
    success: 'bg-green-600 text-white border-green-700',
    warning: 'bg-yellow-600 text-white border-yellow-700',
    error: 'bg-red-600 text-white border-red-700',
  };

  const sizeClasses: Record<TooltipSize, string> = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-5 py-2.5 text-lg',
  };

  const arrowClasses: Record<TooltipPlacement, string> = {
    top: 'after:absolute after:top-full after:left-1/2 after:transform after:-translate-x-1/2 after:border-4 after:border-transparent',
    bottom:
      'after:absolute after:bottom-full after:left-1/2 after:transform after:-translate-x-1/2 after:border-4 after:border-transparent',
    left: 'after:absolute after:left-full after:top-1/2 after:transform after:-translate-y-1/2 after:border-4 after:border-transparent',
    right:
      'after:absolute after:right-full after:top-1/2 after:transform after:-translate-y-1/2 after:border-4 after:border-transparent',
  };

  const arrowColorClasses: Record<TooltipVariant, ArrowColorConfig> = {
    dark: {
      top: 'after:border-t-gray-900',
      bottom: 'after:border-b-gray-900',
      left: 'after:border-l-gray-900',
      right: 'after:border-r-gray-900',
    },
    light: {
      top: 'after:border-t-white',
      bottom: 'after:border-b-white',
      left: 'after:border-l-white',
      right: 'after:border-r-white',
    },
    primary: {
      top: 'after:border-t-primary-600',
      bottom: 'after:border-b-primary-600',
      left: 'after:border-l-primary-600',
      right: 'after:border-r-primary-600',
    },
    success: {
      top: 'after:border-t-green-600',
      bottom: 'after:border-b-green-600',
      left: 'after:border-l-green-600',
      right: 'after:border-r-green-600',
    },
    warning: {
      top: 'after:border-t-yellow-600',
      bottom: 'after:border-b-yellow-600',
      left: 'after:border-l-yellow-600',
      right: 'after:border-r-yellow-600',
    },
    error: {
      top: 'after:border-t-red-600',
      bottom: 'after:border-b-red-600',
      left: 'after:border-l-red-600',
      right: 'after:border-r-red-600',
    },
  };

  const tooltipElement = isVisible && content && (
    <div
      ref={tooltipRef}
      className={cn(
        'fixed z-50 rounded-md border transition-opacity duration-200',
        'pointer-events-none select-none',
        maxWidth,
        variantClasses[variant],
        sizeClasses[size],
        arrow && arrowClasses[actualPlacement],
        arrow && arrowColorClasses[variant][actualPlacement],
        tooltipClassName,
      )}
      style={{
        top: position.top,
        left: position.left,
      }}
      role="tooltip"
      aria-hidden={!isVisible}
    >
      {typeof content === 'string' ? <span>{content}</span> : content}
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        className={cn('inline-block', className)}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...rest}
      >
        {children}
      </div>
      {typeof document !== 'undefined' && createPortal(tooltipElement, document.body)}
    </>
  );
};

/**
 * Tooltip simplu pentru text
 */
export const SimpleTooltip: React.FC<SimpleTooltipProps> = ({ children, text, ...props }) => {
  return (
    <Tooltip content={text} {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Tooltip cu conținut rich (HTML)
 */
export const RichTooltip: React.FC<RichTooltipProps> = ({
  children,
  title,
  description,
  ...props
}) => {
  const content = (
    <div>
      {title && <div className="font-semibold mb-1">{title}</div>}
      {description && <div className="text-sm opacity-90">{description}</div>}
    </div>
  );

  return (
    <Tooltip content={content} maxWidth="max-w-sm" {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Tooltip pentru informații de ajutor
 */
export const HelpTooltip: React.FC<HelpTooltipProps> = ({ children, help, ...props }) => {
  return (
    <Tooltip content={help} variant="light" placement="top" maxWidth="max-w-md" {...props}>
      <span className="inline-flex items-center justify-center w-4 h-4 text-xs bg-gray-400 text-white rounded-full cursor-help hover:bg-gray-500 transition-colors">
        ?
      </span>
    </Tooltip>
  );
};

/**
 * Tooltip pentru status
 */
export const StatusTooltip: React.FC<StatusTooltipProps> = ({
  children,
  status,
  message,
  ...props
}) => {
  const statusVariants: Record<StatusType, TooltipVariant> = {
    success: 'success',
    error: 'error',
    warning: 'warning',
    info: 'primary',
  };

  return (
    <Tooltip content={message} variant={statusVariants[status] || 'dark'} {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Tooltip cu delay personalizabil
 */
export const DelayedTooltip: React.FC<DelayedTooltipProps> = ({
  children,
  delay = 500,
  ...props
}) => {
  return (
    <Tooltip delay={delay} {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Tooltip care se activează la click
 */
export const ClickTooltip: React.FC<ClickTooltipProps> = ({ children, ...props }) => {
  return (
    <Tooltip trigger="click" {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Tooltip pentru elemente de formular
 */
export const FormTooltip: React.FC<FormTooltipProps> = ({
  children,
  label,
  description,
  error,
  ...props
}) => {
  const content = (
    <div>
      {label && <div className="font-medium mb-1">{label}</div>}
      {description && <div className="text-sm mb-2">{description}</div>}
      {error && <div className="text-sm text-red-200 font-medium">{error}</div>}
    </div>
  );

  return (
    <Tooltip content={content} variant={error ? 'error' : 'dark'} maxWidth="max-w-sm" {...props}>
      {children}
    </Tooltip>
  );
};

/**
 * Hook pentru utilizarea tooltip-urilor programatic
 */
export const useTooltip = (): UseTooltipReturn => {
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [content, setContent] = useState<string>('');
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  const show = (newContent: string, x: number = 0, y: number = 0): void => {
    setContent(newContent);
    setPosition({ x, y });
    setIsVisible(true);
  };

  const hide = (): void => {
    setIsVisible(false);
  };

  const toggle = (newContent: string, x: number = 0, y: number = 0): void => {
    if (isVisible) {
      hide();
    } else {
      show(newContent, x, y);
    }
  };

  return {
    isVisible,
    content,
    position,
    show,
    hide,
    toggle,
  };
};

export default Tooltip;
