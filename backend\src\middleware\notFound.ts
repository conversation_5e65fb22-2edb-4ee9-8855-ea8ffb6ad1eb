import { Request, Response, NextFunction } from 'express';
import { safeLog } from '../utils/safeLogger';

// Interface for custom error
interface CustomError extends Error {
  statusCode?: number;
}

/**
 * 404 Not Found middleware
 * This middleware handles requests to routes that don't exist
 */
const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error: CustomError = new Error(`Not Found - ${req.originalUrl}`);
  error.statusCode = 404;
  
  // Log the 404 request for debugging
  safeLog.debug(`404 - ${req.method} ${req.originalUrl} - ${req.ip}`);
  
  // Pass error to error handler
  next(error);
};

export default notFound;