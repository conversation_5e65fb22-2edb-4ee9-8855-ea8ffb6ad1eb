import express, { Request, Response } from 'express';
import subscriptionController from '../controllers/subscriptionController';
import logger from '../utils/logger';

const router = express.Router();

/**
 * Rute pentru webhook-uri (fără autentificare)
 */

/**
 * @route   POST /api/webhooks/stripe
 * @desc    Webhook pentru evenimente Stripe
 * @access  Public (dar verificat prin signature)
 */
router.post('/stripe', express.raw({ type: 'application/json' }), async (req: Request, res: Response): Promise<void> => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    const payload = req.body;

    if (!signature) {
      logger.warn('Missing Stripe signature in webhook request');
      res.status(400).json({
        success: false,
        message: 'Missing Stripe signature'
      });
      return;
    }

    // Verifică și procesează webhook-ul
    await subscriptionController.handleWebhook(req, res);
  } catch (error: unknown) {
    logger.error('Error in Stripe webhook:', error);
    res.status(400).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/webhooks/health
 * @desc    Health check pentru webhook-uri
 * @access  Public
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'Webhook endpoint is healthy',
    timestamp: new Date().toISOString()
  });
});

export default router;