# Expense Tracker Backend

Backend API pentru aplicația de urmărire a cheltuielilor, construită cu Node.js, Express și Sequelize.

## 🚀 Funcționalități

- **Autentificare și Autorizare**: JWT-based authentication
- **Gestionarea Utilizatorilor**: Înregistrare, login, profil, schimbarea parolei
- **Categorii**: CRUD pentru categorii de cheltuieli
- **Cheltuieli**: CRUD pentru cheltuieli cu filtrare avansată
- **Statistici**: Rapoarte și tendințe pentru cheltuieli
- **Tag-uri**: Sistem de etichetare pentru cheltuieli
- **Securitate**: Rate limiting, validare, sanitizare
- **Baza de Date**: Suport pentru SQLite (dev) și PostgreSQL (prod)

## 📋 Cerințe

- Node.js 18+ 
- npm sau yarn
- PostgreSQL (pentru producție) sau SQLite (pentru dezvoltare)

## 🛠️ Instalare

1. **Clonează repository-ul**
```bash
git clone <repository-url>
cd backend
```

2. **Instalează dependențele**
```bash
npm install
```

3. **Configurează variabilele de mediu**
```bash
cp .env.example .env
```

Editează `.env` cu configurațiile tale:
```env
PORT=3000
NODE_ENV=development
DB_TYPE=sqlite
DB_PATH=./data/expense_tracker.db
JWT_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
CORS_ORIGIN=http://localhost:5173
```

4. **Inițializează baza de date**
```bash
npm run db:init
```

## 🚀 Rulare

### Dezvoltare
```bash
npm run dev
```

### Producție
```bash
npm start
```

### Testare
```bash
# Rulează toate testele
npm test

# Rulează testele cu coverage
npm run test:coverage

# Rulează testele în watch mode
npm run test:watch
```

## 📚 API Documentation

Odată ce serverul rulează, poți accesa documentația API la:
- **Health Check**: `GET /api/health`
- **API Docs**: `GET /api/docs`

### Endpoints Principale

#### Autentificare
- `POST /api/auth/register` - Înregistrare utilizator
- `POST /api/auth/login` - Login
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - Logout
- `GET /api/auth/profile` - Profil utilizator
- `PUT /api/auth/profile` - Actualizare profil

#### Categorii
- `GET /api/categories` - Lista categorii
- `POST /api/categories` - Creare categorie
- `PUT /api/categories/:id` - Actualizare categorie
- `DELETE /api/categories/:id` - Ștergere categorie
- `GET /api/categories/stats` - Statistici categorii

#### Cheltuieli
- `GET /api/expenses` - Lista cheltuieli (cu filtrare)
- `POST /api/expenses` - Creare cheltuială
- `PUT /api/expenses/:id` - Actualizare cheltuială
- `DELETE /api/expenses/:id` - Ștergere cheltuială
- `GET /api/expenses/stats` - Statistici cheltuieli
- `GET /api/expenses/trends` - Tendințe lunare

## 🗄️ Structura Bazei de Date

### Tabele Principale
- **users** - Informații utilizatori
- **categories** - Categorii de cheltuieli
- **expenses** - Cheltuielile utilizatorilor

### Relații
- Un utilizator poate avea multe categorii
- Un utilizator poate avea multe cheltuieli
- O categorie poate avea multe cheltuieli
- O cheltuială aparține unui utilizator și unei categorii

## 🔧 Scripturi Disponibile

```bash
# Dezvoltare
npm run dev          # Pornește serverul în modul dezvoltare
npm run start        # Pornește serverul în modul producție

# Testare
npm test             # Rulează testele
npm run test:watch   # Testare în watch mode
npm run test:coverage # Testare cu coverage

# Linting și Formatare
npm run lint         # Verifică codul cu ESLint
npm run lint:fix     # Corectează automat problemele ESLint
npm run format       # Formatează codul cu Prettier

# Baza de Date
npm run db:init      # Inițializează baza de date
npm run db:reset     # Resetează baza de date
npm run db:seed      # Populează cu date de test
```

## 🔒 Securitate

- **JWT Authentication**: Token-uri sigure pentru autentificare
- **Password Hashing**: bcrypt pentru hash-uirea parolelor
- **Rate Limiting**: Protecție împotriva spam-ului
- **Input Validation**: Joi pentru validarea datelor
- **CORS**: Configurare CORS pentru frontend
- **Helmet**: Securitate headers HTTP

## 📁 Structura Proiectului

```
backend/
├── src/
│   ├── config/
│   │   └── database.js      # Configurare bază de date
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── categoryController.js
│   │   └── expenseController.js
│   ├── middleware/
│   │   ├── auth.js          # Middleware autentificare
│   │   └── validation.js    # Middleware validare
│   ├── models/
│   │   ├── User.js
│   │   ├── Category.js
│   │   ├── Expense.js
│   │   └── index.js
│   └── routes/
│       ├── auth.js
│       ├── categories.js
│       ├── expenses.js
│       └── index.js
├── tests/
├── .env.example
├── .gitignore
├── app.js                   # Aplicația Express
├── package.json
└── README.md
```

## 🌍 Variabile de Mediu

| Variabilă | Descriere | Valoare Implicită |
|-----------|-----------|-------------------|
| `PORT` | Portul serverului | `3000` |
| `NODE_ENV` | Mediul de rulare | `development` |
| `DB_TYPE` | Tipul bazei de date | `sqlite` |
| `JWT_SECRET` | Cheia secretă JWT | - |
| `CORS_ORIGIN` | Originea permisă CORS | `http://localhost:5173` |

## 🐛 Debugging

### Loguri
Serverul folosește Morgan pentru logging HTTP și console.log pentru debugging.

### Testare API
Poți testa API-ul folosind:
- **Postman**: Importă colecția din `docs/postman/`
- **curl**: Exemple în documentație
- **Frontend**: Conectează frontend-ul React

## 📈 Monitorizare

- **Health Check**: `GET /api/health`
- **Logs**: Verifică consolă pentru erori
- **Database**: Monitorizează conexiunile DB

## 🚀 Deployment

### Railway/Heroku
1. Setează variabilele de mediu
2. Configurează PostgreSQL
3. Deploy din Git

### VPS
1. Instalează Node.js și PostgreSQL
2. Clonează repository-ul
3. Configurează PM2 pentru management proces
4. Configurează Nginx ca reverse proxy

## 🤝 Contribuții

1. Fork repository-ul
2. Creează o ramură pentru feature (`git checkout -b feature/AmazingFeature`)
3. Commit schimbările (`git commit -m 'Add some AmazingFeature'`)
4. Push pe ramură (`git push origin feature/AmazingFeature`)
5. Deschide un Pull Request

## 📄 Licență

Acest proiect este licențiat sub MIT License.

## 📞 Support

Pentru întrebări sau probleme, deschide un issue pe GitHub.