/**
 * Helper functions pentru gestionarea erorilor TypeScript în frontend
 * Acestea rezolvă problemele cu tipurile unknown în catch blocks
 */

/**
 * Convertește o valoare unknown într-un Error
 * @param error - Valoarea unknown din catch block
 * @returns Error object
 */
export function toError(error: unknown): Error {
  if (error instanceof Error) {
    return error;
  }

  if (typeof error === 'string') {
    return new Error(error);
  }

  if (typeof error === 'object' && error !== null) {
    // Încearcă să extragă mesajul din obiect
    const obj = error as Record<string, unknown>;
    if (typeof obj['message'] === 'string') {
      return new Error(obj['message']);
    }
    if (typeof obj['error'] === 'string') {
      return new Error(obj['error']);
    }
    // Fallback la JSON.stringify
    try {
      return new Error(JSON.stringify(error));
    } catch {
      return new Error('Unknown error object');
    }
  }

  return new Error(`Unknown error: ${String(error)}`);
}

/**
 * Convertește o valoare unknown într-un Error | undefined
 * @param error - Valoarea unknown din catch block
 * @returns Error object sau undefined
 */
export function toErrorOrUndefined(error: unknown): Error | undefined {
  if (error === null || error === undefined) {
    return undefined;
  }
  return toError(error);
}

/**
 * Extrage mesajul de eroare dintr-o valoare unknown
 * @param error - Valoarea unknown din catch block
 * @returns String cu mesajul de eroare
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  if (typeof error === 'object' && error !== null) {
    const obj = error as Record<string, unknown>;
    if (typeof obj['message'] === 'string') {
      return obj['message'];
    }
    if (typeof obj['error'] === 'string') {
      return obj['error'];
    }
    try {
      return JSON.stringify(error);
    } catch {
      return 'Unknown error object';
    }
  }

  return String(error);
}

/**
 * Verifică dacă o valoare unknown este un Error
 * @param error - Valoarea de verificat
 * @returns true dacă este Error
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Safe wrapper pentru funcții care pot arunca erori
 * @param fn - Funcția de executat
 * @returns Rezultatul funcției sau Error
 */
export function safeExecute<T>(fn: () => T): T | Error {
  try {
    return fn();
  } catch (error) {
    return toError(error);
  }
}

/**
 * Safe wrapper async pentru funcții care pot arunca erori
 * @param fn - Funcția async de executat
 * @returns Promise cu rezultatul funcției sau Error
 */
export async function safeExecuteAsync<T>(fn: () => Promise<T>): Promise<T | Error> {
  try {
    return await fn();
  } catch (error) {
    return toError(error);
  }
}

/**
 * Verifică dacă o eroare unknown este un AxiosError și extrage informațiile relevante
 * @param error - Valoarea unknown din catch block
 * @returns Obiect cu informații despre eroare
 */
export function handleAxiosError(error: unknown): {
  message: string;
  errors?: any;
  status?: number;
} {
  // Verifică dacă este AxiosError
  if (typeof error === 'object' && error !== null && 'response' in error) {
    const axiosError = error as any;
    return {
      message: axiosError.response?.data?.message || axiosError.message || 'Eroare de rețea',
      errors: axiosError.response?.data?.errors || null,
      status: axiosError.response?.status,
    };
  }

  // Fallback pentru alte tipuri de erori
  return {
    message: getErrorMessage(error),
    errors: null,
  };
}
