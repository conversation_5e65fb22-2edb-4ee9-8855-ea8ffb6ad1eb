# ⚡ PERFORMANȚĂ ȘI OPTIMIZĂRI

## 📊 OVERVIEW PERFORMANȚĂ

### Obiective de Performanță
- **Page Load Time**: < 2 secunde (First Contentful Paint)
- **API Response Time**: < 200ms (95th percentile)
- **Bundle Size**: < 500KB (gzipped)
- **Lighthouse Score**: > 90 pentru toate categoriile
- **Time to Interactive**: < 3 secunde

### Metrici Actuale
- **Frontend Build**: ~30 secunde
- **Backend Startup**: ~3 secunde
- **Hot Reload**: ~1 secundă
- **Image Optimization**: 55% reducere (396KB → 176KB)
- **Bundle Splitting**: Vendor libraries separate

---

## 🚀 OPTIMIZĂRI FRONTEND

### Bundle Optimization

#### Webpack Configuration
```javascript
// frontend/webpack.config.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/main.tsx',
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isProduction 
        ? '[name].[contenthash].js' 
        : '[name].js',
      chunkFilename: isProduction 
        ? '[name].[contenthash].chunk.js' 
        : '[name].chunk.js',
      clean: true,
    },
    
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 20,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
          },
        },
      },
      runtimeChunk: 'single',
      usedExports: true,
      sideEffects: false,
    },
    
    module: {
      rules: [
        {
          test: /\.(png|jpe?g|gif|svg|webp)$/i,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 8 * 1024, // 8KB
            },
          },
          generator: {
            filename: 'images/[name].[hash][ext]',
          },
          use: [
            {
              loader: 'image-webpack-loader',
              options: {
                mozjpeg: {
                  progressive: true,
                  quality: 80,
                },
                optipng: {
                  enabled: true,
                },
                pngquant: {
                  quality: [0.6, 0.8],
                },
                gifsicle: {
                  interlaced: false,
                },
                webp: {
                  quality: 80,
                },
              },
            },
          ],
        },
      ],
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html',
        minify: isProduction,
      }),
      new MiniCssExtractPlugin({
        filename: isProduction 
          ? '[name].[contenthash].css' 
          : '[name].css',
      }),
      ...(process.env.ANALYZE ? [new BundleAnalyzerPlugin()] : []),
    ],
    
    performance: {
      hints: isProduction ? 'warning' : false,
      maxAssetSize: 512000, // 512KB
      maxEntrypointSize: 512000,
    },
  };
};
```

### Code Splitting și Lazy Loading

#### Route-based Code Splitting
```typescript
// src/App.tsx
import React, { Suspense, lazy } from 'react';
import { Routes, Route } from 'react-router-dom';
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';

// Lazy load pages
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Expenses = lazy(() => import('./pages/Expenses'));
const Reports = lazy(() => import('./pages/Reports'));
const Settings = lazy(() => import('./pages/Settings'));

// Lazy load admin pages
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const UsersList = lazy(() => import('./pages/admin/UsersList'));

function App() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/expenses" element={<Expenses />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
          
          {/* Admin routes */}
          <Route path="/admin" element={<AdminDashboard />} />
          <Route path="/admin/users" element={<UsersList />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
}
```

#### Component-based Code Splitting
```typescript
// src/components/charts/ExpenseChart.tsx
import React, { lazy, Suspense } from 'react';

// Lazy load heavy chart library only when needed
const Chart = lazy(() => import('react-chartjs-2').then(module => ({
  default: module.Line
})));

interface ExpenseChartProps {
  data: ChartData;
  options?: ChartOptions;
}

const ExpenseChart: React.FC<ExpenseChartProps> = ({ data, options }) => {
  return (
    <Suspense fallback={<div className="h-64 bg-gray-100 animate-pulse rounded" />}>
      <Chart data={data} options={options} />
    </Suspense>
  );
};

export default ExpenseChart;
```

### React Performance Optimizations

#### Memoization și Optimization
```typescript
// src/components/ExpenseList.tsx
import React, { memo, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';

interface ExpenseListProps {
  expenses: Expense[];
  onExpenseClick: (expense: Expense) => void;
  filters: ExpenseFilters;
}

const ExpenseList = memo<ExpenseListProps>(({ 
  expenses, 
  onExpenseClick, 
  filters 
}) => {
  // Memoize filtered expenses
  const filteredExpenses = useMemo(() => {
    return expenses.filter(expense => {
      if (filters.category && expense.categoryId !== filters.category) {
        return false;
      }
      if (filters.dateFrom && expense.expenseDate < filters.dateFrom) {
        return false;
      }
      if (filters.dateTo && expense.expenseDate > filters.dateTo) {
        return false;
      }
      return true;
    });
  }, [expenses, filters]);

  // Memoize click handler
  const handleExpenseClick = useCallback((index: number) => {
    onExpenseClick(filteredExpenses[index]);
  }, [filteredExpenses, onExpenseClick]);

  // Memoize row renderer
  const Row = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const expense = filteredExpenses[index];
    
    return (
      <div style={style} className="expense-row">
        <ExpenseItem 
          expense={expense} 
          onClick={() => handleExpenseClick(index)}
        />
      </div>
    );
  }, [filteredExpenses, handleExpenseClick]);

  return (
    <List
      height={600}
      itemCount={filteredExpenses.length}
      itemSize={80}
      width="100%"
    >
      {Row}
    </List>
  );
});

ExpenseList.displayName = 'ExpenseList';
export default ExpenseList;
```

### Service Worker și PWA

#### Service Worker Implementation
```typescript
// public/sw.js
const CACHE_NAME = 'expense-tracker-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});
```

---

## 🗄️ OPTIMIZĂRI BACKEND

### Database Optimization

#### Query Optimization
```typescript
// src/services/expenseService.ts
import { PrismaClient } from '@prisma/client';

class ExpenseService {
  constructor(private prisma: PrismaClient) {}

  async getExpensesOptimized(userId: string, params: ExpenseParams) {
    // Use database-level filtering instead of application-level
    const where = {
      userId,
      ...(params.categoryId && { categoryId: params.categoryId }),
      ...(params.dateFrom && { 
        expenseDate: { 
          gte: new Date(params.dateFrom) 
        } 
      }),
      ...(params.dateTo && { 
        expenseDate: { 
          lte: new Date(params.dateTo) 
        } 
      }),
    };

    // Use select to only fetch needed fields
    const expenses = await this.prisma.expense.findMany({
      where,
      select: {
        id: true,
        amount: true,
        description: true,
        expenseDate: true,
        category: {
          select: {
            id: true,
            name: true,
            icon: true,
            color: true,
          },
        },
      },
      orderBy: { expenseDate: 'desc' },
      take: params.limit || 50,
      skip: params.offset || 0,
    });

    // Get total count efficiently
    const total = await this.prisma.expense.count({ where });

    return { expenses, total };
  }

  async getMonthlyStats(userId: string, year: number) {
    // Use raw SQL for complex aggregations
    const stats = await this.prisma.$queryRaw`
      SELECT 
        EXTRACT(MONTH FROM expense_date) as month,
        SUM(amount) as total_amount,
        COUNT(*) as expense_count,
        AVG(amount) as avg_amount
      FROM expenses 
      WHERE user_id = ${userId} 
        AND EXTRACT(YEAR FROM expense_date) = ${year}
      GROUP BY EXTRACT(MONTH FROM expense_date)
      ORDER BY month
    `;

    return stats;
  }
}
```

#### Database Indexing Strategy
```sql
-- Critical indexes for performance
CREATE INDEX CONCURRENTLY idx_expenses_user_date 
ON expenses(user_id, expense_date DESC);

CREATE INDEX CONCURRENTLY idx_expenses_category 
ON expenses(category_id);

CREATE INDEX CONCURRENTLY idx_expenses_amount 
ON expenses(amount) WHERE amount > 0;

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_expenses_user_category_date 
ON expenses(user_id, category_id, expense_date DESC);

-- Partial indexes for specific conditions
CREATE INDEX CONCURRENTLY idx_expenses_recent 
ON expenses(user_id, expense_date DESC) 
WHERE expense_date >= CURRENT_DATE - INTERVAL '30 days';

-- Index for text search
CREATE INDEX CONCURRENTLY idx_expenses_description_gin 
ON expenses USING gin(to_tsvector('english', description));
```

### Caching Strategy

#### Redis Implementation
```typescript
// src/middleware/cache.ts
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

interface CacheOptions {
  ttl?: number;
  prefix?: string;
  condition?: (req: Request) => boolean;
}

export const cache = (options: CacheOptions = {}) => {
  const { ttl = 300, prefix = 'cache', condition = () => true } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    if (!condition(req) || req.method !== 'GET') {
      return next();
    }

    const key = `${prefix}:${req.originalUrl}:${req.user?.id || 'anonymous'}`;
    
    try {
      const cached = await redis.get(key);
      
      if (cached) {
        return res.json(JSON.parse(cached));
      }

      // Override res.json to cache the response
      const originalJson = res.json;
      res.json = function(data: any) {
        // Cache successful responses
        if (res.statusCode < 400) {
          redis.setex(key, ttl, JSON.stringify(data));
        }
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('Cache error:', error);
      next();
    }
  };
};

// Cache configurations for different endpoints
export const expenseCache = cache({
  ttl: 300, // 5 minutes
  prefix: 'expenses',
  condition: (req) => !req.query.realtime,
});

export const categoryCache = cache({
  ttl: 1800, // 30 minutes
  prefix: 'categories',
});

export const reportCache = cache({
  ttl: 3600, // 1 hour
  prefix: 'reports',
});
```

### API Response Optimization

#### Response Compression
```typescript
// src/middleware/compression.ts
import compression from 'compression';

export const compressionMiddleware = compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6, // Compression level (1-9)
  threshold: 1024, // Only compress responses > 1KB
});
```

#### Response Pagination
```typescript
// src/controllers/expenseController.ts
export const getExpenses = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    const { expenses, total } = await expenseService.getExpenses(
      req.user!.id,
      { ...req.query, limit, offset }
    );

    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    res.json({
      success: true,
      data: expenses,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
        nextPage: hasNext ? page + 1 : null,
        prevPage: hasPrev ? page - 1 : null,
      },
    });
  } catch (error) {
    next(error);
  }
};
```

---

## 📊 MONITORING ȘI METRICI

### Performance Monitoring

#### Application Metrics
```typescript
// src/middleware/metrics.ts
import { Request, Response, NextFunction } from 'express';

interface Metrics {
  requestCount: number;
  responseTime: number[];
  errorCount: number;
  activeConnections: number;
}

const metrics: Metrics = {
  requestCount: 0,
  responseTime: [],
  errorCount: 0,
  activeConnections: 0,
};

export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  metrics.requestCount++;
  metrics.activeConnections++;

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    metrics.responseTime.push(duration);
    metrics.activeConnections--;

    if (res.statusCode >= 400) {
      metrics.errorCount++;
    }

    // Log slow requests
    if (duration > 1000) {
      console.warn(`Slow request: ${req.method} ${req.path} - ${duration}ms`);
    }
  });

  next();
};

export const getMetrics = () => {
  const avgResponseTime = metrics.responseTime.length > 0
    ? metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length
    : 0;

  return {
    ...metrics,
    avgResponseTime,
    p95ResponseTime: calculatePercentile(metrics.responseTime, 95),
    errorRate: metrics.requestCount > 0 
      ? (metrics.errorCount / metrics.requestCount) * 100 
      : 0,
  };
};

function calculatePercentile(arr: number[], percentile: number): number {
  if (arr.length === 0) return 0;
  
  const sorted = [...arr].sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
}
```

### Health Checks

#### Comprehensive Health Check
```typescript
// src/routes/health.ts
import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

const router = Router();
const prisma = new PrismaClient();
const redis = new Redis(process.env.REDIS_URL);

router.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    checks: {
      database: 'unknown',
      redis: 'unknown',
      disk: 'unknown',
    },
  };

  try {
    // Database check
    await prisma.$queryRaw`SELECT 1`;
    health.checks.database = 'healthy';
  } catch (error) {
    health.checks.database = 'unhealthy';
    health.status = 'unhealthy';
  }

  try {
    // Redis check
    await redis.ping();
    health.checks.redis = 'healthy';
  } catch (error) {
    health.checks.redis = 'unhealthy';
    // Redis is optional, don't mark as unhealthy
  }

  // Disk space check (simplified)
  try {
    const stats = require('fs').statSync('.');
    health.checks.disk = 'healthy';
  } catch (error) {
    health.checks.disk = 'unhealthy';
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});

export default router;
```

---

## 🎯 BENCHMARK-URI ȘI ȚINTE

### Performance Targets
```typescript
// Performance benchmarks
const PERFORMANCE_TARGETS = {
  // Frontend
  firstContentfulPaint: 1500, // ms
  largestContentfulPaint: 2500, // ms
  timeToInteractive: 3000, // ms
  cumulativeLayoutShift: 0.1,
  
  // Backend
  apiResponseTime: {
    p50: 100, // ms
    p95: 200, // ms
    p99: 500, // ms
  },
  
  // Database
  queryTime: {
    simple: 10, // ms
    complex: 100, // ms
    reports: 500, // ms
  },
  
  // Bundle sizes
  bundleSize: {
    main: 300, // KB (gzipped)
    vendor: 200, // KB (gzipped)
    total: 500, // KB (gzipped)
  },
};
```

### Monitoring Commands
```bash
# Frontend performance
npm run build
npm run analyze
npx lighthouse http://localhost:5173 --output html

# Backend performance
npm run test:performance
npm run benchmark

# Database performance
npm run db:analyze
npm run db:slow-queries

# Bundle analysis
npx webpack-bundle-analyzer dist
npx bundlesize
```

---

*Ultima actualizare: Ianuarie 2025*
