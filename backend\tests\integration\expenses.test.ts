import request from 'supertest';
import app from '../../src/app';
import { prisma, createTestUser, createTestCategory } from '../setup';
import jwt from 'jsonwebtoken';

describe('Expenses API Integration Tests', () => {
  let testUser: any;
  let testCategory: any;
  let authToken: string;

  beforeEach(async () => {
    testUser = await createTestUser();
    testCategory = await createTestCategory(testUser.id);
    
    // Create auth token
    authToken = jwt.sign(
      { userId: testUser.id, email: testUser.email },
      process.env.JWT_SECRET!,
      { expiresIn: '1h' }
    );
  });

  describe('POST /api/expenses', () => {
    it('should create a new expense successfully', async () => {
      // Arrange
      const expenseData = {
        amount: 25.50,
        description: 'Test expense',
        date: '2024-01-15',
        paymentMethod: 'cash',
        categoryId: testCategory.id,
        tags: ['test', 'integration'],
      };

      // Act
      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(201);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        amount: 25.50,
        description: 'Test expense',
        paymentMethod: 'cash',
        categoryId: testCategory.id,
        tags: ['test', 'integration'],
      });
      expect(response.body.data.id).toBeDefined();

      // Verify in database
      const expenseInDb = await prisma.expense.findUnique({
        where: { id: response.body.data.id },
      });
      expect(expenseInDb).toBeTruthy();
    });

    it('should enforce usage limits for free plan users', async () => {
      // Arrange - Create 50 expenses (free plan limit)
      for (let i = 0; i < 50; i++) {
        await prisma.expense.create({
          data: {
            amount: 10,
            description: `Expense ${i + 1}`,
            date: new Date(),
            paymentMethod: 'cash',
            userId: testUser.id,
          categoryId: testCategory.id,
            tags: [],
            isRecurring: false,
          },
        });
      }

      const expenseData = {
        amount: 25.50,
        description: 'Over limit expense',
        date: '2024-01-15',
        paymentMethod: 'cash',
        categoryId: testCategory.id,
      };

      // Act
      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(403);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Usage limit exceeded');
      expect(response.body.data.upgrade_required).toBe(true);
    });

    it('should validate required fields', async () => {
      // Arrange
      const invalidData = {
        description: 'Missing amount',
        categoryId: testCategory.id,
      };

      // Act
      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should require authentication', async () => {
      // Arrange
      const expenseData = {
        amount: 25.50,
        description: 'Unauthorized expense',
        categoryId: testCategory.id,
      };

      // Act
      const response = await request(app)
        .post('/api/expenses')
        .send(expenseData)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Authentication required');
    });

    it('should validate category ownership', async () => {
      // Arrange
      const otherUser = await createTestUser({
        email: '<EMAIL>',
      });
      const otherCategory = await createTestCategory(otherUser.id);

      const expenseData = {
        amount: 25.50,
        description: 'Invalid category',
        categoryId: otherCategory.id,
      };

      // Act
      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(403);

      // Assert
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/expenses', () => {
    beforeEach(async () => {
      // Create test expenses
      await prisma.expense.createMany({
        data: [
          {
            amount: 25.50,
            description: 'First expense',
            date: new Date('2024-01-15'),
            paymentMethod: 'cash',
            userId: testUser.id,
          categoryId: testCategory.id,
            tags: ['tag1'],
            isRecurring: false,
          },
          {
            amount: 15.75,
            description: 'Second expense',
            date: new Date('2024-01-16'),
            paymentMethod: 'card',
            userId: testUser.id,
          categoryId: testCategory.id,
            tags: ['tag2'],
            isRecurring: false,
          },
        ],
      });
    });

    it('should return user expenses with pagination', async () => {
      // Act
      const response = await request(app)
        .get('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses).toHaveLength(2);
      expect(response.body.data.pagination).toMatchObject({
        current_page: 1,
        per_page: 20,
        total_items: 2,
        total_pages: 1,
      });
    });

    it('should filter expenses by category', async () => {
      // Arrange
      const otherCategory = await createTestCategory(testUser.id, {
        name: 'Other Category',
      });
      
      await prisma.expense.create({
        data: {
          amount: 30,
          description: 'Other category expense',
          date: new Date(),
          paymentMethod: 'cash',
          userId: testUser.id,
          categoryId: otherCategory.id,
          tags: [],
          isRecurring: false,
        },
      });

      // Act
      const response = await request(app)
        .get(`/api/expenses?categoryId=${testCategory.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses.length).toBeGreaterThanOrEqual(1); // At least one expense from testCategory
      expect(response.body.data.expenses.every((expense: any) =>
        expense.categoryId === testCategory.id
      )).toBe(true);
    });

    it('should filter expenses by date range', async () => {
      // Act
      const response = await request(app)
        .get('/api/expenses?date_from=2024-01-16&date_to=2024-01-16')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses.length).toBeGreaterThanOrEqual(0);
      // Check if we have the expected expense in the filtered results
      const filteredExpense = response.body.data.expenses.find((exp: any) => exp.description === 'Second expense');
      if (filteredExpense) {
        expect(filteredExpense.description).toBe('Second expense');
      }
    });

    it('should search expenses by description', async () => {
      // Act
      const response = await request(app)
        .get('/api/expenses?search=First')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses).toHaveLength(1);
      expect(response.body.data.expenses[0].description).toBe('First expense');
    });

    it('should only return expenses for authenticated user', async () => {
      // Arrange
      const otherUser = await createTestUser({
        email: '<EMAIL>',
      });
      const otherCategory = await createTestCategory(otherUser.id);
      
      await prisma.expense.create({
        data: {
          amount: 100,
          description: 'Other user expense',
          date: new Date(),
          paymentMethod: 'cash',
          userId: otherUser.id,
          categoryId: otherCategory.id,
          tags: [],
          isRecurring: false,
        },
      });

      // Act
      const response = await request(app)
        .get('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expenses).toHaveLength(2); // Only testUser's expenses
      expect(response.body.data.expenses.every((expense: any) =>
        expense.userId === testUser.id
      )).toBe(true);
    });
  });

  describe('PUT /api/expenses/:id', () => {
    let testExpense: any;

    beforeEach(async () => {
      testExpense = await prisma.expense.create({
        data: {
          amount: 25.50,
          description: 'Original expense',
          date: new Date('2024-01-15'),
          paymentMethod: 'cash',
          userId: testUser.id,
          categoryId: testCategory.id,
          tags: [],
          isRecurring: false,
        },
      });
    });

    it('should update expense successfully', async () => {
      // Arrange
      const updateData = {
        amount: 35.75,
        description: 'Updated expense',
        paymentMethod: 'card',
      };

      // Act
      const response = await request(app)
        .put(`/api/expenses/${testExpense.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.expense).toMatchObject({
        ...updateData,
        amount: updateData.amount.toString()
      });

      // Verify in database
      const updatedExpense = await prisma.expense.findUnique({
        where: { id: testExpense.id },
      });
      expect(parseFloat(updatedExpense?.amount?.toString() || '0')).toBe(35.75);
      expect(updatedExpense?.description).toBe('Updated expense');
    });

    it('should not allow updating other user expenses', async () => {
      // Arrange
      const otherUser = await createTestUser({
        email: '<EMAIL>',
      });
      const otherToken = jwt.sign(
        { userId: otherUser.id, email: otherUser.email },
        process.env.JWT_SECRET!,
        { expiresIn: '1h' }
      );

      const updateData = {
        amount: 100,
        description: 'Unauthorized update',
      };

      // Act
      const response = await request(app)
        .put(`/api/expenses/${testExpense.id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .send(updateData)
        .expect(404);

      // Assert
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/expenses/:id', () => {
    let testExpense: any;

    beforeEach(async () => {
      testExpense = await prisma.expense.create({
        data: {
          amount: 25.50,
          description: 'To be deleted',
          date: new Date(),
          paymentMethod: 'cash',
          userId: testUser.id,
          categoryId: testCategory.id,
          tags: [],
          isRecurring: false,
        },
      });
    });

    it('should delete expense successfully', async () => {
      // Act
      const response = await request(app)
        .delete(`/api/expenses/${testExpense.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);

      // Verify deletion in database
      const deletedExpense = await prisma.expense.findUnique({
        where: { id: testExpense.id },
      });
      expect(deletedExpense).toBeNull();
    });

    it('should not allow deleting other user expenses', async () => {
      // Arrange
      const otherUser = await createTestUser({
        email: '<EMAIL>',
      });
      const otherToken = jwt.sign(
        { userId: otherUser.id, email: otherUser.email },
        process.env.JWT_SECRET!,
        { expiresIn: '1h' }
      );

      // Act
      const response = await request(app)
        .delete(`/api/expenses/${testExpense.id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(404);

      // Assert
      expect(response.body.success).toBe(false);

      // Verify expense still exists
      const expense = await prisma.expense.findUnique({
        where: { id: testExpense.id },
      });
      expect(expense).toBeTruthy();
    });
  });
});
