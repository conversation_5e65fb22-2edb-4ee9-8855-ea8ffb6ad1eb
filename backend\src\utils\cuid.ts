import { createId } from '@paralleldrive/cuid2';

// Tipuri pentru ID-uri cu prefix
export type UserId = `usr_${string}`;
export type CategoryId = `cat_${string}`;
export type ExpenseId = `exp_${string}`;
export type PlanId = `pln_${string}`;
export type SubscriptionId = `sub_${string}`;
export type UsageLogId = `log_${string}`;
export type WebhookEventId = `whk_${string}`;

export type EntityId = UserId | CategoryId | ExpenseId | PlanId | SubscriptionId | UsageLogId | WebhookEventId;

// Tipuri pentru entități
export type EntityType = 'user' | 'category' | 'expense' | 'plan' | 'subscription' | 'usageLog' | 'webhookEvent';

// Mapare între tipuri și prefixe
const ENTITY_PREFIXES: Record<EntityType, string> = {
  user: 'usr',
  category: 'cat',
  expense: 'exp',
  plan: 'pln',
  subscription: 'sub',
  usageLog: 'log',
  webhookEvent: 'whk'
};

/**
 * Generează un ID unic pentru utilizatori
 */
export function generateUserId(): UserId {
  return `usr_${createId()}` as UserId;
}

/**
 * Generează un ID unic pentru categorii
 */
export function generateCategoryId(): CategoryId {
  return `cat_${createId()}` as CategoryId;
}

/**
 * Generează un ID unic pentru cheltuieli
 */
export function generateExpenseId(): ExpenseId {
  return `exp_${createId()}` as ExpenseId;
}

/**
 * Generează un ID unic pentru planuri
 */
export function generatePlanId(): PlanId {
  return `pln_${createId()}` as PlanId;
}

/**
 * Generează un ID unic pentru abonamente
 */
export function generateSubscriptionId(): SubscriptionId {
  return `sub_${createId()}` as SubscriptionId;
}

/**
 * Generează un ID unic pentru log-uri de utilizare
 */
export function generateUsageLogId(): UsageLogId {
  return `log_${createId()}` as UsageLogId;
}

/**
 * Generează un ID unic pentru evenimente webhook
 */
export function generateWebhookEventId(): WebhookEventId {
  return `whk_${createId()}` as WebhookEventId;
}

/**
 * Generează un ID pentru orice tip de entitate
 */
export function generateEntityId(entityType: EntityType): EntityId {
  const prefix = ENTITY_PREFIXES[entityType];
  return `${prefix}_${createId()}` as EntityId;
}

/**
 * Extrage tipul de entitate dintr-un ID
 */
export function getEntityTypeFromId(id: string): EntityType | null {
  const prefix = id.split('_')[0];
  
  for (const [entityType, entityPrefix] of Object.entries(ENTITY_PREFIXES)) {
    if (entityPrefix === prefix) {
      return entityType as EntityType;
    }
  }
  
  return null;
}

/**
 * Validează dacă un string este un ID valid pentru utilizatori
 */
export function isValidUserId(id: string): id is UserId {
  return id.startsWith('usr_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru categorii
 */
export function isValidCategoryId(id: string): id is CategoryId {
  return id.startsWith('cat_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru cheltuieli
 */
export function isValidExpenseId(id: string): id is ExpenseId {
  return id.startsWith('exp_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru planuri
 */
export function isValidPlanId(id: string): id is PlanId {
  return id.startsWith('pln_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru abonamente
 */
export function isValidSubscriptionId(id: string): id is SubscriptionId {
  return id.startsWith('sub_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru log-uri
 */
export function isValidUsageLogId(id: string): id is UsageLogId {
  return id.startsWith('log_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru webhook-uri
 */
export function isValidWebhookEventId(id: string): id is WebhookEventId {
  return id.startsWith('whk_') && id.length > 4;
}

/**
 * Validează dacă un string este un ID valid pentru orice entitate
 */
export function isValidEntityId(id: string): id is EntityId {
  const entityType = getEntityTypeFromId(id);
  return entityType !== null;
}

/**
 * Convertește un ID numeric vechi într-un CUID nou
 * Folosit în timpul migrării
 */
export function convertLegacyId(oldId: number, entityType: EntityType): EntityId {
  // În timpul migrării, putem folosi o mapare deterministă
  // sau putem genera ID-uri noi și le păstrăm într-o mapare
  const prefix = ENTITY_PREFIXES[entityType];
  const paddedId = oldId.toString().padStart(8, '0');
  return `${prefix}_legacy_${paddedId}_${createId().slice(-8)}` as EntityId;
}

/**
 * Clasă pentru gestionarea mapărilor de ID-uri în timpul migrării
 */
export class IdMigrationManager {
  private mappings: Map<string, EntityId> = new Map();
  
  /**
   * Adaugă o mapare între un ID vechi și unul nou
   */
  addMapping(oldId: number, newId: EntityId, entityType: EntityType): void {
    const key = `${entityType}_${oldId}`;
    this.mappings.set(key, newId);
  }
  
  /**
   * Obține ID-ul nou pentru un ID vechi
   */
  getNewId(oldId: number, entityType: EntityType): EntityId | null {
    const key = `${entityType}_${oldId}`;
    return this.mappings.get(key) || null;
  }
  
  /**
   * Verifică dacă există o mapare pentru un ID vechi
   */
  hasMapping(oldId: number, entityType: EntityType): boolean {
    const key = `${entityType}_${oldId}`;
    return this.mappings.has(key);
  }
  
  /**
   * Obține toate mapările pentru un tip de entitate
   */
  getMappingsForType(entityType: EntityType): Array<{ oldId: number; newId: EntityId }> {
    const result: Array<{ oldId: number; newId: EntityId }> = [];
    
    for (const [key, newId] of this.mappings.entries()) {
      if (key.startsWith(`${entityType}_`)) {
        const oldId = parseInt(key.split('_')[1]);
        result.push({ oldId, newId });
      }
    }
    
    return result;
  }
  
  /**
   * Șterge toate mapările
   */
  clear(): void {
    this.mappings.clear();
  }
  
  /**
   * Exportă mapările ca JSON
   */
  export(): string {
    const obj = Object.fromEntries(this.mappings);
    return JSON.stringify(obj, null, 2);
  }
  
  /**
   * Importă mapările din JSON
   */
  import(json: string): void {
    const obj = JSON.parse(json);
    this.mappings = new Map(Object.entries(obj));
  }
}

/**
 * Instanță globală pentru gestionarea migrării
 */
export const migrationManager = new IdMigrationManager();

/**
 * Utilitare pentru debugging și logging
 */
export const CuidUtils = {
  /**
   * Afișează informații despre un ID
   */
  analyzeId(id: string): {
    isValid: boolean;
    entityType: EntityType | null;
    prefix: string;
    suffix: string;
    isLegacy: boolean;
  } {
    const parts = id.split('_');
    const prefix = parts[0];
    const isLegacy = parts[1] === 'legacy';
    const entityType = getEntityTypeFromId(id);
    
    return {
      isValid: isValidEntityId(id),
      entityType,
      prefix,
      suffix: parts.slice(1).join('_'),
      isLegacy
    };
  },
  
  /**
   * Generează statistici despre ID-urile dintr-o listă
   */
  generateStats(ids: string[]): {
    total: number;
    byType: Record<string, number>;
    valid: number;
    invalid: number;
    legacy: number;
  } {
    const stats = {
      total: ids.length,
      byType: {} as Record<string, number>,
      valid: 0,
      invalid: 0,
      legacy: 0
    };
    
    for (const id of ids) {
      const analysis = this.analyzeId(id);
      
      if (analysis.isValid) {
        stats.valid++;
        const type = analysis.entityType || 'unknown';
        stats.byType[type] = (stats.byType[type] || 0) + 1;
      } else {
        stats.invalid++;
      }
      
      if (analysis.isLegacy) {
        stats.legacy++;
      }
    }
    
    return stats;
  }
};