import { createId } from '@paralleldrive/cuid2';

// Tipuri pentru ID-uri CUID simple (compatibile cu Prisma)
export type UserId = string;
export type CategoryId = string;
export type ExpenseId = string;
export type PlanId = string;
export type SubscriptionId = string;
export type UsageLogId = string;
export type WebhookEventId = string;

export type EntityId = string;

// Tipuri pentru entități
export type EntityType = 'user' | 'category' | 'expense' | 'plan' | 'subscription' | 'usageLog' | 'webhookEvent';

/**
 * Generează un CUID simplu (compatibil cu Prisma @default(cuid()))
 */
export function generateCuid(): string {
  return createId();
}

/**
 * Generează un ID unic pentru utilizatori
 */
export function generateUserId(): UserId {
  return createId();
}

/**
 * Generează un ID unic pentru categorii
 */
export function generateCategoryId(): CategoryId {
  return createId();
}

/**
 * Generează un ID unic pentru cheltuieli
 */
export function generateExpenseId(): ExpenseId {
  return createId();
}

/**
 * Generează un ID unic pentru planuri
 */
export function generatePlanId(): PlanId {
  return createId();
}

/**
 * Generează un ID unic pentru abonamente
 */
export function generateSubscriptionId(): SubscriptionId {
  return createId();
}

/**
 * Generează un ID unic pentru log-uri de utilizare
 */
export function generateUsageLogId(): UsageLogId {
  return createId();
}

/**
 * Generează un ID unic pentru evenimente webhook
 */
export function generateWebhookEventId(): WebhookEventId {
  return createId();
}

/**
 * Generează un ID pentru orice tip de entitate
 */
export function generateEntityId(entityType: EntityType): EntityId {
  return createId();
}

/**
 * Validează dacă un string este un CUID valid
 */
export function isValidCuid(id: string): boolean {
  // CUID format: începe cu 'c' urmat de 24 de caractere alfanumerice lowercase
  return /^c[a-z0-9]{24}$/.test(id);
}

/**
 * Validează dacă un string este un ID valid pentru utilizatori
 */
export function isValidUserId(id: string): id is UserId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru categorii
 */
export function isValidCategoryId(id: string): id is CategoryId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru cheltuieli
 */
export function isValidExpenseId(id: string): id is ExpenseId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru planuri
 */
export function isValidPlanId(id: string): id is PlanId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru abonamente
 */
export function isValidSubscriptionId(id: string): id is SubscriptionId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru log-uri
 */
export function isValidUsageLogId(id: string): id is UsageLogId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru webhook-uri
 */
export function isValidWebhookEventId(id: string): id is WebhookEventId {
  return isValidCuid(id);
}

/**
 * Validează dacă un string este un ID valid pentru orice entitate
 */
export function isValidEntityId(id: string): id is EntityId {
  return isValidCuid(id);
}

/**
 * Convertește un ID numeric vechi într-un CUID nou
 * Folosit în timpul migrării (nu mai este necesar - Prisma generează CUID-uri automat)
 * @deprecated Folosește Prisma @default(cuid()) în schimb
 */
export function convertLegacyId(oldId: number, entityType: EntityType): EntityId {
  // Această funcție nu mai este necesară - Prisma generează CUID-uri automat
  return createId();
}

// Clasele de migrare nu mai sunt necesare - Prisma generează CUID-uri automat

/**
 * Utilitare pentru debugging și logging CUID-uri
 */
export const CuidUtils = {
  /**
   * Afișează informații despre un CUID
   */
  analyzeId(id: string): {
    isValid: boolean;
    isCuid: boolean;
    length: number;
    format: string;
  } {
    return {
      isValid: isValidCuid(id),
      isCuid: isValidCuid(id),
      length: id.length,
      format: isValidCuid(id) ? 'CUID' : 'Unknown'
    };
  },
  
  /**
   * Generează statistici despre CUID-urile dintr-o listă
   */
  generateStats(ids: string[]): {
    total: number;
    valid: number;
    invalid: number;
    averageLength: number;
    cuidCount: number;
  } {
    const stats = {
      total: ids.length,
      valid: 0,
      invalid: 0,
      averageLength: 0,
      cuidCount: 0
    };

    let totalLength = 0;

    for (const id of ids) {
      const analysis = this.analyzeId(id);
      totalLength += id.length;

      if (analysis.isValid) {
        stats.valid++;
      } else {
        stats.invalid++;
      }

      if (analysis.isCuid) {
        stats.cuidCount++;
      }
    }

    stats.averageLength = ids.length > 0 ? totalLength / ids.length : 0;

    return stats;
  }
};