# 🏗️ ARHITECTURA APLICAȚIEI

## 📊 OVERVIEW GENERAL

### Arhitectura de Nivel Înalt
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FRONTEND      │    │    BACKEND      │    │    DATABASE     │
│                 │    │                 │    │                 │
│ React 18        │◄──►│ Node.js/Express │◄──►│ PostgreSQL      │
│ TypeScript      │    │ TypeScript      │    │ Prisma ORM      │
│ Tailwind CSS    │    │ JWT Auth        │    │ Redis Cache     │
│ Zustand         │    │ Stripe SDK      │    │                 │
│ React Query     │    │ REST API        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    HOSTING      │    │    HOSTING      │    │   PAYMENTS      │
│                 │    │                 │    │                 │
│ Vercel/Netlify  │    │ Railway/Heroku  │    │ Stripe          │
│ CDN Assets      │    │ Docker Ready    │    │ Webhooks        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Principii Arhitecturale
1. **Separarea Responsabilităților** - Frontend/Backend/Database
2. **Modularitate** - Componente și servicii independente
3. **Scalabilitate** - Arhitectură pregătită pentru creștere
4. **Securitate** - Security-first approach
5. **Performanță** - Cache și optimizări integrate

---

## 🗄️ SCHEMA BAZEI DE DATE

### Entități Principale

#### Users (Utilizatori)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    avatar VARCHAR(255),
    role VARCHAR(20) DEFAULT 'user', -- 'user', 'admin'
    
    -- Subscription fields
    subscription_plan VARCHAR(20) DEFAULT 'free', -- 'free', 'basic', 'premium'
    stripe_customer_id VARCHAR(255) UNIQUE,
    subscription_status VARCHAR(20) DEFAULT 'inactive',
    subscription_expires_at TIMESTAMP,
    
    -- Security fields
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    refresh_token VARCHAR(255),
    last_login TIMESTAMP,
    login_count INTEGER DEFAULT 0,
    
    -- Preferences
    currency VARCHAR(3) DEFAULT 'USD',
    timezone VARCHAR(50) DEFAULT 'UTC',
    preferences JSON DEFAULT '{}',
    
    -- Audit fields
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Categories (Categorii)
```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    icon VARCHAR(50), -- emoji sau icon name
    color VARCHAR(7), -- hex color
    is_default BOOLEAN DEFAULT FALSE, -- categorii predefinite
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- NULL pentru categorii default
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Expenses (Cheltuieli)
```sql
CREATE TABLE expenses (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category_id INTEGER NOT NULL REFERENCES categories(id),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    expense_date DATE NOT NULL,
    
    -- Metadata
    tags TEXT[], -- array de tag-uri
    receipt_url VARCHAR(255), -- link către chitanță
    location VARCHAR(255), -- locația cheltuielii
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Subscriptions (Abonamente Stripe)
```sql
CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    plan_name VARCHAR(50) NOT NULL, -- 'basic', 'premium'
    status VARCHAR(20) NOT NULL, -- 'active', 'canceled', 'past_due', 'unpaid'
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Usage Tracking (Monitorizare Utilizare)
```sql
CREATE TABLE usage_tracking (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    month_year VARCHAR(7) NOT NULL, -- format: '2024-01'
    expenses_count INTEGER DEFAULT 0,
    categories_count INTEGER DEFAULT 0,
    exports_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, month_year)
);
```

### Indexuri pentru Performanță
```sql
-- Indexuri critice pentru performanță
CREATE INDEX idx_expenses_user_date ON expenses(user_id, expense_date);
CREATE INDEX idx_expenses_category ON expenses(category_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_stripe_customer ON users(stripe_customer_id);
CREATE INDEX idx_subscriptions_user ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe ON subscriptions(stripe_subscription_id);
CREATE INDEX idx_usage_tracking_user_month ON usage_tracking(user_id, month_year);

-- Indexuri pentru căutare și filtrare
CREATE INDEX idx_expenses_amount ON expenses(amount);
CREATE INDEX idx_expenses_date_range ON expenses(expense_date);
CREATE INDEX idx_categories_user ON categories(user_id);
```

---

## 🔌 ARHITECTURA API

### Structura Endpoint-urilor

#### Autentificare
```
POST   /api/auth/register     # Înregistrare utilizator nou
POST   /api/auth/login        # Autentificare
POST   /api/auth/logout       # Deconectare
POST   /api/auth/refresh      # Refresh token
GET    /api/auth/me           # Profilul utilizatorului curent
POST   /api/auth/forgot       # Resetare parolă
POST   /api/auth/reset        # Confirmare resetare parolă
```

#### Utilizatori
```
GET    /api/users/profile     # Profilul utilizatorului
PUT    /api/users/profile     # Actualizare profil
POST   /api/users/avatar      # Upload avatar
DELETE /api/users/account     # Ștergere cont
```

#### Cheltuieli
```
GET    /api/expenses          # Lista cheltuieli (cu filtrare)
POST   /api/expenses          # Adăugare cheltuială nouă
GET    /api/expenses/:id      # Detalii cheltuială
PUT    /api/expenses/:id      # Actualizare cheltuială
DELETE /api/expenses/:id      # Ștergere cheltuială
GET    /api/expenses/stats    # Statistici cheltuieli
```

#### Categorii
```
GET    /api/categories        # Lista categorii (default + personalizate)
POST   /api/categories        # Creare categorie personalizată
PUT    /api/categories/:id    # Actualizare categorie
DELETE /api/categories/:id    # Ștergere categorie
```

#### Rapoarte și Export
```
GET    /api/reports/monthly   # Raport lunar
GET    /api/reports/category  # Cheltuieli pe categorii
GET    /api/reports/trends    # Tendințe (premium)
GET    /api/export/csv        # Export CSV
GET    /api/export/pdf        # Export PDF (premium)
GET    /api/export/excel      # Export Excel (premium)
```

#### Abonamente
```
GET    /api/subscriptions/plans     # Lista planurilor
POST   /api/subscriptions/checkout  # Creare sesiune checkout
POST   /api/subscriptions/portal    # Portal client Stripe
POST   /api/subscriptions/cancel    # Anulare abonament
GET    /api/subscriptions/current   # Abonament curent
```

#### Utilizare și Limite
```
GET    /api/usage/current     # Utilizarea curentă
GET    /api/usage/limits      # Limitele planului
GET    /api/usage/stats       # Statistici utilizare
```

#### Admin (doar administratori)
```
GET    /api/admin/users       # Lista utilizatori
GET    /api/admin/stats       # Statistici generale
GET    /api/admin/revenue     # Statistici venituri
PUT    /api/admin/users/:id   # Modificare utilizator
```

### Exemple Request/Response

#### POST /api/expenses
```json
// Request
{
  "amount": 25.50,
  "description": "Prânz la restaurant",
  "category_id": "cat_123",
  "expense_date": "2024-01-15",
  "tags": ["restaurant", "business"]
}

// Response
{
  "success": true,
  "data": {
    "id": "exp_456",
    "amount": 25.50,
    "description": "Prânz la restaurant",
    "category": {
      "id": "cat_123",
      "name": "Mâncare",
      "icon": "🍽️",
      "color": "#FF6B6B"
    },
    "expense_date": "2024-01-15",
    "tags": ["restaurant", "business"],
    "created_at": "2024-01-15T14:30:00Z"
  }
}
```

---

## 🎨 ARHITECTURA FRONTEND

### Structura Componentelor

```
src/
├── components/              # Componente reutilizabile
│   ├── ui/                 # Componente UI de bază
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Card.tsx
│   │   ├── Modal.tsx
│   │   └── Loading.tsx
│   ├── layout/             # Componente de layout
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Footer.tsx
│   │   └── Layout.tsx
│   ├── auth/               # Componente autentificare
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── ProtectedRoute.tsx
│   ├── expenses/           # Componente cheltuieli
│   │   ├── ExpenseForm.tsx
│   │   ├── ExpenseList.tsx
│   │   ├── ExpenseItem.tsx
│   │   └── ExpenseFilters.tsx
│   ├── categories/         # Componente categorii
│   │   ├── CategorySelector.tsx
│   │   └── CategoryManager.tsx
│   ├── reports/            # Componente rapoarte
│   │   ├── MonthlyReport.tsx
│   │   ├── CategoryChart.tsx
│   │   └── TrendsChart.tsx
│   └── subscription/       # Componente abonamente
│       ├── PricingPlans.tsx
│       ├── SubscriptionStatus.tsx
│       └── PaymentForm.tsx
├── pages/                  # Pagini aplicației
│   ├── Landing.tsx
│   ├── Dashboard.tsx
│   ├── Expenses.tsx
│   ├── Categories.tsx
│   ├── Reports.tsx
│   ├── Profile.tsx
│   ├── Settings.tsx
│   └── admin/
│       ├── AdminDashboard.tsx
│       ├── UsersList.tsx
│       └── RevenueStats.tsx
├── hooks/                  # Custom hooks
│   ├── useAuth.ts
│   ├── useExpenses.ts
│   ├── useCategories.ts
│   ├── useSubscription.ts
│   └── useLocalStorage.ts
├── services/               # API services
│   ├── api.ts
│   ├── authService.ts
│   ├── expenseService.ts
│   ├── categoryService.ts
│   └── subscriptionService.ts
├── store/                  # State management
│   ├── authStore.ts
│   ├── expenseStore.ts
│   └── uiStore.ts
├── utils/                  # Utilitare
│   ├── formatters.ts
│   ├── validators.ts
│   ├── constants.ts
│   └── helpers.ts
├── types/                  # TypeScript types
│   ├── index.ts
│   ├── api.ts
│   └── user.ts
└── styles/                 # Stiluri globale
    ├── globals.css
    └── components.css
```

### State Management

#### Zustand Stores
```typescript
// authStore.ts - Autentificare și utilizator
interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
}

// expenseStore.ts - Cheltuieli locale
interface ExpenseState {
  expenses: Expense[];
  filters: ExpenseFilters;
  setFilters: (filters: ExpenseFilters) => void;
  addExpense: (expense: Expense) => void;
  updateExpense: (id: string, expense: Partial<Expense>) => void;
  removeExpense: (id: string) => void;
}
```

#### React Query pentru Server State
```typescript
// useExpenses.ts
export function useExpenses(params: ExpenseParams) {
  return useQuery({
    queryKey: ['expenses', params],
    queryFn: () => expenseService.getExpenses(params),
    staleTime: 2 * 60 * 1000, // 2 minute
    cacheTime: 10 * 60 * 1000, // 10 minute
  });
}

// useCreateExpense.ts
export function useCreateExpense() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: expenseService.createExpense,
    onSuccess: () => {
      queryClient.invalidateQueries(['expenses']);
      toast.success('Cheltuială adăugată cu succes!');
    },
    onError: (error) => {
      toast.error('Eroare la adăugarea cheltuielii');
    },
  });
}
```

---

## 🔒 ARHITECTURA SECURITĂȚII

### Layered Security Approach

#### 1. Network Level
- **HTTPS**: Toate comunicațiile criptate
- **CORS**: Configurare restrictivă pentru origini
- **Rate Limiting**: Protecție împotriva spam-ului
- **DDoS Protection**: Prin CDN și load balancer

#### 2. Application Level
- **JWT Authentication**: Access + refresh tokens
- **Role-based Authorization**: User/Admin permissions
- **Input Validation**: Joi schemas pentru toate input-urile
- **SQL Injection Protection**: Prisma ORM cu prepared statements
- **XSS Protection**: Sanitizare și CSP headers

#### 3. Data Level
- **Encryption at Rest**: Database encryption
- **Password Hashing**: bcrypt cu salt rounds
- **PII Protection**: Minimizarea datelor personale
- **Audit Trail**: Logging pentru toate acțiunile sensibile

### Middleware Stack
```typescript
// Ordinea middleware-urilor în Express
app.use(helmet()); // Security headers
app.use(cors(corsOptions)); // CORS configuration
app.use(compression()); // Response compression
app.use(express.json({ limit: '10mb' })); // Body parsing
app.use(mongoSanitize()); // NoSQL injection protection
app.use(xss()); // XSS protection
app.use(hpp()); // HTTP Parameter Pollution protection
app.use(rateLimiter); // Rate limiting
app.use(auditLogger); // Audit trail
app.use(authMiddleware); // Authentication
app.use(subscriptionLimits); // Usage limits
```

---

## 📊 ARHITECTURA PERFORMANȚEI

### Caching Strategy

#### Frontend Caching
```typescript
// React Query configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minute
      cacheTime: 10 * 60 * 1000, // 10 minute
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});

// Service Worker pentru cache offline
// PWA capabilities pentru mobile experience
```

#### Backend Caching
```typescript
// Redis cache layers
const cacheConfig = {
  user: { ttl: 900 }, // 15 minute
  expenses: { ttl: 300 }, // 5 minute
  categories: { ttl: 3600 }, // 1 oră
  reports: { ttl: 1800 }, // 30 minute
};

// Cache invalidation strategies
const invalidateUserCache = (userId: string) => {
  redis.del(`user:${userId}:*`);
  redis.del(`expenses:${userId}:*`);
};
```

### Database Optimization
```sql
-- Query optimization examples
EXPLAIN ANALYZE SELECT * FROM expenses 
WHERE user_id = $1 AND expense_date >= $2 
ORDER BY expense_date DESC LIMIT 50;

-- Materialized views pentru rapoarte complexe
CREATE MATERIALIZED VIEW monthly_expense_summary AS
SELECT 
  user_id,
  DATE_TRUNC('month', expense_date) as month,
  category_id,
  SUM(amount) as total_amount,
  COUNT(*) as expense_count
FROM expenses
GROUP BY user_id, month, category_id;
```

---

## 🚀 SCALABILITY CONSIDERATIONS

### Horizontal Scaling
- **Load Balancing**: Multiple backend instances
- **Database Sharding**: User-based partitioning
- **CDN**: Static assets distribution
- **Microservices**: Service decomposition pentru features complexe

### Vertical Scaling
- **Database Optimization**: Indexuri și query tuning
- **Memory Management**: Efficient caching strategies
- **CPU Optimization**: Async processing și worker threads

### Monitoring și Observability
```typescript
// Performance monitoring
const performanceMetrics = {
  apiResponseTime: '< 200ms (95th percentile)',
  databaseQueryTime: '< 100ms average',
  pageLoadTime: '< 2 secunde',
  uptime: '99.9%',
  errorRate: '< 0.1%',
};

// Health checks
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'connected',
    redis: 'connected',
  });
});
```

---

*Ultima actualizare: Ianuarie 2025*